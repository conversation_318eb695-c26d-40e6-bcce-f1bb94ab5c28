import { useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { ToastContainer } from 'react-toastify';

import Router from './routes';
import ThemeProvider from './theme';
import ScrollToTop from './components/ScrollToTop';
import { BaseOptionChartStyle } from './components/chart/BaseOptionChart';

import './assets/css/style.css';
import { get } from './api';

export default function App() {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const loadingRef = useRef(null);

  const loadServices = async () => {
    try {
      setLoading(true);
      const thePayments  = await get('accounts/getPaymentMethods');
      if(thePayments === undefined){
        localStorage.clear();
      }
    } catch (error) {
      localStorage.clear();
    } finally {
      setLoading(false);
    }
  };

  // Handle unauthorized event
  useEffect(() => {
    loadServices();
  }, [navigate]);

  return (
    <ThemeProvider>
      <ToastContainer />
      <ScrollToTop />
      <BaseOptionChartStyle />
      <Router />
    </ThemeProvider>
  );
}
