//

import Card from './Card';
import Paper from './Paper';
import Input from './Input';
import Button from './Button';
import Tooltip from './Tooltip';
import Backdrop from './Backdrop';
import Typography from './Typography';
import CssBaseline from './CssBaseline';
import Autocomplete from './Autocomplete';

// ----------------------------------------------------------------------

export default function ComponentsOverrides(theme) {
  return Object.assign(
    Card(theme),
    Input(theme),
    <PERSON>(theme),
    <PERSON><PERSON>(theme),
    <PERSON><PERSON><PERSON>(theme),
    <PERSON>dr<PERSON>(theme),
    Typography(theme),
    <PERSON>ss<PERSON><PERSON><PERSON>(theme),
    Autocomplete(theme)
  );
}
