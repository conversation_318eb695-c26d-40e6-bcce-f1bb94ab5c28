// PrivacyPolicy.module.scss

.privacyWrapper {
    min-height: 100vh;
    background-color: #f9fafb;
  }
  
  .header {
    background-color: #fff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    padding: 1.5rem 1rem;
  
    .headerContent {
      max-width: 64rem;
      margin: 0 auto;
      display: flex;
      align-items: center;
      gap: 0.75rem;
  
      h1 {
        font-size: 1.5rem;
        font-weight: 700;
        color: #111827;
      }
  
      .icon {
        width: 2rem;
        height: 2rem;
        color: #2563eb;
      }
    }
  }
  
  .mainContent {
    max-width: 64rem;
    margin: 0 auto;
    padding: 2rem 1rem;
  
    .card {
      background: #fff;
      border-radius: 0.5rem;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
      padding: 2rem;
  
      h2 {
        font-size: 1.25rem;
        font-weight: 600;
        color: #111827;
        margin-bottom: 1rem;
      }
  
      h3 {
        font-weight: 500;
        color: #111827;
        margin-bottom: 0.5rem;
      }
  
      p, li {
        color: #4b5563;
        line-height: 1.6;
        margin-bottom: 1rem;
      }
  
      ul {
        list-style-type: disc;
        padding-left: 1.5rem;
  
        li {
          margin-bottom: 0.25rem;
        }
      }
  
      a {
        color: #2563eb;
        text-decoration: none;
  
        &:hover {
          text-decoration: underline;
        }
      }
  
      section {
        margin-bottom: 2rem;
      }
    }
  }
  
  .footer {
    background-color: #f9fafb;
    border-top: 1px solid #e5e7eb;
    padding: 1.5rem 1rem;
  
    .footerText {
      max-width: 64rem;
      margin: 0 auto;
      text-align: center;
      font-size: 0.875rem;
      color: #6b7280;
    }
  }
  