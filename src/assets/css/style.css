@import url("https://fonts.googleapis.com/css2?family=Archivo:wght@400;500&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap");

:root {
  --primary-main: #1F50C2;
  --primary-dark: #0F2B77;
  --primary-light: #5A8DEE;
  --secondary-main: #00A3FF;
  --dark-bg: #0C1323;
  --gray-bg: #FAFAFB;
  --border-radius-sm: 8px;
  --border-radius-md: 12px;
  --border-radius-lg: 16px;
  --box-shadow: 0 8px 20px rgba(31, 80, 194, 0.12);
  --box-shadow-hover: 0 16px 32px rgba(31, 80, 194, 0.16);
  --transition: all 0.3s ease;
}

body {
  background-color: var(--gray-bg) !important;
  margin: 0;
  line-height: 1.6;
  font-family: 'Inter', sans-serif;
  color: #212B36;
  overflow-x: hidden;
}

/* Global styles */
.container {
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 24px;
}

/* Modern buttons */
.btn-primary {
  background: var(--primary-main) !important;
  color: white !important;
  font-weight: 600 !important;
  border-radius: var(--border-radius-sm) !important;
  padding: 12px 24px !important;
  box-shadow: var(--box-shadow);
  transition: var(--transition);
  text-transform: none !important;
  border: none !important;
}

.btn-primary:hover {
  background: var(--primary-dark) !important;
  box-shadow: var(--box-shadow-hover);
  transform: translateY(-2px);
}

.btn-secondary {
  background: white !important;
  color: var(--primary-main) !important;
  font-weight: 600 !important;
  border: 2px solid var(--primary-main) !important;
  border-radius: var(--border-radius-sm) !important;
  padding: 10px 22px !important;
  transition: var(--transition);
  text-transform: none !important;
}

.btn-secondary:hover {
  background: rgba(31, 80, 194, 0.05) !important;
  transform: translateY(-2px);
}

/* Header/Navbar styles */
.navbar {
  background: white !important;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06) !important;
  padding: 16px 0 !important;
  position: fixed !important;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000 !important;
}

.navbar-container {
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
}

.navbar-logo {
  height: 50px !important;
}

.navbar-links {
  display: flex !important;
  gap: 32px !important;
}

.navbar-link {
  color: #212B36 !important;
  font-weight: 500 !important;
  text-decoration: none !important;
  position: relative !important;
  padding: 6px 0 !important;
  transition: var(--transition) !important;
}

.navbar-link:hover {
  color: var(--primary-main) !important;
}

.navbar-link:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--primary-main);
  transition: var(--transition);
}

.navbar-link:hover:after {
  width: 100%;
}

.navbar-buttons {
  display: flex !important;
  gap: 16px !important;
}

.mobile-menu-button {
  display: none !important;
}

/* Card styles */
.card-modern {
  background: white !important;
  border-radius: var(--border-radius-md) !important;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08) !important;
  transition: var(--transition) !important;
  overflow: hidden !important;
  border: none !important;
}

.card-modern:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12) !important;
  transform: translateY(-4px) !important;
}

/* Form styles */
.form-container {
  background: white;
  border-radius: var(--border-radius-md);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  padding: 32px;
  max-width: 560px;
  width: 100%;
  margin: 0 auto;
}

.form-title {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 24px;
  color: var(--primary-dark);
}

.form-select {
  width: 100%;
  padding: 12px 16px;
  border-radius: var(--border-radius-sm);
  border: 1px solid #DFE3E8;
  background-color: white;
  font-size: 16px;
  transition: var(--transition);
}

.form-select:focus {
  border-color: var(--primary-light);
  box-shadow: 0 0 0 3px rgba(31, 80, 194, 0.1);
}

.form-input {
  width: 100%;
  padding: 12px 16px;
  border-radius: var(--border-radius-sm);
  border: 1px solid #DFE3E8;
  background-color: white;
  font-size: 16px;
  transition: var(--transition);
}

.form-input:focus {
  border-color: var(--primary-light);
  box-shadow: 0 0 0 3px rgba(31, 80, 194, 0.1);
}

/* Provider card styles */
.provider-card {
  border: 1px solid #DFE3E8 !important;
  border-radius: var(--border-radius-sm) !important;
  padding: 16px !important;
  cursor: pointer !important;
  transition: var(--transition) !important;
  background: white !important;
}

.provider-card:hover {
  border-color: var(--primary-light) !important;
  box-shadow: 0 4px 16px rgba(31, 80, 194, 0.12) !important;
  transform: translateY(-2px) !important;
}

.provider-card.active {
  border: 2px solid var(--primary-main) !important;
  background: rgba(31, 80, 194, 0.05) !important;
}

/* Hero section */
.hero-section {
  background: var(--dark-bg);
  padding: 120px 0 80px;
  color: white;
  position: relative;
  overflow: hidden;
}

.hero-content {
  position: relative;
  z-index: 1;
  text-align: center;
}

.hero-title {
  font-size: 48px;
  font-weight: 700;
  margin-bottom: 24px;
  line-height: 1.2;
}

.hero-subtitle {
  font-size: 20px;
  font-weight: 400;
  margin-bottom: 40px;
  color: rgba(255, 255, 255, 0.8);
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
}

.hero-cta {
  padding: 14px 32px;
  font-size: 18px;
}

.hero-graphic {
  position: absolute;
  bottom: -100px;
  right: -80px;
  width: 600px;
  opacity: 0.1;
}

/* Responsive styles */
@media (max-width: 992px) {
  .hero-title {
    font-size: 36px;
  }
  
  .hero-subtitle {
    font-size: 18px;
  }
  
  .form-container {
    padding: 24px;
  }
}

@media (max-width: 768px) {
  .navbar-links {
    display: none !important;
  }
  
  .mobile-menu-button {
    display: block !important;
  }
  
  .hero-section {
    padding: 100px 0 60px;
  }
  
  .hero-title {
    font-size: 32px;
  }
  
  .form-container {
    max-width: 100%;
  }
  
  .main-model-block {
    width: 90% !important;
    padding: 16px !important;
    margin: 0 auto !important;
  }
}

@media (max-width: 576px) {
  .hero-title {
    font-size: 28px;
  }
  
  .hero-subtitle {
    font-size: 16px;
  }
  
  .navbar-buttons {
    gap: 8px !important;
  }
  
  .btn-primary, .btn-secondary {
    padding: 10px 16px !important;
    font-size: 14px !important;
  }
}

.fullWidth{
  width: 100% !important;
}

.main-model-block{
  margin: 0 auto !important;
  width:  38%;
  border-radius: 4px;
  background: #ffffff;
  margin-top: 20px !important;
  padding: 15px 20px;
  max-height: 95vh;
}


@media(max-width: 760px){
    .main-model-block{
      margin: 0 auto !important;
      width:  65%;
      border-radius: 4px;
      background: #ffffff;
      padding: 15px 20px;
    }
}

.item-form-top-101{
  margin-top: 15px !important;
}


.hidden{
  display: none;
  visibility: hidden;
}


.default-tab{
  color: #000000;
  padding: 5px 20px !important;
  margin-right: 5px !important;
  display: inline-block !important;
}

.active-tab{

  color: #2065D1 !important; 
  background: #c8daf4 !important;
  padding: 5px 20px !important;
  margin-right: 5px !important;
  display: inline-block !important;
}

.tabing-section{
  padding-bottom: 15px;
}


.model-container-blocking{

  max-height:    70vh;
  overflow:      hidden;
  overflow-y:    auto;
  padding:       0 15px;
}


.text-center{
  text-align: center !important;
  text-transform: uppercase;
}


.full-width{
  width: 100% !important;

}

.mt-3{
  margin-top: 8px !important;
}

.mt-25{
  margin-top: 25px !important; 
}

.made-payment-btn{
  padding-top: 40px !important; 
}

.made-payment-btn button{
  padding: 15px 20px;
  cursor: pointer;
  border-radius: 8px;
  -webkit-transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, box-shadow 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, border-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, box-shadow 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, border-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms, color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  color: #fff;
  background-color: #2065D1;
  text-transform: none;
  font-size: 16px;
  box-shadow: 0px 3px 1px -2px rgba(145, 158, 171, 0.2), 0px 2px 2px 0px rgba(145, 158, 171, 0.14), 0px 1px 5px 0px rgba(145, 158, 171, 0.12);
  box-shadow: 0 8px 16px 0 rgba(32, 101, 209, 0.24);
  outline: none;
  border: 0;
}

.reload-body{
  padding: 10px;
}

.reload-body reloading-loading-btn{
  border:     2px solid red;
  padding:    0;
  width:      20px;
}


.phone-field{
  border: 1px solid #CACACA;
  border-radius: 5px;
  input{
    border: 0;
    box-shadow: 0;
  }
}

.react-tel-input .flag-dropdown {
  position: absolute;
  top: 0;
  bottom: 0;
  padding: 6px 4px;
  background-color: #f5f5f5;
  border: 1px solid #cacaca;
  border-radius: 3px 0 0 3px;
}

.react-tel-input .form-control,
.react-tel-input .form-control:focus,
.react-tel-input .form-control:active,
.react-tel-input .form-control:visited
 {
    font-size: 14px;
    letter-spacing: .01rem;
    margin-top: 0 !important;
    margin-bottom: 0 !important;
    padding-left: 74px;
    margin-left: 0;
    background: #FFFFFF !important;
    border: 0 solid #CACACA !important;
    border-radius: 7px;
    line-height: 25px;
    height: 50px;
    width: 100% !important;
    outline: none !important;

  }


  .react-tel-input .special-label {
    position: absolute;
    z-index: 1;
    top: -7px;
    left: 25px;
    display: block;
    background: white;
    padding: 0 5px;
    font-size: 13px;
    white-space: nowrap;
    display: none !important;
    visibility: hidden;
}

.pull-right{
  float: right !important;
  right: 0;
  width: 100%;
  text-align: right;
}


.payment-detail-pop{
  display:  flex;
  justify-content: space-between;
  padding:  8px 0;
  min-width:  400px;
}


@media(max-width: 760px){
  .payment-detail-pop{
    min-width:  310px;
  }
}

.pop-title{
  display:  flex;
  justify-content: space-between;
}


.form-block{
  padding: 0 0 4px 0;

  .form-block-title{
    display: block;
    font-weight: 600;
  }
}

.main-swap-block{
  border: 0 solid #fff; 
  Grid{
    padding: 0;
  }
  .main-swap-block-input, 
  .main-swap-block-input:focus, 
  .main-swap-block-input:active{
    border: 0 solid #fff !important;
     input {
       border: 0 solid #fff !important;
     }
  }
}
.main-swap-block-currency{

  button{
    border: 2px solid #f1f1f1; 
    float: right;
    padding: 12px 15px;
  }
}


.currency-listing{
  width: 34%;
  margin: 0 auto;
}

@media(max-width: 640px){
  .currency-listing{
    width: 90%;
    margin: 0 auto;
  }
}

.currency-item_flex{
  display: flex;
  padding: 10px 10px;
  cursor: pointer;
  img{
    margin-right: 8px;
    margin-top:   -3px;
    height:       30px;
  }

  .flags-extra{
    height:        25px;
    width:         25px;
    margin-top:    -1px;
    border-radius: 100%;
  }
}




.currency-item_flex:hover{
  background: #f1f1f1;
}


.full-width{
  width: 100%;
  font-size: 12px;
  clear: both;
  text-align: right;
}

.clearfix{
  clear: both !important;
  width: 100% !important;
  display: block;
}

.provider-blocking{
  border:  1px solid #f1f1f1;
  padding: 0 0 5px 0;
  border-radius: 5px;

  h6{
    background:  #f1f1f1;
    padding: 5px 10px;
  }

  .provider-title{
    display:         flex !important;
    justify-content: space-between;

    b{
      padding-top:    4.5px;
      padding-bottom: 5px;
      height:         40px;
    }
  }
}

.provider-blocking:hover{
  border:  1px solid #2065D1;
  cursor:  pointer;
}


.provider-blocking-active{
  border:  1px solid rgb(32, 101, 209);
  cursor:  pointer;
  h6{
    background:  rgb(32, 101, 209, 0.2);
    padding: 5px 10px;
  }
}

.space-btn-text{
  display:         flex !important;
  justify-content: space-between;
  padding:         5px 0;
  font-size:       14px;
  padding:         5px 10px;
}

.avaliable-provers_{
  display:   block !important;
  padding:   3px 0;
  margin:  0 0 -10px 0;
  font-size: 14px;
}


.filtering-section{
  width: 200px;
  padding-top:    0 !important;
  padding-bottom: 15px;

  .form-block-title{
    margin-top: 2px !important;
  }
}

.cancel-btn{
  color: #f10000;
}

.margin-left-5{
  margin-left: 10px;
}

.account_option{
  display: flex;
  justify-content: space-around;
  padding: 0;

  button{ 
    width:   48%;
    padding: 15px 15px;
    border:  1px solid rgba(0,0,0,0.34);
    color: #000000;
    font-weight: 600;
    font-size: 16px;
    cursor: pointer;
    border-radius: 9px;
  }

  button.active{ 
    width:   50%;
    padding: 10px 15px;
    border:  1px;
    color: #ffffff;
    background: #4f87df; 
  }
}

.error_texting{
   font-size: 12px;
   margin-top: 3px !important;  
   display: block;
   position: relative;
   padding-left: 17px;
   padding-right: 17px; 
}


.selected-currency{
  border:  1px solid #888888;
  background: rgba(0,0,0,0.05);
  padding: 7px 0 7px 10px;
  border-radius: 5px;
  margin:  0 7px 8px 0;
  font-size: 13px;
  cursor: pointer;
  font-weight: 500;
}

.selected-currency-items{
  padding: 15px 7px;
  border: 1px solid  rgba(0,0,0,0.2);
  border-radius: 10px;
}


.selected-currency-items_1{
  margin: 0;
}


.selected-currency-items_2{
  padding-top: 4px !important; 
}


.full-width-settings{
  padding: 10px 0 20px 0 !important;
  display: block;
  width: 100% !important;
}

.margin-left-0{
  margin-left:  0 !important;
}

.padding-left-right-20{
  padding-left: 25px;
  padding-right: 25px;
  padding-bottom: 25px;
}

.profile-texting{
  width: 100%;
  justify-content: center;
  font-size: 16px;

  b{
    width: 150px;
    display: inline-block;
    
  }
}


.red-error-text{
  color: #f10000;
}
/* 
.case-study-subTitle{
  border: 1p olid red;
}

.case-study-subTitle{
  border: 1px solid red;
} */

.lower-background{
  background: #F3F3F5;
}


.service-main-box{
  min-height: 310px;

  .service-main-image{
    max-width:  100%;
  }
  .service-main-content{
    padding: 0 0.1%;
  }
}


.casestudy-main-box{
  min-height: 280px;
}


@media(max-width: 640px){
  .casestudy-main-box{
    min-height: auto;
  }

  .service-main-box{

    min-height: auto;
    .service-main-image{
      max-width:  100%;
    }
    .service-main-content{
      padding: 0 5%;
    }
  }
}

.border-red{
  border: 1px solid red;
}

.default-casestudy-btn{
  border: 1px solid rgba(35, 187, 217, 0.7);
  margin: 4px 6px;
  border-radius: 20px;
  padding: 10px 15px;
  font-size: 16px;
  cursor: pointer;
}


.default-casestudy-btn-active{
  cursor: pointer;
  border: 1px solid rgba(35, 187, 217, 0.7);
  background: rgba(35, 187, 217, 0.8);
}

.main-widget-body{
  box-shadow: 0 0 0 rgba(0,0,0,0.1);

  div{
    box-shadow: 0 0 0 rgba(0,0,0,0.1);
  }
}


.privacy_container{
        max-width: 1200px;
        margin: 0 auto;
        padding: 2rem 1rem;
        font-size: 18px !important;


      ol, ul{
        padding: 5px 0 3px 30px;

        li{
          line-height:  26px;
          padding-bottom: 8px;
        }
      }

      .policy-wrapper {
        max-width: 800px;
        margin: 0 auto;
      }

      /* Header styles */                                       
      .policy-header {
        margin-bottom: 2rem;
      }

      .policy-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
      }

      .policy-date {
        color: #666;
      }

      /* Table of contents */
      .toc-container {
        margin-bottom: 2.5rem;
        padding: 1.5rem;
        background-color: #f9f9f9;
        border-radius: 0.5rem;
      }

      .toc-title {
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: 1rem;
      }

      .toc-list {
        list-style: none;
        padding: 0;
      }

      .toc-item {
        margin-bottom: 0.5rem;
      }

      .toc-link {
        display: flex;
        align-items: center;
        color: #333;
        text-decoration: none;
      }

      .toc-link:hover {
        text-decoration: underline;
      }

      .toc-icon {
        margin-right: 0.5rem;
      }

      /* Content sections */
      .policy-content {
        line-height: 1.6;
      }

      .policy-section {
        margin-bottom: 2.5rem;
      }

      .section-title {
        font-size: 1.75rem;
        font-weight: 700;
        margin-bottom: 1rem;
      }

      .subsection-title {
        font-size: 1.25rem;
        font-weight: 600;
        margin-top: 1.5rem;
        margin-bottom: 0.75rem;
      }

      .section-text {
        margin-bottom: 1rem;
      }

      /* Lists */
      .list {
        padding-left: 1.5rem;
        margin-bottom: 1rem;
      }

      .list-item {
        margin-bottom: 0.25rem;
      }

      .spaced-list {
        padding-left: 1.5rem;
        margin-bottom: 1rem;
      }

      .spaced-list-item {
        margin-bottom: 0.75rem;
      }

      /* Links */
      .link {
        color: #0066cc;
        text-decoration: none;
      }

      .link:hover {
        text-decoration: underline;
      }

      /* Responsive adjustments */
      @media (min-width: 768px) {
        .container {
          padding: 3rem 1.5rem;
        }

        .policy-title {
          font-size: 3rem;
        }

        .section-title {
          font-size: 2rem;
        }
      }

}


.main-header{
  background: #0c1323;
}


.faq-answer-block{
  
  padding: 0 10px 10px 1px;
  border-radius: 15px;
  margin-bottom: 15px;
  font-family: Public Sans, sans-serif;
  
  .faq-answer-title{
    width: 100%;
    text-align: left;
    padding: 15px 20px;
    font-size: 18px;
    line-height: 26px;
    cursor: pointer;
    background: #F3F3F5;
    border: 0px !important;
    border-radius: 15px 15px 0 0;
    font-weight: 600;
    font-family: Public Sans, sans-serif;

    @media(max-width: 678px){
      padding: 10px 10px;
      font-size: 16px;
      line-height: 18px;
    }

    display: flex;
    justify-content: space-between;


    .toggle-btn{
      height:40px;
      width: 40px;
      margin-top: 1px;
      background: rgba(12, 19, 35, 0.07);
    }
  }

  .faq-answer{
     padding: 20px;
     background: rgba(243, 243, 245, 0);
     border-radius:  0 0 15px 15px;
     border: 1px solid rgba(12, 19, 35, 0.08);
     border-top: 1px solid   rgba(243, 243, 245, 1);
     font-family: Public Sans, sans-serif;
     
     ol, ul{
      margin-left: 20px;
      font-family: Public Sans, sans-serif;
     }

     @media(max-width: 678px){
       font-size: 16px;
       line-height: 18px;
     }
  }
}


.toggle-option-outer{
  display: block;
  margin:  4px 15px 10px 0;


  .toggle-option{
    background-color: rgba(243, 243, 245, 1);
    padding: 12px 4px;
    display: inline;
    button{
      display: inline;
      padding: 9px 18px;
      text-align: center;
      background-color: rgba(255, 255, 255, 1);
      cursor: pointer;
      outline: none;
      box-shadow: hidden;
      border: 0;
    }
    button.active-select{
      background-color: #1F50C2;
      color: #ffffff;
      display: inline;
      padding: 6px 13px;
      text-align: center;
      cursor: pointer;
    }
    

 }
}


.label-text{
  color: #637381;
  font-size: 0.818em;
  font-family: Public Sans,sans-serif;
  font-weight: 400;
  padding:0;
  white-space:nowrap;
  overflow:hidden;
  text-overflow: ellipsis;
  left: 0;
  pointer-events: auto;
  -webkit-user-select:none;
  -moz-user-select:none;
  -ms-user-select: none;
  user-select: none;
}



.pending-quote-block{
  border: 1px solid rgba(0,0,0,0.2);
  border-radius: 10px;
  padding: 10px 10px 15px 10px;
}

.pending-quote-block-inner{
  padding: 0 0 30px 0;
}

.content-pending-quote-title{
  strong{
    font-weight: 700;
    font-size:   16px;
    display:     inline-block;
    width:       140px;
  }

  span{
      font-weight: 400;
      font-size: 16px;
  }
}

.content-pending-quote{

   .block-pending-quote{
      float: left;
      width: 50%;
      margin-bottom: 10px;
   } 

   @media(max-width: 678px){
    .block-pending-quote{
      float: nonw;
      width: 100%;
   } 
  }

   p{
      font-weight: 400;
      font-size: 16px;

      strong{
        font-weight: 700;
        font-size:   16px;
     }
   }
}


.pending-quote-footer{
  width: 100%;
  clear: both;

  button{
          float: right;
          display: -webkit-inline-box;
          display: -webkit-inline-flex;
          display: -ms-inline-flexbox;
          display: inline-flex;
          -webkit-align-items:
        center;
          -webkit-box-align:
        center;
          -ms-flex-align: center;
          align-items: center;
          -webkit-box-pack:
        center;
          -ms-flex-pack: center;
          -webkit-justify-content:
        center;
          justify-content: center;
          position: relative;
          box-sizing: border-box;
          -webkit-tap-highlight-color: transparent;
          background-color: transparent;
          outline:
        0;
          border:
        0;
          margin:
        0;
          border-radius:
        0;
          padding:
        0;
          cursor: pointer;
          -webkit-user-select:
        none;
          -moz-user-select:
        none;
          -ms-user-select: none;
          user-select: none;
          vertical-align: middle;
          -moz-appearance:
        none;
          -webkit-appearance:
        none;
          -webkit-text-decoration: none;
          text-decoration:
        none;
          color: inherit;
          font-weight: 700;
          line-height: 1.7142857142857142;
          font-size: 0.875rem;
          text-transform: capitalize;
          font-family: Public Sans,sans-serif;
          min-width: 64px;
          padding:
        5px 15px;
          border-radius:
        8px;
          -webkit-transition:
        background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms,box-shadow 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms,border-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms,color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
          transition:
        background-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms,box-shadow 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms,border-color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms,color 250ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
          border:
        1px solid rgba(255, 72, 66, 0.5);
          color: #FF4842;

        }
}

.pt-2{
  padding-top: 15px;
  font-size: 16px;
}