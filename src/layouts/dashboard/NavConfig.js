// component
import Iconify from '../../components/Iconify';

// ----------------------------------------------------------------------

const getIcon = (name) => <Iconify icon={name} width={22} height={22} />;

const navConfig = [
  
  {
    title: 'Dashboard',
    path: '/dashboard/main',
    icon: getIcon('eva:pie-chart-2-fill'),
  },
  {
    title: 'Send Money',
    path: '/dashboard/payments',
    icon: getIcon('eva:shopping-bag-fill'),
  },
  {
    title: 'Transactions',
    path: '/dashboard/transactions',
    icon: getIcon('eva:file-text-fill'),
  },
  {
    title: 'Settings',
    path: '/dashboard/settings',
    icon: getIcon('eva:lock-fill'),
  },
  {
    title: 'API Documentation',
    path: '/dashboard/api/documentation',
    icon: getIcon('eva:link-fill'),
  },
  {
    title: 'Profile',
    path: '/dashboard/kyc',
    icon: getIcon('eva:person-fill'),
  }
  // {
  //   title: 'Support',
  //   path: '/dashboard/support',
  //   icon: getIcon('eva:info-fill'),
  // }
];

export default navConfig;
