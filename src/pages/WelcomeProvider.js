import React from 'react';
import { Link as RouterLink, useNavigate } from 'react-router-dom';
// @mui
import {
  styled,
  Container,
  Typography,
  Box,
  Button,
  Paper,
  Grid,
  Card,
  CardContent,
  CardActions,
  Divider
} from '@mui/material';
import { 
  BookOpen, 
  FileText, 
  Server, 
  RefreshCw, 
  ExternalLink, 
  LogIn, 
  CheckCircle 
} from 'lucide-react';
// components
import Page from '../components/Page';
import Logo from '../components/Logo';

// ----------------------------------------------------------------------

const RootStyle = styled('div')(({ theme }) => ({
  minHeight: '100vh',
  display: 'flex',
  backgroundColor: '#F8F9FA',
}));

// ----------------------------------------------------------------------

export default function WelcomeProvider() {
  const navigate = useNavigate();

  const handleLoginRedirect = () => {
    navigate('/login');
  };

  const resourceCards = [
    {
      title: 'Provider Onboarding Documents',
      description: 'Essential documentation to help you get started as a provider on Liquidity Rail.',
      icon: <FileText size={40} color="#1F50C2" />,
      action: 'Download Documents',
      link: 'https://docs.liquidityrail.com/provider-onboarding',
    },
    {
      title: 'Integration Guide',
      description: 'Step-by-step guide on how to integrate with our platform.',
      icon: <BookOpen size={40} color="#1F50C2" />,
      action: 'View Guide',
      link: 'https://docs.liquidityrail.com/integration-guide',
    },
    {
      title: 'Bridger Server Setup',
      description: 'Instructions on how to set up and run our bridger server for seamless operations.',
      icon: <Server size={40} color="#1F50C2" />,
      action: 'Server Documentation',
      link: 'https://docs.liquidityrail.com/bridger-server',
    },
    {
      title: 'Rates Endpoint Configuration',
      description: 'Documentation on configuring and connecting to our rates endpoint.',
      icon: <RefreshCw size={40} color="#1F50C2" />,
      action: 'Configuration Guide',
      link: 'https://docs.liquidityrail.com/rates-endpoint',
    },
  ];

  return (
    <Page title="Welcome to Liquidity Rail">
      <RootStyle>
        <Container maxWidth="md" sx={{ py: 8 }}>
          <Paper sx={{ 
            width: '100%',
            mx: 'auto', 
            overflow: 'hidden',
            borderRadius: 3,
            boxShadow: '0 10px 40px rgba(0,0,0,0.1)',
            bgcolor: 'white',
            p: { xs: 3, md: 4 },
          }}>
            <Box sx={{ mb: 4, textAlign: 'center' }}>
              <Box sx={{ display: 'flex', justifyContent: 'center', mb: 3 }}>
                <Logo />
              </Box>
              <Typography variant="h4" gutterBottom fontWeight="700" color="#1F50C2">
                Welcome to Liquidity Rail!
              </Typography>
              <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
                Your provider account has been created successfully.
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 2 }}>
                <CheckCircle color="#4CAF50" size={20} style={{ marginRight: 8 }} />
                <Typography variant="body2" color="#4CAF50">
                  Email verification completed
                </Typography>
              </Box>
            </Box>

            <Box sx={{ mb: 4 }}>
              <Typography variant="h5" gutterBottom fontWeight="600">
                Get Started with Liquidity Rail
              </Typography>
              <Typography variant="body2" color="text.secondary" paragraph>
                Before you can begin providing services on our platform, please review our documentation and complete the required setup steps.
              </Typography>
              
              <Grid container spacing={3} sx={{ mt: 2 }}>
                {resourceCards.map((card, index) => (
                  <Grid item xs={12} md={6} key={index}>
                    <Card variant="outlined" sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                      <CardContent sx={{ flexGrow: 1 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                          {card.icon}
                          <Typography variant="h6" fontWeight="600" sx={{ ml: 1.5 }}>
                            {card.title}
                          </Typography>
                        </Box>
                        <Typography variant="body2" color="text.secondary">
                          {card.description}
                        </Typography>
                      </CardContent>
                      <CardActions>
                        <Button 
                          variant="outlined" 
                          color="primary" 
                          component="a" 
                          href={card.link} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          endIcon={<ExternalLink size={16} />}
                          sx={{ borderRadius: '8px' }}
                        >
                          {card.action}
                        </Button>
                      </CardActions>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </Box>

            <Divider sx={{ my: 4 }} />

            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h6" gutterBottom>
                Ready to access your dashboard?
              </Typography>
              <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2, mt: 2 }}>
                <Button
                  variant="contained"
                  color="primary"
                  size="large"
                  startIcon={<LogIn size={20} />}
                  onClick={handleLoginRedirect}
                  sx={{ 
                    py: 1, 
                    px: 3,
                    backgroundColor: '#1F50C2', 
                    '&:hover': { backgroundColor: '#0F2B77' },
                    borderRadius: '8px',
                    fontWeight: 600 
                  }}
                >
                  Log In to Dashboard
                </Button>
                <Button
                  variant="outlined"
                  component="a"
                  size="large"
                  href="https://docs.liquidityrail.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  endIcon={<ExternalLink size={16} />}
                  sx={{ 
                    py: 1, 
                    px: 3,
                    borderRadius: '8px',
                    fontWeight: 600 
                  }}
                >
                  Developer Documentation
                </Button>
              </Box>
            </Box>
          </Paper>
        </Container>
      </RootStyle>
    </Page>
  );
} 