import React from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import { 
  TextField, 
  Grid, 
  Typography, 
  FormControl, 
  InputLabel, 
  Select, 
  MenuItem, 
  Box,
  Paper,
  InputAdornment
} from '@mui/material';

const AmountCurrencyInput = ({
  label,
  amountName,
  currencyName,
  amountError,
  currencyError,
  currencyOptions = [],
  readOnly = false,
  onAmountChange,
  selectedCurrency
}) => {
  const { control, watch } = useFormContext();
  const watchedCurrency = watch(currencyName);
  
  // Find the selected currency object
  const currencyObject = currencyOptions.find(c => c.value === watchedCurrency);

  return (
    <Box sx={{ mb: 1 }}>
      <Typography 
        variant="subtitle1" 
        sx={{ 
          mb: 1, 
          fontWeight: 600,
          color: 'text.primary' 
        }}
      >
        {label}
      </Typography>
      <Paper
        elevation={0}
        sx={{
          display: 'flex',
          flexDirection: { xs: 'column', sm: 'row' },
          borderRadius: 'var(--border-radius-sm)',
          border: '1px solid #DFE3E8',
          overflow: 'hidden',
          transition: 'var(--transition)',
          '&:hover': {
            borderColor: 'var(--primary-light)',
          },
          '&:focus-within': {
            borderColor: 'var(--primary-main)',
            boxShadow: '0 0 0 3px rgba(31, 80, 194, 0.1)',
          },
        }}
      >
        {/* Amount Input */}
        <Box 
          sx={{ 
            flex: 1,
            p: 0,
          }}
        >
          <Controller
            name={amountName}
            control={control}
            render={({ field }) => (
              <TextField
                {...field}
                placeholder="0.00"
                type="number"
                fullWidth
                onChange={(e) => {
                  field.onChange(e);
                  if (onAmountChange) onAmountChange(e.target.value);
                }}
                disabled={readOnly}
                InputProps={{
                  disableUnderline: true,
                  sx: {
                    height: '100%',
                    fontSize: '1.1rem',
                    fontWeight: 500,
                    p: 0,
                    '& .MuiOutlinedInput-notchedOutline': { border: 'none' },
                    '& .MuiInputBase-input': { 
                      p: 2,
                      '&:disabled': {
                        WebkitTextFillColor: '#212B36',
                        color: '#212B36',
                      }
                    },
                  }
                }}
                variant="outlined"
              />
            )}
          />
        </Box>
        
        {/* Currency Select */}
        <Box
          sx={{
            width: { xs: '100%', sm: '40%' },
            maxWidth: { sm: '200px' },
            bgcolor: '#F9FAFB',
            borderLeft: { xs: 'none', sm: '1px solid #DFE3E8' },
            borderTop: { xs: '1px solid #DFE3E8', sm: 'none' },
          }}
        >
          <Controller
            name={currencyName}
            control={control}
            render={({ field }) => (
              <Select
                {...field}
                displayEmpty
                variant="standard"
                disabled={readOnly}
                sx={{
                  height: '100%',
                  width: '100%',
                  '& .MuiSelect-select': {
                    display: 'flex',
                    alignItems: 'center',
                    p: 2,
                    pl: 2,
                    pr: 3,
                  },
                  '&:before, &:after': {
                    display: 'none',
                  }
                }}
                renderValue={(selected) => {
                  const currency = currencyOptions.find(c => c.value === selected);
                  if (!selected || !currency) {
                    return <Typography color="text.secondary">Select currency</Typography>;
                  }
                  return (
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      {currency.logo && (
                        <img 
                          src={currency.logo} 
                          alt={currency.label} 
                          style={{ height: '24px', width: '24px', objectFit: 'contain' }} 
                        />
                      )}
                      <Typography fontWeight={500}>{currency.label}</Typography>
                    </Box>
                  );
                }}
              >
                {currencyOptions.map((currency) => (
                  <MenuItem 
                    key={currency.value} 
                    value={currency.value}
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1
                    }}
                  >
                    {currency.logo && (
                      <img 
                        src={currency.logo} 
                        alt={currency.label} 
                        style={{ height: '24px', width: '24px', objectFit: 'contain' }} 
                      />
                    )}
                    {currency.label}
                  </MenuItem>
                ))}
              </Select>
            )}
          />
        </Box>
      </Paper>
      
      {amountError && (
        <Typography color="error" variant="caption" sx={{ mt: 0.5, display: 'block' }}>
          {amountError.message}
        </Typography>
      )}
      
      {currencyError && (
        <Typography color="error" variant="caption" sx={{ mt: 0.5, display: 'block' }}>
          {currencyError.message}
        </Typography>
      )}
    </Box>
  );
};

export default AmountCurrencyInput;
