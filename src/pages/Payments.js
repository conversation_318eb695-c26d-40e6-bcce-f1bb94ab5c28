import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useForm, Controller, FormProvider } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as Yup from 'yup';
import {
  Card,
  CardContent,
  Grid,
  Typography,
  FormControl,
  Button,
  Stepper,
  OutlinedInput,
  Step,
  StepLabel,
  ToggleButton,
  ToggleButtonGroup,
  Select,
  MenuItem,
  Box,
  InputLabel,
  CircularProgress,
  Paper
} from '@mui/material';
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { post, get } from '../api';
import AmountCurrencyInput from './AmountCurrencyInput';
import Iconify from '../components/Iconify';
import NewPaymentMethodPop from '../components/settingsForms/NewPaymentMethodPopup';
import NewAddressPopup from '../components/settingsForms/NewAddressPopup';
import ImportedPaymentForm from './PaymentForm';
import { generateTrxRef } from '../utils/formatNumber';

const steps = ['Amount', 'Payment Method', 'Payout', 'Pay'];

const ProviderCard = ({ provider, isSelected, onSelect }) => (
  <Card
    onClick={() => onSelect(provider)}
    sx={{
      border: isSelected ? '2px solid #1F50C2' : '1px solid #ddd',
      borderRadius: 2,
      p: 2,
      cursor: 'pointer',
      minWidth: 200,
      mr: 2,
      mb: 1,
      transition: 'all 0.2s ease',
      '&:hover': {
        borderColor: '#1F50C2',
        transform: 'translateY(-2px)',
        boxShadow: '0 4px 16px rgba(31, 80, 194, 0.12)',
      },
      background: isSelected ? 'rgba(31, 80, 194, 0.05)' : 'white',
      position: 'relative',
      overflow: 'visible',
    }}
  >
    {isSelected && (
      <Box
        sx={{
          position: 'absolute',
          top: -10,
          right: -10,
          backgroundColor: '#1F50C2',
          color: 'white',
          borderRadius: '50%',
          width: 24,
          height: 24,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          fontSize: '14px',
          fontWeight: 'bold',
        }}
      >
        ✓
      </Box>
    )}
    <Typography variant="h6" fontWeight={600} sx={{ mb: 1 }}>{provider.name || `Provider ${provider.id}`}</Typography>
    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
      <Typography variant="body2" sx={{ opacity: 0.7 }}>Min: {provider.min_amount}</Typography>
      <Typography variant="body2" sx={{ opacity: 0.7 }}>Max: {provider.max_amount}</Typography>
    </Box>
    <Typography variant="subtitle1" fontWeight={600} color="primary">Rate: {provider.rate}</Typography>
    <Typography variant="caption" sx={{ color: '#666', mt: 1, display: 'block' }}>
      {getProviderTypeLabel(provider.provider_type)}
    </Typography>
  </Card>
);

// Helper function to get a nice label for provider types
const getProviderTypeLabel = (type) => {
  switch (type) {
    case 'bank':
      return 'Bank Transfer';
    case 'mobile':
      return 'Mobile Money';
    default:
      return type ? type.charAt(0).toUpperCase() + type.slice(1) : 'Other';
  }
};

// Helper function to get a nice label for service codes
const getServiceCodeLabel = (code) => {
  switch (code) {
    case 'BANK_TRANSFER':
      return 'Bank Transfer';
    case 'MOBILE_TRANSFER':
      return 'Mobile Money';
    default:
      return code.replace(/_/g, ' ').replace(/\b\w/g, c => c.toUpperCase());
  }
};

const PaymentForm = () => {

  const navigate = useNavigate();
  const [activeStep, setActiveStep] = useState(0);
  const [services, setServices] = useState([]);
  const [assets, setAssets] = useState([]);
  const [providers, setProviders] = useState([]);
  const [selectedProvider, setSelectedProvider] = useState(null);
  const [rate, setRate] = useState(null);
  const [fee, setFee] = useState(0.25);
  const [providerType, setProviderType] = useState('');
  const [convertedAmount, setConvertedAmount] = useState('');
  const [transactionType, setTransactionType] = useState('sell');
  const [fiatCurrencies, setFiatCurrencies] = useState([]);
  const [paymentMethods, setPaymentMethods] = useState([]);
  const [addresses, setAddresses] = useState([]);
  const [formData, setFormData] = useState({});
  const [isWalletOpen, setIsWalletOpen] = useState(false);
  const [isPMOpen, setIsPMOpen] = useState(false);
  const [quotes, setPendingQuotes] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoadingPaymentMethods, setIsLoadingPaymentMethods] = useState(false);
  const [isLoadingAddresses, setIsLoadingAddresses] = useState(false);
  const [groupedProviders, setGroupedProviders] = useState({});
  const [selectedServiceCode, setSelectedServiceCode] = useState('');
  
  const [loginDialogOpen, setLoginDialogOpen] = useState(false);
  const [openForm, setopenForm] = useState(false);
  const [user, setUser] = useState(null);
  const [userAuth, setUserAuth] = useState(null);
  const [userCheck, setUserCheck] = useState(true);




  const step1Schema = Yup.object().shape({
    service_id: Yup.string().required('Service is required'),
    send_asset: Yup.string().required('Pay currency is required'),
    receive_currency: Yup.string().required('Receiving currency is required'),
    send_amount: Yup.number().positive().required('Sending amount is required'),
    receive_amount: Yup.number().positive().required('Receivable amount is required'),
    provider_service_id: Yup.string().required('Provider is required'),
  });

  const step2Schema = Yup.object().shape({
    payment_method_id: Yup.string().required('Payment method is required')
  });

  const step3Schema = Yup.object().shape({
    sending_address: Yup.string().required('Sending address is required'),
  });

  let schema;
  if (activeStep === 0) {
    schema = step1Schema;
  } else if (activeStep === 1) {
    schema = step2Schema;
  } else if (activeStep === 2) {
    schema = step3Schema;
  } else {
    schema = step3Schema;
  }

  const methods = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      service_id: '',
      send_asset: 'USDT',
      send_asset_id: '',
      send_asset_chain: '',
      receive_currency: '',
      send_amount: '',
      receive_amount: '',
      payment_method_id: '',
      sending_address: '',
      bank_name: '',
      bank_code: '',
      provider_service_id: '',
      
      requires_memo: 'no',
      source: 'wallet'
    },
  });

  const {
    handleSubmit,
    control,
    watch,
    setValue,
    formState: { errors, isSubmitting: formIsSubmitting },
  } = methods;

  const watchedSendAmount = watch('send_amount', '');
  const watchedReceiveAmount = watch('receive_amount', '');
  const watchedSendAsset = watch('send_asset', '');
  const watchedSendAssetId = watch('send_asset_id', '');
  const watchedSendAssetChain = watch('send_asset_chain', '');
  const watchedReceiveCurrency = watch('receive_currency', '');
  const watchedServiceId = watch('service_id', '');
  const watchedPaymentMethodId = watch('payment_method_id', '');
  const watchedSendingAddress = watch('sending_address', '');
  const watchedProviderId = watch('provider_service_id', '');
  
  const watchedRequiresMemo = watch('requires_memo', '');
  const watchedSource = watch('source', '');

  const handlePaymentFormComplete = (data) => {
    console.log("Payment form completed with data:", data);
    
    // Check if we received a provider_service_id from the PaymentForm
    if (!data.provider_service_id) {
      console.error("No provider_service_id in data:", data);
      toast.error('Please select a provider');
      return;
    }
    
    // Get provider details if available
    const provider = providers.find(p => p.provider_service_id === data.provider_service_id);
    
    if (!provider) {
      console.error("Could not find provider with ID:", data.provider_service_id);
      // Create a provider object from the data we have
      const syntheticProvider = {
        provider_service_id: data.provider_service_id,
        name: data.provider_name || 'Unknown Provider',
        provider_type: data.provider_type || 'unknown',
        rate: data.ex_rate || 0
      };
      console.log("Using synthetic provider:", syntheticProvider);
      setSelectedProvider(syntheticProvider);
    } else {
      // We found the provider in our list, use it
      console.log("Found provider:", provider);
      setSelectedProvider(provider);
    }
    
    // Make a deep copy of the data to ensure we keep all values
    const step1Data = JSON.parse(JSON.stringify(data));
    console.log("Step 1 data (deep copy):", step1Data);
    
    // Add service code if available
    if (data.service_code) {
      step1Data.service_code = data.service_code;
      setSelectedServiceCode(data.service_code);
    }
    
    // Update form data state with all values from step 1
    setFormData(prevData => {
      const mergedData = { ...prevData, ...step1Data };
      console.log("Updated form data state:", mergedData);
      return mergedData;
    });
    
    if (data.receive_currency) {
      console.log(`Immediately loading payment methods for ${data.receive_currency}`);
      try {
        get(`accounts/getPaymentMethodForCurrency/${data.receive_currency}`).then(methodsData => {
          console.log("Payment methods loaded:", methodsData);
          if (Array.isArray(methodsData) && methodsData.length > 0) {
            setPaymentMethods(methodsData);
            setValue('payment_method_id', methodsData[0].payment_method_id.toString());
          } else {
            setPaymentMethods([]);
          }
        });
      } catch (error) {
        console.error("Error loading payment methods:", error);
      }
    }
    
    // Save key values to form values to ensure they're available in later steps
    console.log("Setting form values from step 1 data");
    if (data.send_asset) setValue('send_asset', data.send_asset);
    if (data.send_asset_id) setValue('send_asset_id', data.send_asset_id);
    if (data.send_asset_chain) setValue('send_asset_chain', data.send_asset_chain);
    if (data.receive_currency) setValue('receive_currency', data.receive_currency);
    if (data.send_amount) setValue('send_amount', data.send_amount);
    if (data.receive_amount) setValue('receive_amount', data.receive_amount);
    if (data.service_id) setValue('service_id', data.service_id);
    
    // Move to next step
    setActiveStep(1);
    
    setTimeout(() => {
      loadPaymentMethods();
      loadAddresses();
    }, 100);
  };

  const loadServicesAndAssets = async () => {
    const servicesData = await get('accounts/services');
    setServices(servicesData?.data);

    const assetsData = await get('accounts/getAssets');
    setAssets(assetsData?.data);
    if (assetsData?.data?.length > 0) {
      setValue('send_asset', assetsData?.data[0].asset_code);
      setValue('send_asset_id', assetsData?.data[0].asset_id);
      setValue('send_asset_chain', assetsData?.data[0].chain);
    }
  };


  
  const onLoginOpen = () => {
    setopenForm(true);
  }

  const closeForm = () => {
    setopenForm(false);
  }
  
  // on successful login
  const onLogin = (e) => {
    setUser(e);
    setopenForm(false);
    const returnPath    = sessionStorage.getItem('afterLoginRedirect');
    const userAuthToken = localStorage.getItem('authJWT');
    setUserAuth(userAuthToken);
    if (returnPath) {
      sessionStorage.removeItem('afterLoginRedirect');
      return navigate(returnPath);
    }
  }

  useEffect(() => {
    loadServicesAndAssets();
  }, [setValue]);

  useEffect(() => {
    const loadProvidersAndRate = async () => {
      if (!watchedSendAsset || !watchedReceiveCurrency || !watchedServiceId) return;
      setIsLoading(true);
      try {
        const searchData = {
          asset: watchedSendAsset,
          asset_code:   watchedSendAsset,
          currency: watchedReceiveCurrency,
          service_id: watchedServiceId,
        };
        const sortedProviders = await post('accounts/provider', searchData);
        setProviders(sortedProviders?.data);
        
        // Group providers by service_code
        const grouped = {};
        sortedProviders?.data.forEach(provider => {
          const serviceCode = provider.service_code || 'UNKNOWN';
          if (!grouped[serviceCode]) {
            grouped[serviceCode] = [];
          }
          grouped[serviceCode].push(provider);
        });
        
        setGroupedProviders(grouped);
        console.log("Grouped providers:", grouped);
        
        // Set default selected service code if available
        const availableServiceCodes = Object.keys(grouped);
        if (availableServiceCodes.length > 0) {
          setSelectedServiceCode(availableServiceCodes[0]);
          
          // Select the first provider from the first service code group
          if (grouped[availableServiceCodes[0]].length > 0) {
            const defaultProvider = grouped[availableServiceCodes[0]][0];
            setSelectedProvider(defaultProvider);
            setRate(defaultProvider.rate);
            setProviderType(defaultProvider.provider_type);
          }
        }
      } catch (error) {
        console.error('Error loading providers:', error);
        
        // toast.error('Failed to load providers');
      } finally {
        setIsLoading(false);
      }
    };
    loadProvidersAndRate();
  }, [watchedSendAsset, watchedReceiveCurrency, watchedServiceId, watchedSendAssetId]);

  useEffect(() => {
    if (watchedSendAmount && rate) {
      const calculatedReceive = (watchedSendAmount * rate).toFixed(2);
      setValue('receive_amount', calculatedReceive);
      setConvertedAmount(calculatedReceive);
    }
  }, [watchedSendAmount, rate, setValue]);

  useEffect(() => {
    if (watchedServiceId) {
      const selectedService = services.find(service => service.service_id === watchedServiceId);
      if (selectedService) {
        setFiatCurrencies([{ label: selectedService.currency, value: selectedService.currency, logo: selectedService.logo }]);
        setValue('receive_currency', selectedService.currency);
      }
    }
  }, [watchedServiceId, services, setValue]);

  const loadPaymentMethods = async () => {
    if (!watchedReceiveCurrency) {
      console.log("Cannot load payment methods: missing currency");
      return;
    }
    
    console.log(`Direct call to load payment methods for ${watchedReceiveCurrency}`);
    try {
      setIsLoadingPaymentMethods(true);
      
      // Make a direct call to the API to get payment methods
      const endpoint = `accounts/getPaymentMethodForCurrency/${watchedReceiveCurrency}`;
      console.log(`Calling API endpoint: ${endpoint}`);
      
      // Use the get function directly
      get(endpoint).then(response => {
        console.log("Raw payment methods response:", response);
        
        if (Array.isArray(response) && response.length > 0) {
          console.log(`Found ${response.length} payment methods, setting state`);
          setPaymentMethods(response);
          console.log("Setting default payment method ID:", response[0].payment_method_id);
          setValue('payment_method_id', response[0].payment_method_id.toString());
        } else {
          console.log("No payment methods found or invalid response format");
          setPaymentMethods([]);
          setValue('payment_method_id', '');
        }
        
        setIsLoadingPaymentMethods(false);
      }).catch(err => {
        console.error("API error when loading payment methods:", err);
        toast.error("Error loading payment methods");
        setPaymentMethods([]);
        setValue('payment_method_id', '');
        setIsLoadingPaymentMethods(false);
      });
      
    } catch (error) {
      console.error('Error in loadPaymentMethods function:', error);
      setPaymentMethods([]);
      setValue('payment_method_id', '');
      setIsLoadingPaymentMethods(false);
    }
  };

  const loadAddresses = async () => {
    if (!watchedSendAsset || !watchedSendAssetChain) {
      console.log("Cannot load addresses: missing asset or chain");
      return;
    }
    
    console.log(`Loading addresses for ${watchedSendAsset} on chain ${watchedSendAssetChain}`);
    try {
      setIsLoadingAddresses(true);
      const addressesData = await get(`accounts/getAddressesForCurrency/${watchedSendAsset}?chain=${watchedSendAssetChain}`);
      console.log("Addresses response:", addressesData);
      
      if (Array.isArray(addressesData) && addressesData.length > 0) {
        // Store the complete address data
        setAddresses(addressesData);
        console.log("Setting default address ID:", addressesData[0].address_id);
        // Force update the form value with the first address ID
        setValue('sending_address', addressesData[0].address_id.toString());
        
        // Store the address reference in hidden field for later retrieval
        if (addressesData[0].address) {
          console.log("Storing associated address string for later use");
        }
      } else {
        console.log("No addresses found or invalid response");
        setAddresses([]);
        setValue('sending_address', '');
      }
    } catch (error) {
      console.error('Error loading addresses:', error);
      // toast.error('Failed to load addresses');
      setAddresses([]);
      setValue('sending_address', '');
    } finally {
      setIsLoadingAddresses(false);
    }
  };

  const getPendingQuotes = async () => {
    const quotes = await get('accounts/getPendingQuotes');
    setPendingQuotes(quotes || []);
    return quotes
  };



  useEffect(() => {

    if (activeStep === 1) {

      console.log("Step 1 active - loading payment methods and addresses");
      // First clear any previous values to prevent stale state
      setValue('payment_method_id', '');
      setValue('sending_address', '');
      
      // Then load fresh data
      loadPaymentMethods();
      loadAddresses();
      getPendingQuotes();
      
      // Create a small delay to ensure the values are applied correctly
      setTimeout(() => {
        // If we already have data, make sure the form values are updated
        if (addresses.length > 0) {
          console.log("Re-setting address from already loaded addresses");
          setValue('sending_address', addresses[0].address_id.toString());
        }
        
        if (paymentMethods.length > 0) {
          console.log("Re-setting payment method from already loaded methods");
          setValue('payment_method_id', paymentMethods[0].payment_method_id.toString());
        }
      }, 300);
    }
  }, [activeStep, watchedReceiveCurrency]);

  // Separate useEffect to monitor asset/chain changes
  useEffect(() => {
    if (activeStep === 1 && watchedSendAsset && watchedSendAssetChain) {
      console.log(`Asset or chain changed - reloading addresses for ${watchedSendAsset} on chain ${watchedSendAssetChain}`);
      loadAddresses();
    }
  }, [watchedSendAsset, watchedSendAssetChain, activeStep]);

  useEffect(() => {
    if (assets.length > 0 && !watchedSendAssetChain) {
      const defaultAsset = assets.find(asset => asset.asset_code === watchedSendAsset);
      if (defaultAsset?.chain) {
        console.log(`Setting default chain to ${defaultAsset.chain}`);
        setValue('send_asset_chain', defaultAsset.chain);
      }
    }
  }, [assets, watchedSendAsset, watchedSendAssetChain, setValue]);


  
  useEffect(() => {
    getPendingQuotes();
    setValue('requires_memo', 'no');
    setValue('source', 'wallet');
  }, []);


  const onSubmitForm = async (data) => {

   try{
        // console.log("Form submitted with data:", data);
        // Create a deep copy to ensure we don't lose any data
        const currentFormData = JSON.parse(JSON.stringify(data));

        // Update the formData state with the latest data
        setFormData(prevData => {
          const mergedData = { ...prevData, ...currentFormData };
          console.log("Updated form data in onSubmit:", mergedData);
          return mergedData;
        });



        // Also call getPendingQuotes
        const pendQuotes  = await getPendingQuotes();
        if(quotes.length > 0){
          return toast.error('Please cancel pending quotes to continue');
        }

        if (activeStep === 0 ) {


          console.log("Moving from step 0 to step 1");
          // Then make the direct API calls rather than waiting for the useEffect hooks
          console.log("Making direct API calls for payment methods and addresses");

          // Direct API call for payment methods
          if (data.receive_currency) {
            const endpoint = `accounts/getPaymentMethodForCurrency/${data.receive_currency}`;
            console.log(`Direct API call to: ${endpoint}`);

            get(endpoint).then(response => {
              console.log("Payment methods direct response:", response);
              if (Array.isArray(response) && response.length > 0) {
                console.log(`Setting ${response.length} payment methods from direct call`);
                setPaymentMethods(response);
                const methodId = response[0].payment_method_id.toString();
                console.log("Setting payment method ID:", methodId);
                setValue('payment_method_id', methodId);
              } else {
                console.log("No payment methods found from direct call");
                setPaymentMethods([]);
                setValue('payment_method_id', '');
              }
            }).catch(err => {
              console.error("Error in direct payment methods call:", err);
              setValue('payment_method_id', '');
            });
          }

          // Direct API call for addresses
          if (data.send_asset && data.send_asset_chain) {

            const endpoint = `accounts/getAddressesForCurrency/${data.send_asset}?chain=${data.send_asset_chain}`;
            console.log(`Direct API call to: ${endpoint}`);

            get(endpoint).then(response => {
              console.log("Addresses direct response:", response);
              if (Array.isArray(response) && response.length > 0) {
                console.log(`Setting ${response.length} addresses from direct call`);
                setAddresses(response);
                const addressId = response[0].address_id.toString();
                console.log("Setting address ID:", addressId);
                setValue('sending_address', addressId);
              } else {
                console.log("No addresses found from direct call");
                setAddresses([]);
                setValue('sending_address', '');
              }
            }).catch(err => {
              console.error("Error in direct addresses call:", err);
              setValue('sending_address', '');
            });
          }


          // First update the step state
          setActiveStep(1);

        } else if (activeStep === 1) {

          console.log("Validating step 1 data...");
          if (!data.payment_method_id) {
            toast.error('Please select a payment method');
            return;
          }

          // Log detailed information about our current selections
          console.log("Validate to confirmation step with data:", {
            formData: {...formData, ...data},
            paymentMethodId: data.payment_method_id,
            sendingAddressId: data.sending_address,
            selectedPaymentMethod: paymentMethods.find(m => m.payment_method_id.toString() === data.payment_method_id.toString()),
            // selectedAddress: addresses.find(a => a.address_id.toString() === data.sending_address.toString()),
            allPaymentMethods: paymentMethods,
            allAddresses: addresses,
            watchedValues: {
              sendAmount: watchedSendAmount,
              receiveAmount: watchedReceiveAmount,
              sendAsset: watchedSendAsset,
              sendAssetId: watchedSendAssetId,
              sendAssetChain: watchedSendAssetChain,
              receiveCurrency: watchedReceiveCurrency,
              serviceId: watchedServiceId,
              paymentMethodId: watchedPaymentMethodId,
              sendingAddress: watchedSendingAddress
            }
          });

          console.log("Moving from step 1 to step 2");
          setActiveStep(2);

        } else if (activeStep === 2) {

        console.log("Validating step 2 data...");


        const watchedRequiresMemo = watch('requires_memo', 'no');
        const watchedSource = watch('source', 'wallet');


        if (!data.source) {
          toast.error('Please select wallet source option');
          return;
        }

        if (!data.sending_address) {
          toast.error('Please select a sending address');
          return;
        }

        // Log detailed information about our current selections
        console.log("Moving to confirmation step with data:", {
          formData: {...formData, ...data},
          paymentMethodId: data.payment_method_id,
          sendingAddressId: data.sending_address,
          selectedPaymentMethod: paymentMethods.find(m => m.payment_method_id.toString() === data.payment_method_id.toString()),
          selectedAddress: addresses.find(a => a.address_id.toString() === data.sending_address.toString()),
          allPaymentMethods: paymentMethods,
          allAddresses: addresses,
          watchedValues: {
            sendAmount: watchedSendAmount,
            receiveAmount: watchedReceiveAmount,
            sendAsset: watchedSendAsset,
            sendAssetId: watchedSendAssetId,
            sendAssetChain: watchedSendAssetChain,
            receiveCurrency: watchedReceiveCurrency,
            serviceId: watchedServiceId,
            paymentMethodId: watchedPaymentMethodId,
            sendingAddress: watchedSendingAddress
          }
        });

        console.log("Moving from step 2 to step 3");
        setActiveStep(3);

        } else if (activeStep === 3) {

          console.log("Moving from step 2 to step 3");
          await sendData(data);

        } else {

          console.log("Moving to next step");
          setActiveStep(prev => prev + 1);
        }

   } catch(e){
     console.log("error ", e)
   }

  };

  const sendData = async (data) => {
    setIsSubmitting(true);
    
    // Find the selected payment method and address objects for display
    const selectedPaymentMethod = paymentMethods.find(m => m.payment_method_id.toString() === data.payment_method_id?.toString());
    const selectedAddress = addresses.find(a => a.address_id.toString() === data.sending_address?.toString());
    
    // Log all form data from both steps including selections for debugging
    const debugData = {
      // Form state
      formData,
      currentData: data,
      
      // Selections
      selectedProvider,
      selectedPaymentMethod,
      selectedAddress,
      
      // Watched values
      watchedValues: {
        sendAmount: watchedSendAmount,
        receiveAmount: watchedReceiveAmount,
        sendAsset: watchedSendAsset,
        sendAssetId: watchedSendAssetId,
        sendAssetChain: watchedSendAssetChain,
        receiveCurrency: watchedReceiveCurrency,
        serviceId: watchedServiceId,
        paymentMethodId: watchedPaymentMethodId,
        sendingAddress: watchedSendingAddress
      },
      
      // Available options
      availableOptions: {
        paymentMethodsCount: paymentMethods.length,
        addressesCount: addresses.length
      }
    };
    
    console.log("COMPLETE FORM DEBUG DATA:", debugData);
    
    // Include the full address object instead of just the ID
    const addressObject = selectedAddress || {};
    
    // Create a simplified form data object with only the requested fields
    // const completeFormData = {
    //   // The exact fields requested by the user
    //   provider_service_id: selectedProvider?.provider_service_id || "",
    //   send_asset: watchedSendAsset,
    //   sending_address: addressObject.address || "",
    //   company_id: data.company_id || formData.company_id || "",
    //   send_amount: watchedSendAmount,
    //   receive_currency: watchedReceiveCurrency,
    //   service_id: watchedServiceId,
    //   ex_rate: rate || 0,
    //   receiver_address: data.receiver_address || formData.receiver_address || "",
    //   payment_method_id: data.payment_method_id,
    //   chain: watchedSendAssetChain || ""
    // };

    const generatedRef = await generateTrxRef();
    const completeFormData = {
      service_id: watchedServiceId,
      provider_service_id: selectedProvider?.provider_service_id || "",
      reference_id: generatedRef,
      asset_code: watchedSendAsset,
      receive_currency: watchedReceiveCurrency,
      send_amount: watchedSendAmount,
      payment_method_id: data.payment_method_id,
      source: "exchange",
      sending_address: addressObject.address || "",
      company_id: data.company_id || formData.company_id || ""
    }
    
    console.log("Simplified form data being sent with exact keys:", completeFormData);
    
    // Highlight the changes we've made
    console.log("✅ IMPORTANT: Sending only the requested fields:", Object.keys(completeFormData));
    console.log("📌 Chain value:", completeFormData.chain);
    
    // Check for missing required fields
    // Check for missing required fields
    // const requiredFields = [
    //   'provider_service_id', 'send_asset', 'sending_address', 'send_amount', 
    //   'receive_currency', 'service_id', 'payment_method_id', 'chain'
    // ];

    const requiredFields = [
      'provider_service_id', 'asset_code', 'sending_address', 'send_amount', 
      'receive_currency', 'service_id', 'payment_method_id'
    ];
    
    const missingFields = requiredFields.filter(field => {
      const value = completeFormData[field];
      return value === undefined || value === null || value === '';
    });
    
    if (missingFields.length > 0) {
      console.warn("⚠️ WARNING: Missing required fields:", missingFields);
    }
    
    try {
      // Log the final data being sent to API
      console.log("Final data for API submission:", JSON.stringify(completeFormData));
      
      // const response = await post('accounts/getInvoice', completeFormData);
      const response = await post('accounts/generateQuote', completeFormData);
      console.log("Invoice API response:", response);
      
      if (response?.status === 200) {
        toast.success(response.message, { position: 'top-right' });
        navigate(`/dashboard/confirmation?id=${response?.data?.quote_id}`);
      } else {
        toast.error(response.message || 'Failed to process payment', { position: 'top-right' });
        console.error("API returned error:", response);
      }
    } catch (error) {
      console.error('Error processing payment:', error);
      toast.error('Error processing payment. Please try again.', { position: 'top-right' });
    } finally {
      setIsSubmitting(false);
    }
  };

  const onFormBack = () => {
    setActiveStep(prev => prev - 1);
  };

  const handleTransactionType = (event, newType) => {
    if (newType !== null) {
      setTransactionType(newType);
    }
  };

  const handleProviderSelect = (provider) => {
    console.log("Selected provider:", provider);
    setSelectedProvider(provider);
    // Update the provider ID in the form
    setValue("provider_service_id", provider?.provider_service_id);
    // Set the rate from the selected provider
    setRate(provider.rate);
    // Set the provider type
    setProviderType(provider.provider_type);
  };

  const onWalletOpen = () => {
    console.log("Opening address popup");
    setIsWalletOpen(true);
  };

  const onWalletClose = () => {
    console.log("Closing address popup");
    setIsWalletOpen(false);
  };

  const onWalletSuccess = async () => {
    console.log("Address added successfully");
    setIsWalletOpen(false);
    await loadAddresses();
    toast.success("New address added successfully");
  };

  const onPMOpen = () => {
    console.log("Opening payment method popup");
    setIsPMOpen(true);
  };

  const onPMClose = () => {
    console.log("Closing payment method popup");
    setIsPMOpen(false);
  };

  const onPMSuccess = async () => {
    console.log("Payment method added successfully");
    setIsPMOpen(false);
    await loadPaymentMethods();
    toast.success("New payment method added successfully");
  };

  // Update the GroupedProviders component
  const GroupedProviders = () => {
    if (isLoading) {
      return (
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 3 }}>
          <CircularProgress />
        </Box>
      );
    }
    
    const serviceCodes = Object.keys(groupedProviders);
    if (serviceCodes.length === 0) {
      return (
        <Paper sx={{ p: 3, mb: 3, borderRadius: '12px', textAlign: 'center' }}>
          <Typography>No providers available for this asset and currency combination</Typography>
        </Paper>
      );
    }


    // return (
    //   <Box sx={{ width: '100%', mb: 3 }} >
    //     <Typography variant="h6" gutterBottom sx={{ mt: 4 }}>Off-ramp Type</Typography>

    //     <ToggleButtonGroup
    //       value={selectedServiceCode}
    //       exclusive
    //       onChange={(e, newValue) => {
    //         if (newValue) {
    //           setSelectedServiceCode(newValue);
    //           // Reset the selected provider when changing service code
    //           setSelectedProvider(null);
    //           // Clear the provider_service_id in the form
    //           setValue('provider_service_id', '');
    //         }
    //       }}
    //       aria-label="Off-ramp Type"
    //       sx={{ mb: 2, display: 'flex', flexWrap: 'wrap' }}
    //     >
    //       {serviceCodes.map(code => (
    //         <ToggleButton
    //           key={code}
    //           value={code}
    //           sx={{
    //             flex: { xs: '1 0 100%', sm: '0 1 auto' },
    //             mb: 1,
    //             mr: 1,
    //             py: 1.5,
    //             px: 3,
    //             fontWeight: selectedServiceCode === code ? 700 : 400,
    //             borderColor: selectedServiceCode === code ? '#1F50C2' : 'divider',
    //             borderRadius: '8px',
    //             '&.Mui-selected': {
    //               backgroundColor: 'rgba(31, 80, 194, 0.1)',
    //               color: '#1F50C2',
    //               borderColor: '#1F50C2',
    //               '&:hover': {
    //                 backgroundColor: 'rgba(31, 80, 194, 0.2)',
    //               }
    //             }
    //           }}
    //         >
    //           {getServiceCodeLabel(code)}
    //         </ToggleButton>
    //       ))}
    //     </ToggleButtonGroup>

    //     <Typography variant="subtitle1" gutterBottom>
    //       Select Provider {watchedProviderId ? '✓' : '(required)'}
    //     </Typography>

    //     <Box sx={{ display: 'flex', flexWrap: 'wrap', pb: 1 }}>
    //       {selectedServiceCode && groupedProviders[selectedServiceCode]?.map(provider => (
    //         <ProviderCard
    //           key={provider.id}
    //           provider={provider}
    //           isSelected={selectedProvider?.provider_service_id === provider.provider_service_id}
    //           onSelect={handleProviderSelect}
    //         />
    //       ))}
    //     </Box>
    //   </Box>
    // );
  };

  return (

    <FormProvider {...methods}>
      <Box sx={{ mb: 5 }}>
        <Card sx={{ borderRadius: '16px', overflow: 'hidden', boxShadow: '0 4px 20px rgba(0,0,0,0.08)' }}>
          <CardContent sx={{ p: { xs: 2, md: 3 } }}>
            <Typography variant="h5" fontWeight={700} sx={{ mb: 3, color: '#1F50C2' }}>
              Make a Payment
            </Typography>
            
            <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
              {steps.map((label) => (
                <Step key={label}>
                  <StepLabel>{label}</StepLabel>
                </Step>
              ))}
            </Stepper>
            

            {activeStep === 0 ? (
              <>
                <ImportedPaymentForm onSubmitSuccess={handlePaymentFormComplete} isOpen={openForm} onClose={closeForm} onSuccess={onLogin} onLoginOpen={onLoginOpen}/>
                {providers.length > 0 && <GroupedProviders />}
              </>
            ) : (


              <form onSubmit={handleSubmit(onSubmitForm)}>
                {activeStep === 1 && (
                  <Box>
                    <Typography variant="h6" gutterBottom>
                      Payment Details
                    </Typography>
                    
                    <Grid container spacing={3}>
                      <Grid item xs={12}>
                        <FormControl fullWidth error={Boolean(errors.payment_method_id)}>
                          <InputLabel>Payment Method </InputLabel>
                          <Controller
                            name="payment_method_id"
                            control={control}
                            defaultValue=""
                            render={({ field }) => (
                              <Select
                                {...field}
                                label="Payment Method"
                                displayEmpty
                                value={field.value || ""}
                                onChange={(e) => {
                                  const selectedValue = e.target.value;
                                  console.log("Selected payment method value:", selectedValue);
                                  field.onChange(selectedValue);
                                  // Force setValue to ensure the form state is updated
                                  setValue('payment_method_id', selectedValue);
                                }}
                                disabled={isLoadingPaymentMethods}
                                startAdornment={
                                  isLoadingPaymentMethods ? (
                                    <CircularProgress size={20} color="inherit" sx={{ mr: 1 }} />
                                  ) : null
                                }
                              >
                                {paymentMethods.length === 0 ? (
                                  <MenuItem disabled value="">
                                    <Typography color="text.secondary">No payment methods available</Typography>
                                  </MenuItem>
                                ) : (
                                  paymentMethods.map((method) => (
                                    <MenuItem key={method.payment_method_id} value={method.payment_method_id.toString()} sx={{
                                      display: 'flex',
                                      flexDirection: 'column',
                                      alignItems: 'flex-start',
                                      py: 1.5
                                    }}>
                                      <Typography fontWeight="bold">{method.name}</Typography>
                                      <Typography variant="body2" color="text.secondary">
                                        {method.bank_name || method.bank_code || method.number || method.phone_number || ""}
                                           {(method?.account_name)? ` (${method?.account_name})` : ""}
                                      </Typography>
                                    </MenuItem>
                                  ))
                                )}
                              </Select>
                            )}
                          />
                          {errors.payment_method_id && (
                            <Typography color="error" variant="caption">
                              {errors.payment_method_id.message}
                            </Typography>
                          )}
                        </FormControl>
                        <Button
                          variant="contained"
                          color="primary"
                          onClick={onPMOpen}
                          startIcon={<Iconify icon="eva:plus-fill" />}
                          sx={{ 
                            mt: 2,
                            backgroundColor: '#1F50C2',
                            '&:hover': {
                              backgroundColor: '#0F2B77',
                            },
                            boxShadow: '0 4px 12px rgba(31, 80, 194, 0.15)'
                          }}
                        >
                          Add New Payment Method
                        </Button>
                      </Grid>
                    </Grid>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>

                      <Button onClick={onFormBack} variant="outlined">
                        Back
                      </Button>

                      <Button 
                        type="submit"
                        variant="contained"
                        disabled={formIsSubmitting || !watch('payment_method_id')}
                        sx={{
                          backgroundColor: '#1F50C2',
                          '&:hover': {
                            backgroundColor: '#0F2B77',
                          }
                        }} >
                        {formIsSubmitting ? <CircularProgress size={24} /> : 'Continue'}
                      </Button>
                    </Box>
                  </Box>
                )}

                {activeStep === 2 && (
                  <Box>
                    <Typography variant="h6" gutterBottom>
                      Payout wallet details
                    </Typography>
                    
                    <Grid container spacing={3}>

                      
                    <Grid item xs={12} className="mb-0">
                        <FormControl fullWidth error={Boolean(errors.source)}  className="ml-0 pl-0">
                          <span className="ml-0 pl-0 mb-0 pb-0 label-text">Payment Source</span>
                        </FormControl>
                        <div className="toggle-option-outer mb-0">
                          <div className="toggle-option mb-0">
                            <button className={(watchedSource === "wallet")? "active-select":""}
                                    type="button"
                                    onClick={() => setValue('source', 'wallet')} >Wallet</button>
                            <button className={(watchedSource === "exchange")? "active-select":""}
                                    onClick={() => setValue('source', 'exchange')} 
                                    type="button">Exchange</button>
                          </div>
                        </div>  
                         
                      </Grid>  


                      <Grid item xs={12}>
                        <FormControl fullWidth error={Boolean(errors.sending_address)}>
                          <InputLabel>Sending Address</InputLabel>
                          <Controller
                            name="sending_address"
                            control={control}
                            defaultValue=""
                            render={({ field }) => (
                              <Select
                                {...field}
                                label="Sending Address"
                                displayEmpty
                                value={field.value || ""}
                                onChange={(e) => {
                                  const selectedValue = e.target.value;
                                  console.log("Selected address value:", selectedValue);
                                  field.onChange(selectedValue);
                                  // Force setValue to ensure the form state is updated
                                  setValue('sending_address', selectedValue);
                                }}
                                disabled={isLoadingAddresses}
                                startAdornment={
                                  isLoadingAddresses ? (
                                    <CircularProgress size={20} color="inherit" sx={{ mr: 1 }} />
                                  ) : null
                                }
                              >
                                {addresses.length === 0 ? (
                                  <MenuItem disabled value="">
                                    <Typography color="text.secondary">No addresses available</Typography>
                                  </MenuItem>
                                ) : (
                                   addresses.map((address) => (
                                    <MenuItem key={address.address_id} value={address.address_id.toString()} sx={{
                                      display: 'flex',
                                      flexDirection: 'column',
                                      alignItems: 'flex-start',
                                      py: 1.5
                                    }}>
                                      <Typography fontWeight="bold">{address.name}</Typography>
                                      <Typography variant="body2" color="text.secondary" sx={{ wordBreak: 'break-all' }}>
                                        {address?.address}  {(address?.wallet_name)? ` (${address?.wallet_name})`: ''}
                                      </Typography>
                                    </MenuItem>
                                  ))
                                )}
                              </Select>
                            )}
                          />
                          {errors.sending_address && (
                            <Typography color="error" variant="caption">
                              {errors.sending_address.message}
                            </Typography>
                          )}
                        </FormControl>
                        <Button
                          variant="contained"
                          color="primary"
                          onClick={onWalletOpen}
                          startIcon={<Iconify icon="eva:plus-fill" />}
                          sx={{ 
                            mt: 2,
                            backgroundColor: '#1F50C2',
                            '&:hover': {
                              backgroundColor: '#0F2B77',
                            },
                            boxShadow: '0 4px 12px rgba(31, 80, 194, 0.15)'
                          }}
                        >
                          Add New Address
                        </Button>
                      </Grid>
                    </Grid>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>
                      <Button onClick={onFormBack} variant="outlined">
                        Back
                      </Button>
                      <Button 
                        type="submit"
                        variant="contained"
                        disabled={formIsSubmitting || !watch('payment_method_id') || !watch('sending_address')}
                        sx={{
                          backgroundColor: '#1F50C2',
                          '&:hover': {
                            backgroundColor: '#0F2B77',
                          }
                        }}
                      >
                        {formIsSubmitting ? <CircularProgress size={24} /> : 'Continue'}
                      </Button>
                    </Box>
                  </Box>
                )}


                {activeStep === 3 && (
                  <Box>
                    <Typography variant="h6" gutterBottom>
                      Confirm Your Payment
                    </Typography>
                    
                    <Paper sx={{ p: 3, mb: 3, borderRadius: '12px' }}>
                      <Grid container spacing={2}>
                        <Grid item xs={12}>
                          <Typography variant="body2" color="text.secondary">Provider</Typography>
                          <Typography variant="body1" fontWeight={500}>
                            {selectedProvider ? selectedProvider.name : 'Not selected'}
                            {selectedProvider && ` (${getProviderTypeLabel(selectedProvider.provider_type)})`}
                          </Typography>
                        </Grid>
                        
                        <Grid item xs={12}>
                          <Typography variant="body2" color="text.secondary">Selected Payment Method</Typography>
                          <Typography variant="body1" fontWeight={500}>
                            {(() => {
                              // Find the selected payment method by ID
                              const selectedMethod = paymentMethods.find(method => 
                                method.payment_method_id.toString() === watchedPaymentMethodId.toString()
                              );
                              if (selectedMethod) {
                                return selectedMethod.account_name || selectedMethod.name || 'Unknown method';
                              }
                              
                              return 'Not selected';
                            })()}
                          </Typography>
                        </Grid>
                        
                        <Grid item xs={12}>
                          <Typography variant="body2" color="text.secondary">Selected Sending Address</Typography>
                          <Typography variant="body1" fontWeight={500}>
                            {(() => {
                              // Find the selected address by ID
                              const selectedAddress = addresses.find(address => 
                                address.address_id.toString() === watchedSendingAddress.toString()
                              );
                              
                              // console.log("Selected address:", selectedAddress, "ID:", watchedSendingAddress);
                              // console.log("All addresses:", addresses);
                              
                              // Determine address display text
                              let addressDisplay = 'Not selected';
                              
                              if (selectedAddress) {
                                if (selectedAddress.name) {
                                  addressDisplay = selectedAddress.name;
                                } else if (selectedAddress.address) {
                                  const addressText = selectedAddress.address;
                                  addressDisplay = `${addressText.substring(0, 20)}...`;
                                }
                              }
                              
                              return addressDisplay;
                            })()}
                          </Typography>
                        </Grid>
                        
                        <Grid item xs={12} md={6}>
                          <Typography variant="body2" color="text.secondary">You're Sending</Typography>
                          <Typography variant="h6" fontWeight={600} color="primary.main">
                            {watchedSendAmount} {watchedSendAsset}
                          </Typography>
                        </Grid>
                        
                        <Grid item xs={12} md={6}>
                          <Typography variant="body2" color="text.secondary">You'll Receive</Typography>
                          <Typography variant="h6" fontWeight={600} color="success.main">
                            {convertedAmount} {watchedReceiveCurrency}
                          </Typography>
                        </Grid>
                        
                        <Grid item xs={12}>
                          <Typography variant="body2" color="text.secondary">Exchange Rate</Typography>
                          <Typography variant="body1" fontWeight={500}>
                            1 {watchedSendAsset} = {rate} {watchedReceiveCurrency}
                          </Typography>
                        </Grid>
                        
                        <Grid item xs={12}>
                          <Typography variant="body2" color="text.secondary">Fee</Typography>
                          <Typography variant="body1" fontWeight={500}>
                            {fee}% ({(watchedSendAmount * fee / 100).toFixed(2)} {watchedSendAsset})
                          </Typography>
                        </Grid>
                      </Grid>
                    </Paper>
                    
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>
                      <Button onClick={onFormBack} variant="outlined">
                        Back
                      </Button>
                      <Button 
                        type="submit"
                        variant="contained"
                        disabled={isSubmitting}
                        sx={{
                          backgroundColor: '#1F50C2',
                          '&:hover': {
                            backgroundColor: '#0F2B77',
                          }
                        }}
                      >
                        {isSubmitting ? <CircularProgress size={24} /> : `Confirm Payment`}
                      </Button>
                    </Box>
                  </Box>
                )}
              </form>
            )}
          </CardContent>
        </Card>
      </Box>
      
      <NewPaymentMethodPop 
        open={isPMOpen} 
        isOpen={isPMOpen}
        onClose={onPMClose} 
        onSuccess={onPMSuccess}
        data={{ currency: watchedReceiveCurrency }} 
      />
      
      <NewAddressPopup 
        open={isWalletOpen}
        isOpen={isWalletOpen} 
        onClose={onWalletClose} 
        onSuccess={onWalletSuccess}
        data={{ payCurrency: watchedSendAssetChain }}
      />
    </FormProvider>
  );
};

const SelectionField = ({ name, label, control, options, error }) => {
  return (
    <Grid item xs={12}>
      <Controller
        name={name}
        control={control}
        render={({ field }) => (
          <FormControl fullWidth error={!!error}>
            <InputLabel>{label}</InputLabel>
            <Select {...field} input={<OutlinedInput label={label} />}>
              {options.map(option => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        )}
      />
      {error && <Typography color="error">{error.message}</Typography>}
    </Grid>
  );
};

export default PaymentForm;