import React, { useState, useEffect } from 'react';
import {
  Card, CardContent,
  Typography,
  Box, Chip,
  CircularProgress,
  Button, Grid,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Container,
  TextField,
  Paper,
  Divider,
  IconButton,
  Tooltip,
  Stack,
  Table, 
  TableBody, 
  TableCell, 
  TableRow
} from '@mui/material';


import { useForm, Controller } from 'react-hook-form';
import QRCode from 'qrcode.react';
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { useLocation, useNavigate } from 'react-router-dom';
import { get } from '../api';
import { cancelQuote, confirmPayment } from '../services';
import Label from '../components/Label';
import Page from '../components/Page';
import Iconify from '../components/Iconify';

const Confirmation = () => {
  const query = new URLSearchParams(useLocation().search);
  const id = query.get('id');

  const [transaction, setTransaction] = useState({});
  const [transactionReload, setTransactionReload] = useState(false);
  const [isPolling, setIsPolling] = useState(true);
  const [showSuccess, setShowSuccess] = useState(false);
  const [trxnDelete, setTrxnDelete] = useState(false);
  const [trxnDeleteDetails, setTrxnDeleteDetails] = useState({});
  const [transactionHash, setTransactionHash] = useState("");

  const [trxnConfirm, setTrxnConfirm] = useState(false);
  const [trxnProcessingConfirm, setTrxnProcessingConfirm] = useState(false);
  const [trxnConfirmDetails, setTrxnConfirmDetails] = useState({});
  
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNavigate();

  const methods = useForm({
    defaultValues: {},
  });

  const {
    handleSubmit,
    control,
    reset,
    formState: { errors, isSubmitting },
  } = methods;

  useEffect(() => {
    const loadTransaction = async () => {
      try {
        setIsLoading(false);
        const response = await get(`accounts/getTransaction/${id}`);
        setIsLoading(false);
        
        if (response.status === 200) {
          setTransaction(response.data);

          // Stop polling if the transaction is final
          if (["FAILED", "SUCCESSFUL", "CANCELLED", "EXPIRED"].includes(response.data.status.toUpperCase())) {
            setIsPolling(false);
          }

          // Show success details if transaction is successful
          if (response.data.status.toUpperCase() === "SUCCESSFUL") {
            setShowSuccess(true);
          }
        } else {
          toast.dismiss();
          toast.error(response.message || "Failed to load transaction data");
        }
      } catch (error) {
        setIsLoading(false);
        console.error("Error fetching transaction:", error);
        toast.dismiss();
        toast.error("Failed to load transaction data.");
      }
    };

    loadTransaction();

    // Poll every 20 seconds if the status is not final
    const interval = setInterval(() => {
      if (isPolling) {
        loadTransaction();
      }
    }, 20000);

    return () => clearInterval(interval); // Cleanup on unmount
  }, [id, isPolling]);

  const reloadTransaction = async () => {
    try {
      setTransactionReload(true);
      const response = await get(`accounts/getTransaction/${id}`);
      setTransactionReload(false);
      if (response.status === 200) {
        setTransaction(response.data);
        if (["FAILED", "SUCCESSFUL", "EXPIRED"].includes(response.data.status.toUpperCase())) {
          setIsPolling(false);
        }

        if (response.data.status.toUpperCase() === "SUCCESSFUL") {
          setShowSuccess(true);
        }
      } else {
        toast.dismiss();
        toast.error(response.message || "Failed to reload transaction data");
      }
    } catch (error) {
      setTransactionReload(false);
      console.error("Error fetching transaction:", error);
      toast.dismiss();
      toast.error("Failed to reload transaction data.");
    }
  };

  const confirmTrxnCancel = (e) => {
    setTrxnDelete(true);
    setTrxnDeleteDetails(e);
  };


  const confirmTrxnPayment = (e) => {

    setTransactionHash(transaction?.hash || "")
    setTrxnConfirm(true);
    setTrxnConfirmDetails(e);
  }
                    
  const confirmToDelete = async (e) => {
    try {
      const transactionCancel = await cancelQuote({id: e?.transId});
      if (transactionCancel.status === 200 || transactionCancel.status === 200) {
        toast.success(`${transactionCancel.message}`);
        setTrxnDeleteDetails({}); 
        setTrxnDelete(false);
        navigate('/dashboard/transactions');
      } else {

        toast.dismiss();
        toast.error(`${transactionCancel.message}`);
      }
    } catch (error) {
      toast.dismiss();
      toast.error("Error cancelling transaction");
    }
  };


  const confirmTheTransaction = async (e) => {
    try {

      if(transactionHash === ""){
        toast.dismiss();
        return toast.error("Provide transaction hash to confirm payment");
      }

      setTrxnProcessingConfirm(true);
      const response = await confirmPayment({hash: transactionHash, trans_id: e.transId})
      if(response.status === 200){

        setTrxnProcessingConfirm(false);
        setTrxnConfirmDetails({});
        reloadTransaction();
        setTransactionHash("")
      } else{
          
        setTrxnProcessingConfirm(false);
        reloadTransaction();
        toast.dismiss();
        toast.error(response.message);
      }

    } catch (error) {
      setTrxnProcessingConfirm(false);
      toast.dismiss();
      toast.error("Error cancelling transaction");
    }
  }
  
  function getColorForStatus(status) {
    status = status.toUpperCase();
    switch (status) {
      case 'PENDING':
        return 'warning';
      case 'COMPLETED':
      case 'SUCCESSFUL':
        return 'success';
      case 'FAILED':
      case 'EXPIRED':
      case 'CANCELLED':
        return 'error';
      default:
        return 'primary';
    }   
  }       

  function maskKey(key, start = 10, end = 12) {
    const visibleStart = key.slice(0, start);
    const visibleEnd = key.slice(-end);
    const masked = '*'.repeat(key.length - start - end);
    return `${visibleStart}${masked}${visibleEnd}`;
  }
  

  return (
    <Page title="Transaction Confirmation">
      <Container maxWidth="lg">
        <Card sx={{ 
          borderRadius: 2, 
          boxShadow: '0 0 10px rgba(0,0,0,0.05)', 
          overflow: 'hidden',
          mb: 4
        }}>
          <CardContent sx={{ p: { xs: 2, md: 4 } }}>
           
              <>
                <Typography variant="h4" gutterBottom component="div" textAlign="center" sx={{ 
                  fontWeight: 700, 
                  color: showSuccess ? 'success.main' : 'text.primary',
                  mb: 4
                }}>
                  {showSuccess ? "Transaction Successful" : "Payment Confirmation"}
                </Typography>

                {/* Success Page */}
                {showSuccess ? (
                  <Paper elevation={0} sx={{ 
                    p: 3, 
                    border: '1px solid #e0e0e0', 
                    borderRadius: 2,
                    backgroundColor: 'rgba(0, 171, 85, 0.04)',
                    borderColor: 'rgba(0, 171, 85, 0.2)'
                  }}>
                    <Box sx={{ display: 'flex', justifyContent: 'center', mb: 3 }}>
                      <Iconify icon="eva:checkmark-circle-fill" sx={{ width: 64, height: 64, color: 'success.main' }} />
                    </Box>
                    
                    <Divider sx={{ mb: 3 }} />
                    
                    <Table>
                        <TableBody>
                          <TableRow>
                            <TableCell>
                              <Typography sx={{ fontWeight: 'bold', fontSize: 16 }} color="text.secondary">Ref ID</Typography>
                            </TableCell>
                            <TableCell>{transaction.transId}</TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell>
                              <Typography sx={{ fontWeight: 'bold', fontSize: 16 }} color="text.secondary">Receiver Address</Typography>
                            </TableCell>
                            <TableCell sx={{ wordBreak: 'break-all' }}>{maskKey(transaction.receiver_address, 10, 12)}</TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell>
                              <Typography sx={{ fontWeight: 'bold', fontSize: 16 }} color="text.secondary">Sent Amount</Typography>
                            </TableCell>
                            <TableCell>{transaction.send_amount} {transaction.send_asset}</TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell>
                              <Typography sx={{ fontWeight: 'bold', fontSize: 16 }} color="text.secondary">Received Amount</Typography>
                            </TableCell>
                            <TableCell>{transaction.receive_amount} {transaction.receive_currency}</TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell>
                              <Typography sx={{ fontWeight: 'bold', fontSize: 16 }} color="text.secondary">Exchange Rate</Typography>
                            </TableCell>
                            <TableCell>1 {transaction.send_asset} = {transaction.ex_rate} {transaction.receive_currency}</TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell>
                              <Typography sx={{ fontWeight: 'bold', fontSize: 16 }} color="text.secondary">Transaction Fees</Typography>
                            </TableCell>
                            <TableCell>{transaction.fee} {transaction.receive_currency}</TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell>
                              <Typography sx={{ fontWeight: 'bold', fontSize: 16 }} color="text.secondary">Transaction Hash</Typography>
                            </TableCell>
                            <TableCell sx={{ wordBreak: 'break-all' }}>{transaction.hash}</TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell>
                              <Typography sx={{ fontWeight: 'bold', fontSize: 16 }} color="text.secondary">Transaction Date</Typography>
                            </TableCell>
                            <TableCell>{transaction.created_at}</TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell>
                              <Typography sx={{ fontWeight: 'bold', fontSize: 16 }} color="text.secondary">Pay in Status</Typography>
                            </TableCell>
                            <TableCell>{transaction.pay_in_status}</TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell>
                              <Typography sx={{ fontWeight: 'bold', fontSize: 16 }} color="text.secondary">Pay out Status</Typography>
                            </TableCell>
                            <TableCell>{transaction.status}</TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell>
                              <Typography sx={{ fontWeight: 'bold', fontSize: 16 }} color="text.secondary">Final Status</Typography>
                            </TableCell>
                            <TableCell>
                              <Label variant="filled" color="success" sx={{ width: 'fit-content' }}>SUCCESSFUL</Label>
                            </TableCell>
                          </TableRow>
                        </TableBody>
                      </Table>
                    
                    <Box sx={{ mt: 4, textAlign: 'center' }}>
                      <Button 
                        variant="contained" 
                        color="primary" 
                        onClick={() => navigate('/dashboard/app')}
                        startIcon={<Iconify icon="eva:home-fill" />}
                        sx={{ mr: 2 }}
                      >
                        Go to Home
                      </Button>
                      <Button 
                        variant="outlined"
                        onClick={() => navigate('/dashboard/payments')}
                        startIcon={<Iconify icon="eva:plus-circle-fill" />}
                      >
                        New Transaction
                      </Button>
                    </Box>
                  </Paper>
                ) : (
                  <>
                    {transaction.status?.toUpperCase() === "PENDING" && (
                      <Paper elevation={0} sx={{ p: 3, border: '1px solid #e0e0e0', borderRadius: 2 }}>
                        <Grid container spacing={3} alignItems="center">
                          {/* Payment Details */}
                          <Grid item xs={12} md={8}>
                            <Typography variant="h6" gutterBottom sx={{ fontWeight: 600 }}>
                              Payment Instructions
                            </Typography>
                            
                            <Box sx={{ mb: 3 }}>
                              <Typography variant="body1" gutterBottom>
                                Please send the following amount:
                              </Typography>
                              <Typography variant="h5" sx={{ fontWeight: 700, color: 'primary.main', my: 2 }}>
                                {transaction.send_amount} {transaction.send_asset}
                              </Typography>
                              <Typography variant="body1" gutterBottom>
                                To this address:
                              </Typography>
                              <Paper sx={{ 
                                p: 2, 
                                backgroundColor: 'rgba(145, 158, 171, 0.08)', 
                                borderRadius: 1,
                                wordBreak: 'break-all'
                              }}>
                                <Typography variant="body2" fontFamily="monospace">
                                  {transaction.receiver_address}
                                </Typography>
                              </Paper>
                              
                              {transaction.id && (
                                <Box sx={{ mt: 2 }}>
                                  <Typography variant="body1" gutterBottom>
                                    MEMO (important):
                                  </Typography>
                                  <Paper sx={{ 
                                    p: 2, 
                                    backgroundColor: 'rgba(255, 171, 0, 0.08)', 
                                    borderRadius: 1
                                  }}>
                                    <Typography variant="body2" fontFamily="monospace" fontWeight={600}>
                                      {transaction.id}
                                    </Typography>
                                  </Paper>
                                </Box>
                              )}
                            </Box>
                            
                            <Box sx={{ mb: 3 }}>
                              <Grid container spacing={2}>
                                <Grid item xs={6}>
                                  <Typography variant="body2" color="text.secondary">Status:</Typography>
                                  <Label
                                    variant="filled"
                                    color={getColorForStatus(transaction?.status)}
                                    sx={{ mt: 1 }}
                                  >
                                    {transaction?.status.toUpperCase()}
                                  </Label>
                                </Grid>
                                <Grid item xs={6}>
                                  <Typography variant="body2" color="text.secondary">Pay in status:</Typography>
                                  <Label
                                    variant="filled"
                                    color={getColorForStatus(transaction?.pay_in_status)}
                                    sx={{ mt: 1 }}
                                  >
                                    {transaction?.pay_in_status.toUpperCase()}
                                  </Label>
                                </Grid>
                              </Grid>
                            </Box>
                          </Grid>

                          {/* QR Code */}
                          <Grid item xs={12} md={4} sx={{ textAlign: "center" }}>
                            <Paper elevation={0} sx={{ 
                              p: 3, 
                              border: '1px solid #e0e0e0', 
                              borderRadius: 2,
                              display: 'flex',
                              flexDirection: 'column',
                              alignItems: 'center'
                            }}>
                              <Typography variant="subtitle2" gutterBottom sx={{ mb: 2 }}>
                                Scan QR Code
                              </Typography>
                              <Box sx={{ 
                                p: 2, 
                                backgroundColor: '#fff', 
                                borderRadius: '8px',
                                border: '1px solid #eee',
                                display: 'inline-block'
                              }}>
                                <QRCode 
                                  value={transaction.receiver_address || ''} 
                                  size={150} 
                                  level="H" 
                                  includeMargin
                                  renderAs="svg"
                                />
                              </Box>
                              <Typography variant="caption" sx={{ mt: 2, color: 'text.secondary' }}>
                                Scan to copy address
                              </Typography>
                            </Paper>
                          </Grid>
                        </Grid>
                        
                        <Box sx={{ mt: 4, display: 'flex', justifyContent: 'center' }}>
                          {transactionReload ? (
                            <Button variant="contained" disabled>
                              <CircularProgress size={24} sx={{ mr: 1 }} />
                              Checking Payment...
                            </Button>
                          ) : (
                            <>

                            {(transaction?.hash !== null && transaction?.hash !== "")? 
                              (<Button 
                                variant="contained" 
                                color="primary" 

                                onClick={() => confirmTrxnPayment(transaction)}
                                startIcon={<Iconify icon="eva:checkmark-circle-2-fill" />}
                                sx={{ mr: 2 }}
                              >
                                I've Made Payment
                              </Button>): (
                                <Button
                                   variant="contained"
                                   color="primary"
                                   onClick={() => confirmTrxnPayment(transaction)}
                                   startIcon={<Iconify icon="eva:checkmark-circle-2-fill" />}
                                   sx={{ mr: 2 }}
                                  >
                                  I've Made Payment
                                </Button>)
                              }
                              
                              <Button 
                                variant="outlined" 
                                color="error" 
                                onClick={() => confirmTrxnCancel(transaction)}
                                startIcon={<Iconify icon="eva:close-circle-fill" />}
                              >
                                Cancel Order
                              </Button>
                            </>
                          )}
                        </Box>
                      </Paper>
                    )}

                    {["FAILED", "EXPIRED", "CANCELLED"].includes(transaction.status?.toUpperCase()) && (
                      <Paper elevation={0} sx={{ 
                        p: 4, 
                        border: '1px solid #e0e0e0', 
                        borderRadius: 2,
                        backgroundColor: 'rgba(255, 72, 66, 0.08)',
                        borderColor: 'rgba(255, 72, 66, 0.2)',
                        textAlign: 'center'
                      }}>
                        <Box sx={{ display: 'flex', justifyContent: 'center', mb: 3 }}>
                          <Iconify icon="eva:alert-circle-fill" sx={{ width: 64, height: 64, color: 'error.main' }} />
                        </Box>
                        
                        <Typography variant="h6" sx={{ mb: 2, color: 'error.main' }}>
                          Transaction {transaction.status?.toUpperCase()}
                        </Typography>
                        
                        <Typography variant="body1" sx={{ mb: 4 }}>
                          Your transaction could not be completed.
                        </Typography>
                        
                        <Button 
                          variant="contained" 
                          color="primary" 
                          onClick={() => navigate('/dashboard/app')}
                          startIcon={<Iconify icon="eva:home-fill" />}
                          sx={{ mr: 2 }}
                        >
                          Go to Home
                        </Button>
                        <Button 
                          variant="outlined"
                          onClick={() => navigate('/dashboard/payments')}
                          startIcon={<Iconify icon="eva:plus-circle-fill" />}
                        >
                          Try Again
                        </Button>
                      </Paper>
                    )}
                  </>
                )}

                {!["PENDING", "SUCCESSFUL", "FAILED", "EXPIRED", "CANCELLED"].includes(transaction.status?.toUpperCase()) && (
                  <Paper elevation={0} sx={{ 
                    p: 4, 
                    border: '1px solid #e0e0e0', 
                    borderRadius: 2,
                    backgroundColor: 'rgba(24, 144, 255, 0.08)',
                    textAlign: 'center'
                  }}>
                    <Box sx={{ display: 'flex', justifyContent: 'center', mb: 3 }}>
                      <CircularProgress />
                    </Box>
                    
                    <Typography variant="h6" sx={{ mb: 2, color: 'primary.main' }}>
                      Transaction Processing
                    </Typography>
                    
                    <Label
                      variant="filled"
                      color="info"
                      sx={{ mb: 3 }}
                    >
                      {transaction.status ? transaction.status.toUpperCase() : "PROCESSING"}
                    </Label>
                    
                    <Typography variant="body1" sx={{ mb: 4 }}>
                      Your transaction is currently in <strong>{transaction.status?.toUpperCase() || "PROCESSING"}</strong> state.
                      Please wait while we process it. You will be notified once the status changes.
                    </Typography>
                    
                    <Button 
                      variant="contained" 
                      color="primary" 
                      onClick={reloadTransaction}
                      startIcon={<Iconify icon="eva:refresh-fill" />}
                      disabled={transactionReload}
                    >
                      {transactionReload ? 'Refreshing...' : 'Refresh Status'}
                    </Button>
                  </Paper>
                )}
              </>
            
          </CardContent>
        </Card>





        
        <Dialog 
          open={trxnDelete} 
          onClose={() => {
            setTrxnDeleteDetails({});
            setTrxnDelete(false);
          }}
          PaperProps={{
            sx: { borderRadius: 2, p: 1 }
          }}
        >
          <DialogTitle sx={{ fontWeight: 600 }}>
            Cancel Transaction
          </DialogTitle>
          <DialogContent>
            <Typography variant="body1" sx={{ mb: 2 }}>
              Are you sure you want to cancel this transaction?
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Transaction ID: {trxnDeleteDetails?.transId || trxnDeleteDetails?.id}
            </Typography>
          </DialogContent>

          <DialogActions sx={{ px: 3, pb: 3 }}>
            <Button 
              onClick={() => {
                setTrxnDeleteDetails({});
                setTrxnDelete(false);
              }}
              sx={{ mr: 1 }}
            >
              No, Keep Transaction
            </Button>
            <Button 
              variant="contained" 
              color="error"
              disabled={isSubmitting}
              onClick={() => confirmToDelete(trxnDeleteDetails)}
            >
              Yes, Cancel Transaction
            </Button>
          </DialogActions>
        </Dialog>



        <Dialog 
          open={trxnConfirm} 
          onClose={() => {
            setTrxnConfirmDetails({});
            setTrxnConfirm(false);
          }}
          PaperProps={{
            sx: { borderRadius: 2, p: 1 }
          }}
        >
          <DialogTitle sx={{ fontWeight: 600 }}>
            Confirm Transaction Payment
          </DialogTitle>
          <DialogContent>
             <Typography variant="body1">
              Provide the transaction hash to check your payment
            </Typography>
            <div className="form-block">
              <Typography mt={3} mb={1} variant="p" className="form-block-title">Transaction Hash</Typography> 
              <TextField  name="transaction_hash" label=""  
                          fullWidth style={{ marginBottom: 1 }} 
                          value={transactionHash}
                          onChange={(e) => setTransactionHash(e.target.value)} />
            </div>
          </DialogContent>
          <DialogActions sx={{ px: 3, pb: 3 }}>
            <Button 
              onClick={() => {
                setTrxnConfirmDetails({});
                setTrxnConfirm(false);
              }}
              sx={{ mr: 1 }}
            >
              Cancel
            </Button>
            <Button 
              variant="contained" 
              color="primary"
              disabled={trxnProcessingConfirm}
              onClick={() => confirmTheTransaction(trxnConfirmDetails)}
            >
               {trxnProcessingConfirm ? 'Confirming Payment...' : 'Yes, Confirm Payment'}
            </Button>
          </DialogActions>
        </Dialog>
      </Container>
    </Page>
  );
};

export default Confirmation;
