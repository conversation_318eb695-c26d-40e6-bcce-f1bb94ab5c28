import { useState, useEffect } from 'react';
import { Container, Grid, Typography, Card, CardContent, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, TablePagination } from '@mui/material';
import { Link as RouterLink, useNavigate } from 'react-router-dom';
import Page from '../components/Page';
import { getTransactions } from '../services';
import { AppWidgetSummary } from '../sections/@dashboard/app';
import Scrollbar from '../components/Scrollbar';
import Label from '../components/Label';

export default function DashboardApp() {
  const [transactions, setTransactions] = useState([]);
  const [sentFiat, setSentFiat] = useState(0);
  const [sentUSDT, setSentUSDT] = useState(0);
  const [sentUSDC, setSentUSDC] = useState(0);
  const [receivedKES, setReceivedKES] = useState(0);
  const [totalSentTransactions, setTotalSentTransactions] = useState(0);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(5);

    const navigate = useNavigate();
  

  useEffect(() => {
    const fetchTransactions = async () => {
      const transact = await getTransactions();
      if (transact.length > 0) {
        const successfulTransactions = transact.filter(tx => tx.status.toUpperCase() === 'SUCCESSFUL');

        setTransactions(successfulTransactions);
        setTotalSentTransactions(successfulTransactions.length);

        let { fiatTotal, usdtTotal , usdcTotal , kesReceived} = 0;

        successfulTransactions.forEach(tx => {
          if (tx.send_asset === 'USDT') usdtTotal += parseFloat(tx.send_amount);
          if (tx.send_asset === 'USDC') usdcTotal += parseFloat(tx.send_amount);
          if (tx.receive_currency === 'KES') kesReceived += parseFloat(tx.receive_amount);
          fiatTotal += parseFloat(tx.send_amount);
        });

      //  setSentFiat(fiatTotal);
      //  setSentUSDT(usdtTotal);
      //  setSentUSDC(usdcTotal);
      //  setReceivedKES(kesReceived);
      }
    };

    fetchTransactions();
  }, []);

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const navigatingToSection = async (transId) => {
    navigate(`/dashboard/confirmation?id=${transId}`);
  }

  return (
    <Page title="Dashboard">
      <Container maxWidth="xl">
      

        <Grid container spacing={2}>
          <Grid item xs={12} sm={6} md={3}>
            <AppWidgetSummary title="Sent Transactions" total={totalSentTransactions} color="primary" icon={`https://via.placeholder.com/50x50`} />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <AppWidgetSummary title="Sent Fiat" total={`$${sentFiat.toFixed(2)}`} color="info" icon={`https://via.placeholder.com/50x50`} />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <AppWidgetSummary title="Sent USDT" total={`${sentUSDT.toFixed(2)} USDT`} color="success" icon={`https://via.placeholder.com/50x50`} />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <AppWidgetSummary title="Sent USDC" total={`${sentUSDC.toFixed(2)} USDC`} color="warning" icon={`https://via.placeholder.com/50x50`} />
          </Grid>
         
        </Grid>

        <Typography variant="h6" sx={{ mt: 5, mb: 2 }}>
          Last successful transactions
        </Typography>

        <Card>
          <Scrollbar>
            <TableContainer sx={{ minWidth: 800 }}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>ID</TableCell>
                    <TableCell>Provider</TableCell>
                    <TableCell>Sent</TableCell>
                    <TableCell>Received</TableCell>
                    <TableCell>Date</TableCell>
                    <TableCell>Status</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {transactions.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage).map((row) => (
                    <TableRow onClick={()=>navigatingToSection(row.transId)} key={row.id}>
                      <TableCell>{row.id}</TableCell>
                      <TableCell>{row.service_name}</TableCell>
                      <TableCell>{row.send_asset} {row.send_amount}</TableCell>
                      <TableCell>{row.receive_currency} {row.receive_amount}</TableCell>
                      <TableCell>{row.created_at}</TableCell>
                      <TableCell>
                        <Label variant="ghost" color="success">
                          COMPLETED
                        </Label>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Scrollbar>

          <TablePagination
            rowsPerPageOptions={[5, 10, 25]}
            component="div"
            count={transactions.length}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={handleChangePage}
            onRowsPerPageChange={handleChangeRowsPerPage}
          />
        </Card>
      </Container>
    </Page>
  );
}
