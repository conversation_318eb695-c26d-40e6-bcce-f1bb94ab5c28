import React, { useState } from 'react';
import { Link as RouterLink, useNavigate, useLocation } from 'react-router-dom';
// @mui
import {
  styled,
  Container,
  Typography,
  Box,
  TextField,
  Button,
  Link,
  Stack,
  InputAdornment,
  IconButton,
  CircularProgress
} from '@mui/material';
// icons
import VisibilityIcon from '@mui/icons-material/Visibility';
import VisibilityOffIcon from '@mui/icons-material/VisibilityOff';
import { toast } from 'react-toastify';
import { post } from '../api';
// hooks
import useResponsive from '../hooks/useResponsive';
// components
import Page from '../components/Page';
import Logo from '../components/Logo';

// ----------------------------------------------------------------------

const RootStyle = styled('div')(({ theme }) => ({
  minHeight: '100vh',
  display: 'flex',
  backgroundColor: '#F8F9FA',
}));

const HeaderStyle = styled('header')(({ theme }) => ({
  top: 0,
  zIndex: 9,
  lineHeight: 0,
  width: '100%',
  display: 'flex',
  alignItems: 'center',
  position: 'absolute',
  padding: theme.spacing(3),
  justifyContent: 'space-between',
  [theme.breakpoints.up('md')]: {
    padding: theme.spacing(5),
  },
}));

const ContentStyle = styled(Box)(({ theme }) => ({
  maxWidth: 480,
  margin: 'auto',
  display: 'flex',
  flexDirection: 'column',
  justifyContent: 'center',
  padding: theme.spacing(4, 3),
  borderRadius: theme.shape.borderRadiusMd,
  backgroundColor: theme.palette.background.paper,
  boxShadow: '0 8px 40px rgba(0,0,0,0.12)',
}));

// ----------------------------------------------------------------------

export default function Login() {

  const location =  useLocation()
  const navigate = useNavigate();
  const smUp = useResponsive('up', 'sm');
  const mdUp = useResponsive('up', 'md');
  
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const handleLogin = async (e) => {
    e.preventDefault();
    
    if (!email || !password) {
      toast.error('Please enter both email and password');
      return;
    }
    
    setIsSubmitting(true);
    try {

      const response = await post('accounts/login', { email, password });
      if (response.status === 200) {

        localStorage.setItem('userObject', JSON.stringify(response.data));
        localStorage.setItem('authJWT', response.data.jwt);
        localStorage.setItem('serviceToken', response.data.jwt);
        if (response.data.user_type) {
          localStorage.setItem('userType', response.data.user_type);
        }
        
        if (response.data.kyc_status) {
          localStorage.setItem('kycStatus', response.data.kyc_status);
        }
        

        toast.success('Login successful!');
        const params = new URLSearchParams(location.search);
        const nextPath = params.get("next");


        if (nextPath && nextPath === 'dashboard/payments') {
          return navigate(`/${nextPath}`, { replace: true });
        }
        navigate('/dashboard');


      } else {
        
        toast.error('Login failed. Please check your credentials.');
      }
    } catch (error) {

      console.error('Login error:', error);
      toast.error('Error during login. Please try again later.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Page title="Login">
      <RootStyle>
        <Container maxWidth="sm" sx={{ display: 'flex', alignItems: 'center', height: '100vh' }}>
          <Box sx={{ 
            width: '100%',
            mx: 'auto', 
            overflow: 'hidden',
            borderRadius: 3,
            boxShadow: '0 10px 40px rgba(0,0,0,0.1)',
            bgcolor: 'white', 
            p: { xs: 3, md: 5 }
          }}>
            {/* Login Form */}
            <Box sx={{ mb: 4, textAlign: 'center' }}>
              <Box sx={{ display: 'flex', justifyContent: 'center', mb: 3 }}>
                <Logo />
              </Box>
              <Typography variant="h4" gutterBottom fontWeight="700" color="#1F50C2">
                Welcome Back
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Sign in to continue to Liquidity Rail
              </Typography>
            </Box>

            <Box component="form" onSubmit={handleLogin}>
              <TextField
                label="Email Address"
                fullWidth
                variant="outlined"
                margin="normal"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="<EMAIL>"
                type="email"
                autoFocus
                sx={{ mb: 2 }}
              />
        
              <TextField
                label="Password"
                type={showPassword ? 'text' : 'password'}
                fullWidth
                variant="outlined"
                margin="normal"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={() => setShowPassword(!showPassword)}
                        edge="end"
                      >
                        {showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                      </IconButton>
                    </InputAdornment>
                  )
                }}
                sx={{ mb: 1 }}
              />
        
              <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 3 }}>
                <Link 
                  component={RouterLink}
                  to="/password-reset"
                  variant="body2" 
                  sx={{ color: '#1F50C2', textDecoration: 'none', '&:hover': { textDecoration: 'underline' } }}
                >
                  Forgot password?
                </Link>
              </Box>

              <Button
                type="submit"
                variant="contained"
                fullWidth
                disabled={isSubmitting}
                sx={{ 
                  py: 1.5, 
                  backgroundColor: '#1F50C2', 
                  '&:hover': { backgroundColor: '#0F2B77' },
                  borderRadius: '8px',
                  fontWeight: 600
                }}
              >
                {isSubmitting ? (
                  <CircularProgress size={24} color="inherit" />
                ) : (
                  'Sign In'
                )}
              </Button>

              <Typography variant="body2" align="center" sx={{ mt: 3 }}>
                Don't have an account?{' '}
                <Link 
                  variant="subtitle2" 
                  component={RouterLink} 
                  to="/register"
                  sx={{ color: '#1F50C2', fontWeight: 600 }}
                >
                  Get started
                </Link>
              </Typography>
            </Box>
          </Box>
        </Container>
      </RootStyle>
    </Page>
  );
}
