import * as Yup from 'yup';
import React, { useState } from 'react';
import { useNavigate, Link as RouterLink } from 'react-router-dom';
import { useForm } from 'react-hook-form';


import { yupResolver } from '@hookform/resolvers/yup';
import { <PERSON><PERSON>, Card, CardContent, Stack, Container, Grid, Typography, Select, OutlinedInput, MenuItem, FormControl, InputLabel } from '@mui/material';
import { DataGrid } from '@mui/x-data-grid';

import { LoadingButton } from '@mui/lab';
import Page from '../components/Page';
import { RHFTextField } from '../components/hook-form';
import Iconify from '../components/Iconify';
import  ModalPop from '../sections/modals/MainModalView'

const columns = [
  { field: 'id', headerName: 'ID', width: 70 },
  { field: 'token', headerName: 'Token', width: 200},
  { field: 'address', headerName: 'Address', width: 280},
];

const rows = [
  { id: 1, token: 'USDT(Tron)', address: '8989d3bc-7b89-48e0-b78a-d9567d1df904' },
  { id: 2, token: 'USDT(Stellar)', address: '8989d3bc-7b89-48e0-b78a-d9567d1df904' },
];

const rows2 = [
  { id: 1, network: 'Mobile Money', networkCode: '8989d3b', currency: 'UGX' },
  { id: 2, network: 'Mpesa', networkCode: '8989d3bc', currency: 'KES' },
];

const Wallets = () => {


  const navigate = useNavigate();
  const [modelData, setModelData] = useState({});
  const [open, setOpen] = useState(false);
  const [modelType, setModelType] = useState('');

  const [services] = useState([
    { label: "Airtime", value: "airtime" },
    { label: "Electricity", value: "electricity" },
    { label: "Water", value: "water" },
  ]);
  const [paymentAssets] = useState([
    { label: "BTC", value: "btc" },
    { label: "ETH", value: "eth" },
    { label: "XLM", value: "xlm" },
  ]);
  const [countries] = useState([
    { label: "United States (USD)", value: "us" },
    { label: "United Kingdom (GBP)", value: "uk" },
    { label: "Canada (CAD)", value: "ca" },
  ]);

  const [providers] = useState([
    { label: "MTN Uganda", value: "mtn", rate: "1 USD = 3800", fee: "30UGX" },
    { label: "Airtel Uganda", value: "airtel", rate: "1 USD = 3700", fee: "25UGX" },
    { label: "Vodafone Ghana", value: "vodafone", rate: "1 USD = 6.1", fee: "0.5GHS" },
  ]);

  const [selectedProviderRate, setSelectedProviderRate] = useState("");
  const showWalletModel = async (optionTrend) => {
    setOpen(true)
    setModelType(optionTrend);
    setModelData({ "type": optionTrend, "content": "" })
  }

  const onModelComplete = async (options) => {
     console.log("model compele", options)
  }


  return (
     <Page title="">
      <ModalPop openModel={open} 
              modalToggler={setOpen} 
              theModelType={modelType} 
              dataContent={modelData}
              onComplete={onModelComplete} /> 

      <Container>
        <div className={"item-form-top-10"}> 
            <Stack direction="row" alignItems="center" justifyContent="space-between" mb={5}>
              <Typography variant="h6" gutterBottom>
                Wallets
              </Typography>

              <Button variant="contained" component={RouterLink} 
                      onClick={() => { showWalletModel('wallets')}}
                      to="#" startIcon={<Iconify icon="eva:plus-fill" />}>
                  Add Wallet
              </Button>
            </Stack>
            
            <DataGrid
                      className="fullWidth"
                      rows={rows}
                      columns={columns}
                      initialState={{
                        pagination: {
                          paginationModel: { page: 0, pageSize: 5 },
                        },
                      }}
                      pageSizeOptions={[5, 10]}
                      checkboxSelection
                    />
           </div>         
     </Container>
    </Page>  
  );
};

export default Wallets;
