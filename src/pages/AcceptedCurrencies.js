import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>er, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Dialog, DialogTitle, DialogContent, DialogActions, TextField, Select, MenuItem, IconButton } from '@mui/material';
import { DataGrid } from '@mui/x-data-grid';
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import Iconify from '../components/Iconify';
import { post, get } from '../api';
import NewProviderAcceptedAssetPopup from '../components/settingsForms/NewProviderAcceptedAssetPopup';

export default function AcceptedCurrencies() {

    const [addresses, setAddresses] = useState([]);
    const [open, setOpen] = useState(false);
    const [isOpen, setIsOpen] = useState(false);
    const [formData, setFormData] = useState({
        address: '',
        chain: 'STELLAR'
    });

    useEffect(() => {
        fetchAddresses();
    }, []);

    const fetchAddresses = async () => {
        const response = await get(`accounts/getAddresses`);
        setAddresses(response.data || []);
    };

    const handleChange = (e) => {
        setFormData({ ...formData, [e.target.name]: e.target.value });
    };

    const handleSubmit = async () => {
        await post(`accounts/addAddress`, formData);
        toast.success('Address added successfully!');
        setOpen(false);
        fetchAddresses();
    };

    const handleDelete = async (id) => {
        await get(`accounts/deleteAddress/${id}`);
        toast.success('Address deleted successfully!');
        fetchAddresses();
    };

    const columns = [
        { field: 'address_id', headerName: 'ID', width: 70 },
        { field: 'chain', headerName: 'Chain', width: 150 },
        { field: 'address', headerName: 'Asset Name', width: 450 },
        {
            field: 'actions',
            headerName: 'Actions',
            width: 120,
            renderCell: (params) => (
                <>
                    <IconButton onClick={() => handleDelete(params.row.address_id)}>
                        <Iconify icon="eva:trash-2-outline" color="red" />
                    </IconButton>
                </>
            )
        }
    ];

    
    const onClose = () => {
        setIsOpen(false)
    }
     
    const onSuccess = async() => {
        setIsOpen(false)
    }
     
    const onOpen = () => {
         setIsOpen(true)
    }
     

    return (
        <Container>
            
            <NewProviderAcceptedAssetPopup isOpen={isOpen} onClose={() => onClose()} onSuccess={() => onSuccess()} />
            <Stack direction="row" alignItems="center" justifyContent="space-between" mb={5}>
                <Typography variant="h4">Accepted Currencies</Typography>
                <Button variant="contained" onClick={() => onOpen()} startIcon={<Iconify icon="eva:plus-fill" />}>Add New</Button>
            </Stack>

            {/* Fix: Use getRowId to ensure a unique ID */}
            <DataGrid rows={addresses} columns={columns} pageSize={5} getRowId={(row) => row.address_id} />


        </Container>
    );
}
