import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
    AppBar,
    Toolbar,
    Typography,
    Button,
    Box,
    Container,
    Grid,
    Link,
    Paper,
    Card,
    CardContent,
    IconButton,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    TextField,
    Menu,
    MenuItem,
    useMediaQuery,
    Drawer,
    List,
    ListItem,
    ListItemText
} from '@mui/material';
import { Facebook, Twitter, LinkedIn, Instagram, Menu as MenuIcon, Security, Speed, Payments, Public, Send } from '@mui/icons-material';
import { useTheme } from '@mui/material/styles';
import Logo from '../components/Logo';
import "../assets/css/style.css";

const Tab = ({ label, isActive, onClick }) => (
  <button
    onClick={onClick}
    className={`flex items-center gap-2 px-6 py-3 rounded-full transition-all duration-300 default-casestudy-btn ${
      isActive 
        ? 'bg-blue-500 text-white shadow-lg default-casestudy-btn-active' 
        : 'bg-white text-gray-600 hover:bg-gray-50'
    }`}
  >
    <span className="font-medium">{label}</span>
  </button>
);

// Import the about image asset
 
const Header = () => {

  const [user, setUser] = useState(null);
  const [userAuth, setUserAuth] = useState(null);
  const [userCheck, setUserCheck] = useState(true);
  const [loginDialogOpen, setLoginDialogOpen] = useState(false);
  const [openForm, setopenForm] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('wallets');
  
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const navigate = useNavigate();
  // Listen for "unauthorized" events (fired by Axios interceptor on 401)
  // useEffect(() => {

  //   const handleUnauthorized = () => {
  //     setLoginDialogOpen(true);
  //   };
    
  //   const handleShowLoginPopup = (e) => {
  //     setLoginDialogOpen(true);
  //     // Store the return path if provided
  //     if (e.detail && e.detail.returnTo) {
  //       sessionStorage.setItem('afterLoginRedirect', e.detail.returnTo);
  //     }
  //   };
    
  //   window.addEventListener('unauthorized', handleUnauthorized);
  //   window.addEventListener('show-login-popup', handleShowLoginPopup);
    
  //   return () => {
  //     window.removeEventListener('unauthorized', handleUnauthorized);
  //     window.removeEventListener('show-login-popup', handleShowLoginPopup);
  //   };
  // }, []);



  // Check localStorage for JWT on mount
  useEffect(() => {

    const userAuthToken = localStorage.getItem('authJWT');
    setUserAuth(userAuthToken);


    const userObject = localStorage.getItem('userObject');
    if (userObject) {
      try {
        
        const decoded = JSON.parse(userObject);
        setUser(decoded);

      } catch (error) {
        console.error('Invalid user object in local storage');
        localStorage.removeItem('userObject');
      }
    }
  }, []); // userCheck

  const handleLogout = () => {
    localStorage.removeItem('userObject');
    setUser(null);
  };

  const toggleUserCheck = () => {
    setUserCheck(!userCheck);
  };

  // on successful login
  const onLogin = (e) => {
    setUser(e);
    setopenForm(false);
    const returnPath    = sessionStorage.getItem('afterLoginRedirect');
    const userAuthToken = localStorage.getItem('authJWT');
    setUserAuth(userAuthToken);
    if (returnPath) {
      sessionStorage.removeItem('afterLoginRedirect');
      return navigate(returnPath);
    }

  }

  const closeForm = () => {
    setopenForm(false);
  }
  
  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  const scrollToSection = (sectionId) => {
    const section = document.getElementById(sectionId);
    if (section) {
      section.scrollIntoView({ behavior: 'smooth' });
    }
    setMobileMenuOpen(false);
  };
  
  const navigateToLogin = () => {
    navigate('/login');
    setMobileMenuOpen(false);
  };

  const APIDocumentationRedirect = () => {
    return window.open('https://payments-doc.muda.tech/liquidity/intro', '_blank'); 
  };

  const tabs = [
    { id: 'wallets', label: 'Wallets', icon: "" },
    { id: 'fintechs-and-aggregators', label: 'Fintechs and Aggregators', icon: "" },
    { id: 'remittance', label: 'Remittance', icon: "" },
  ];

  const onLoginOpen = () => {
    setopenForm(true);
  }
  
  return (
    <> 
      <AppBar 
        position="static" 
        color="transparent" 
        className="main-header"
        elevation={0}
      >
        <Container maxWidth="xl">
          <Toolbar sx={{ justifyContent: 'space-between', py: 2 }}>
            {/* Replace text logo with actual Logo component */}
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Logo sx={{ height: 90, mr: 2 }} />
            </Box>
            <Box sx={{ display: { xs: 'none', md: 'flex' }, gap: 2 }}>
              <Button 
                variant="text" 
                color="inherit" 
                onClick={() => scrollToSection('hero')}
                sx={{ 
                  color: 'white',
                  '&:hover': {
                    backgroundColor: 'rgba(255, 255, 255, 0.1)'
                  }
                }}
              >
                Home
              </Button>
              <Button 
                variant="text" 
                color="inherit" 
                onClick={() => scrollToSection('about')}
                sx={{ 
                  color: 'white',
                  '&:hover': {
                    backgroundColor: 'rgba(255, 255, 255, 0.1)'
                  }
                }}
              >
                About
              </Button>
              <Button 
                variant="text" 
                color="inherit" 
                component="a"
                href="https://payments-doc.muda.tech/liquidity/intro"
                target="_blank"
                rel="noopener noreferrer"
                sx={{ 
                  color: 'white',
                  '&:hover': {
                    backgroundColor: 'rgba(255, 255, 255, 0.1)'
                  }
                }}
              >
                Docs
              </Button>
              {(userAuth != null) ? (
                <Button 
                  variant="contained" 
                  color="primary"
                  onClick={() => navigate('/dashboard')}
                  sx={{ 
                    fontWeight: 'bold'
                  }}
                >
                  Dashboard
                </Button>
              ) : (
                <Button 
                  variant="outlined" 
                  color="primary"
                  onClick={() => navigate('/login')}
                  sx={{ 
                    color: 'white',
                    borderColor: 'white',
                    '&:hover': {
                      borderColor: '#1F50C2',
                      backgroundColor: 'rgba(31, 80, 194, 0.1)'
                    }
                  }}
                >
                  Login
                </Button>
              )}
            </Box>
            
            {/* Mobile menu button */}
            <IconButton 
              color="inherit" 
              onClick={toggleMobileMenu} 
              sx={{ 
                display: { xs: 'block', md: 'none' },
                color: 'white'
              }}
            >
              <MenuIcon />
            </IconButton>
          </Toolbar>
        </Container>
      </AppBar>

      
            {/* Mobile Menu Drawer - Updated with new menu items */}
            <Drawer
              anchor="right"
              open={mobileMenuOpen}
              onClose={toggleMobileMenu}
              sx={{ 
                '& .MuiDrawer-paper': { 
                  width: '70%', 
                  maxWidth: '300px',
                  paddingTop: '20px' 
                } 
              }}
            >
              <Box sx={{ p: 2 }}>
                <Typography 
                  variant="h5" 
                  component="div" 
                  sx={{ 
                    fontWeight: 700, 
                    mb: 4
                  }}
                >
                  <span style={{ color: '#1F50C2' }}>Liquidity</span>Rail
                </Typography>
                <List component="nav">
                  <ListItem button onClick={() => scrollToSection('hero')}>
                    <ListItemText primary="Home" />
                  </ListItem>
                  <ListItem button onClick={() => scrollToSection('about')}>
                    <ListItemText primary="About" />
                  </ListItem>
                  <ListItem 
                    button 
                    component="a" 
                    href="https://payments-doc.muda.tech" 
                    target="_blank"
                    rel="noopener noreferrer"
                    onClick={toggleMobileMenu}
                  >
                    <ListItemText primary="Docs" />
                  </ListItem>
                  {user ? (
                    <ListItem button onClick={() => { navigate('/dashboard'); toggleMobileMenu(); }}>
                      <ListItemText primary="Dashboard" />
                    </ListItem>
                  ) : (
                    <ListItem button onClick={() => { navigate('/login'); toggleMobileMenu(); }}>
                      <ListItemText primary="Login" />
                    </ListItem>
                  )}
                </List>
              </Box>
            </Drawer>
         </>   

  );
};

export default Header;
