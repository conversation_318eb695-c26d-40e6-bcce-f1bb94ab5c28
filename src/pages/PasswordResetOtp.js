import { Link as RouterLink } from 'react-router-dom';
// @mui
import { styled } from '@mui/material/styles';
import { Link, Container, Typography, Box, Paper } from '@mui/material';
// hooks
import useResponsive from '../hooks/useResponsive';
// components
import Page from '../components/Page';
import Logo from '../components/Logo';
// sections
import { PasswordResetOtpForm } from '../sections/auth/password-reset-otp';

// ----------------------------------------------------------------------

const RootStyle = styled('div')(({ theme }) => ({
  minHeight: '100vh',
  display: 'flex',
  backgroundColor: '#F8F9FA',
}));

// ----------------------------------------------------------------------

export default function PasswordResetOtp() {
  const smUp = useResponsive('up', 'sm');

  return (
    <Page title="Reset Password">
      <RootStyle>
        <Container maxWidth="sm" sx={{ display: 'flex', alignItems: 'center', height: '100vh' }}>
          <Paper sx={{ 
            width: '100%',
            mx: 'auto', 
            p: { xs: 3, md: 4 },
            borderRadius: 3,
            boxShadow: '0 10px 40px rgba(0,0,0,0.1)',
            bgcolor: 'white'
          }}>
            <Box sx={{ mb: 4, textAlign: 'center' }}>
              <Box sx={{ display: 'flex', justifyContent: 'center', mb: 3 }}>
                <Logo />
              </Box>
              <Typography variant="h4" gutterBottom fontWeight="700" color="#1F50C2">
                Reset Your Password
              </Typography>
              <Typography sx={{ color: 'text.secondary' }}>
                Enter the verification code and your new password.
              </Typography>
            </Box>

            <PasswordResetOtpForm />
            
            <Typography variant="body2" align="center" sx={{ mt: 3 }}>
              Don't have an account?{' '}
              <Link 
                variant="subtitle2" 
                component={RouterLink} 
                to="/register"
                sx={{ color: '#1F50C2', fontWeight: 600 }}
              >
                Get started
              </Link>
            </Typography>
          </Paper>
        </Container>
      </RootStyle>
    </Page>
  );
}
