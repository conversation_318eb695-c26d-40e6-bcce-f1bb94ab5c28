import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Con<PERSON>er, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Dialog, DialogTitle, DialogContent, DialogActions, TextField, Select, MenuItem, IconButton } from '@mui/material';
import { DataGrid } from '@mui/x-data-grid';
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import Iconify from '../components/Iconify';
import { post, get } from '../api';
import NewProviderAddressPopup from '../components/settingsForms/NewProviderAddressPopup';


const chainOptions = ['STELLAR', 'CELO', 'TRON'];
export default function ProviderAddresses() {
  const [addresses, setAddresses] = useState([]);
  const [open, setOpen] = useState(false);
  const [walletDelete, setWalletDelete] = useState(false);
  const [walletDeleteDetails, setWalletDeleteDetails] = useState({});
  const [isOpen, setIsOpen] = useState(false);
  
  const [isSubmitting, setIsSubmitting] = useState(false);


  useEffect(() => {
    fetchAddresses();
  }, []);

  const fetchAddresses = async () => {
    try {
      const response = await get("accounts/getProviderAddresses");
      setAddresses(response?.data || []);
    } catch (error) {
      toast.error("Failed to fetch addresses");
    }
  };

  
  const confirmToDelete = async (deleteData) => {
      try {
        console.log('content data ', deleteData)
        const postData = {
                              "asset": deleteData?.asset,
                              "address": deleteData?.address,
                              "chain": deleteData?.chain
                        };
        const deletedAddress = await post(`accounts/deactivateAddress`, postData);
        if (deletedAddress.status === 200 || deletedAddress.status === 200) {
            toast.success(`${deletedAddress.message}`);
            setWalletDeleteDetails({}); 
            fetchAddresses();
          } else {
            toast.error(`${deletedAddress.message}`);
          }
      } catch (error) {

        console.log("error here ", error)
        toast.error("Error deactivating address");
      }
  }


  const columns = [
    { field: "wallet_name", headerName: "Name", width: 70 },
    { field: "chain", headerName: "Chain", width: 150 },
    { field: "address", headerName: "Address", width: 450 },
    {
      field: "actions",
      headerName: "Actions",
      width: 120,
      renderCell: (params) => (
        <IconButton onClick={() => {
                                       setWalletDelete(true);
                                       setWalletDeleteDetails(params.row)
                                       // handleDelete(params.row.address_id)
                                   }}>
          <Iconify icon="eva:trash-2-outline" color="red" />
        </IconButton>
      ),
    },
  ];

  
  const onClose = () => {
    setIsOpen(false)
  }

  const onSuccess = async() => {
      setIsOpen(false)
      const response = await get("accounts/getAddresses");
      setAddresses(response.data || []);
  }

  const onOpen = () => {
    setIsOpen(true)
  }

  return (
    <Container>
      
      <NewProviderAddressPopup isOpen={isOpen} onClose={() => onClose()} onSuccess={() => onSuccess()} />

      <Stack direction="row" alignItems="center" justifyContent="space-between" mb={5}>
        <Typography variant="h4">Provider Addresses</Typography>
        <Button variant="contained" onClick={() => onOpen()} startIcon={<Iconify icon="eva:plus-fill" />}>
          Add Address 
        </Button>
      </Stack>

      <DataGrid rows={addresses} columns={columns} pageSize={5} getRowId={(row) => row.address_id} />

      <Dialog open={walletDelete} onClose={() => setWalletDelete(false)}>
        <DialogTitle>Confirm to Delete</DialogTitle>
          <DialogContent>
            <Typography  className='text-center center'>Confirm to delete the {walletDeleteDetails?.chain} wallet {walletDeleteDetails?.address}</Typography>
          </DialogContent> 
          <DialogActions>
            <Button onClick={() => {  
                                      setWalletDeleteDetails({}); 
                                      setWalletDelete(false);
                                   }}>Cancel</Button>
            <Button variant="contained" disabled={isSubmitting} onClick={() => confirmToDelete(walletDeleteDetails)}>
              Confirm
            </Button>
          </DialogActions>
      </Dialog>

    </Container>
  );
}