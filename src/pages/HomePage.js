import React, { useEffect, useState } from "react";
import { Link as RouterLink } from "react-router-dom";
import {
  AppB<PERSON>,
  <PERSON><PERSON><PERSON>,
  Typo<PERSON>,
  Button,
  Container,
  Grid,
  Card,
  CardContent,
  CardActions,
  Box,
  Alert,
  AlertTitle,
  Link,
} from "@mui/material";
import {
  ArrowRight,
  Coins,
  CreditCard,
  Send,
  TrendingUp,
  Users,
  Globe,
  Zap,
  Shield,
  RefreshCw,
  AlertTriangle,
  ExternalLink,
} from "lucide-react";

export default function HomePage() {
  const [userType, setUserType] = useState(null);
  const [kycStatus, setKycStatus] = useState(null);
  const [showKycBanner, setShowKycBanner] = useState(false);

  useEffect(() => {
    // Get user type and KYC status from localStorage
    const storedUserType = localStorage.getItem('userType');
    const storedKycStatus = localStorage.getItem('kycStatus');
    
    setUserType(storedUserType);
    setKycStatus(storedKycStatus);
    
    // Show KYC banner for providers with unverified KYC status
    if (storedUserType === 'provider' && storedKycStatus === 'unverified') {
      setShowKycBanner(true);
    }
  }, []);

  return (
    <Box display="flex" flexDirection="column" minHeight="100vh">
      {/* KYC Verification Banner for Providers */}
      {showKycBanner && (
        <Alert 
          severity="warning" 
          variant="filled"
          icon={<AlertTriangle />}
          sx={{ 
            borderRadius: 0,
            py: 1,
            alignItems: 'center'
          }}
          action={
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button 
                color="inherit" 
                size="small" 
                variant="outlined"
                component="a"
                href="https://b2b.unifid.io"
                target="_blank"
                rel="noopener noreferrer"
                endIcon={<ExternalLink size={16} />}
                sx={{ borderColor: 'rgba(255,255,255,0.7)' }}
              >
                Submit KYC
              </Button>
              <Button 
                color="inherit" 
                size="small" 
                component={RouterLink}
                to="/dashboard/profile"
                sx={{ borderColor: 'rgba(255,255,255,0.7)' }}
              >
                View Profile
              </Button>
            </Box>
          }
        >
          <AlertTitle>Pending Verification</AlertTitle>
          Hello, your account is pending KYC confirmation. Please submit your documents to continue.
        </Alert>
      )}

      {/* Navigation */}
      <AppBar position="static" color="default" elevation={1}>
        <Toolbar>
          {/* Logo */}
          <Typography
            variant="h6"
            component={RouterLink}
            to="/"
            sx={{
              textDecoration: "none",
              color: "inherit",
              fontWeight: "bold",
              mr: 2,
            }}
          >
            MUDA
          </Typography>

          {/* Navigation Links */}
          <Box flexGrow={1}>
            <Button component={RouterLink} to="#features" color="inherit">
              Products
            </Button>
            <Button component={RouterLink} to="#about-liquidity-rail" color="inherit">
              About
            </Button>
            <Button component={RouterLink} to="#contact" color="inherit">
              Contact
            </Button>
            {/* Only show Developer Docs link for clients */}
            {userType !== 'provider' && (
              <Button component={RouterLink} to="#docs" color="inherit">
                Developer Docs
              </Button>
            )}
          </Box>

          {/* Auth Buttons */}
          <Button
            component={RouterLink}
            to="/login"
            variant="outlined"
            sx={{ mr: 1 }}
          >
            Log in
          </Button>
          <Button
            component={RouterLink}
            to="/signup"
            variant="contained"
            color="primary"
          >
            Sign up
          </Button>
        </Toolbar>
      </AppBar>

      {/* Hero Section */}
      <Box
        component="section"
        sx={{
          backgroundColor: "primary.main",
          color: "common.white",
          py: 8,
          textAlign: "center",
        }}
      >
        <Container maxWidth="md">
          <Typography variant="h3" fontWeight="bold" gutterBottom>
            Instant automated stablecoin to fiat payouts
          </Typography>
          <Typography variant="h6" color="primary.light" gutterBottom>
            MUDA Liquidity Rail enables seamless, secure, and instant digital
            asset off-ramps in Africa.
          </Typography>
          <Button
            component={RouterLink}
            to="/signup"
            variant="contained"
            size="large"
            color="secondary"
            endIcon={<ArrowRight />}
          >
            Get Started
          </Button>
        </Container>
      </Box>

      {/* About Section */}
      <Box component="section" py={8} id="about-liquidity-rail">
        <Container maxWidth="md" textAlign="center">
          <Typography variant="h4" fontWeight="bold" gutterBottom>
            What is the MUDA Liquidity Rail?
          </Typography>
          <Typography variant="body1">
            The liquidity rail is a blockchain solution that enables real-time
            transactions among financial services without the need for
            pre-funded float accounts.
          </Typography>
        </Container>
      </Box>

      {/* How It Works Section */}
      <Box component="section" py={8} bgcolor="grey.100" id="how-it-works">
        <Container>
          <Typography
            variant="h4"
            fontWeight="bold"
            textAlign="center"
            gutterBottom
          >
            How It Works
          </Typography>
          <Grid container spacing={4}>
            {[
              {
                icon: <Globe size={48} />,
                title: "Select Destination",
                description: "Choose the currency you want to send.",
              },
              {
                icon: <Coins size={48} />,
                title: "Choose Source Asset",
                description: "Select your preferred digital asset.",
              },
              {
                icon: <Users size={48} />,
                title: "Compare Providers",
                description: "View and select from various providers.",
              },
              {
                icon: <TrendingUp size={48} />,
                title: "Select Rate",
                description: "Choose the best exchange rate.",
              },
              {
                icon: <CreditCard size={48} />,
                title: "Enter Account Details",
                description: "Provide the recipient's account information.",
              },
              {
                icon: <Send size={48} />,
                title: "Send and Complete",
                description: "Complete your transaction securely.",
              },
            ].map((step, index) => (
              <Grid item xs={12} sm={6} md={4} key={index}>
                <Box textAlign="center">
                  {step.icon}
                  <Typography variant="h6" fontWeight="bold" mt={2}>
                    {step.title}
                  </Typography>
                  <Typography variant="body2">{step.description}</Typography>
                </Box>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* Features Section */}
      <Box component="section" py={8} id="features">
        <Container>
          <Typography
            variant="h4"
            fontWeight="bold"
            textAlign="center"
            gutterBottom
          >
            Why Choose MUDA Liquidity Rail?
          </Typography>
          <Grid container spacing={4}>
            {[
              {
                icon: <Zap size={48} />,
                title: "Instant Settlement",
                description:
                  "Experience real-time cross-border transactions.",
              },
              {
                icon: <Globe size={48} />,
                title: "Pan-African Network",
                description: "Connect across African countries seamlessly.",
              },
              {
                icon: <Shield size={48} />,
                title: "Enhanced Security",
                description: "Enterprise-grade security and compliance.",
              },
              {
                icon: <RefreshCw size={48} />,
                title: "Flexible Integration",
                description: "Easy-to-integrate APIs.",
              },
            ].map((feature, index) => (
              <Grid item xs={12} sm={6} md={3} key={index}>
                <Card>
                  <CardContent sx={{ textAlign: "center" }}>
                    {feature.icon}
                    <Typography variant="h6" fontWeight="bold" mt={2}>
                      {feature.title}
                    </Typography>
                    <Typography variant="body2">{feature.description}</Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* CTA Section */}
      <Box
        component="section"
        py={8}
        bgcolor="primary.main"
        color="common.white"
        id="contact"
      >
        <Container textAlign="center">
          <Typography variant="h4" fontWeight="bold" gutterBottom>
            Ready to Transform Your Cross-Border Payments?
          </Typography>
          <Button
            component={RouterLink}
            to="/contact"
            variant="contained"
            size="large"
            color="secondary"
            sx={{ mr: 2 }}
          >
            Contact Sales
          </Button>
          <Button
            component="a"
            href="https://github.com/Muda-Dev/bridgeServer"
            target="_blank"
            rel="noopener noreferrer"
            variant="outlined"
            size="large"
            color="inherit"
          >
            View Documentation
          </Button>
        </Container>
      </Box>
    </Box>
  );
}
