import React from 'react';
import { Controller } from 'react-hook-form';
import { IconButton, TextField, Grid, Typography, FormControl, InputLabel, Select, MenuItem, Box } from '@mui/material';
import Iconify from '../components/Iconify';

const CurrencyAssetsInput = ({
  control,
  currencyName,
  currencies,
  selectedCurrency,
  handleCurrencyChange,
  selectedCurrencyList,
  assetLabel,
  removeSelected,
  seletcedPayinAssetsName,
  errors
}) => {
  return (
    <Grid container spacing={2} alignItems="center">
      <Grid item xs={12} >
        <FormControl fullWidth error={!!errors[currencyName]}>
          <InputLabel>{assetLabel}</InputLabel>
          <Controller
            name={currencyName}
            control={control}
            defaultValue={selectedCurrency}
            render={({ field }) => (
              <Select
                {...field}
                onChange={(e) => {
                  field.onChange(e);
                  handleCurrencyChange(currencyName, e.target.value);
                }}
                label={assetLabel}
              >
                {currencies.map((currency) => (
                  <MenuItem key={currency.value} value={currency.value}>
                    <Box display="flex" alignItems="center">
                      <img
                        src={currency.logo}
                        alt={currency.label}
                        width="24"
                        height="24"
                        style={{ marginRight: 8 }}
                      />
                      {currency.label}
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            )}
          />
          {errors[currencyName] && (
            <Typography color="error" variant="caption">
              {errors[currencyName].message}
            </Typography>
          )}
        </FormControl>
      </Grid>
      <Grid item xs={12} className="selected-currency-items_2">
        <div className="selected-currency-items_1">
          {Array.isArray(selectedCurrencyList) && selectedCurrencyList.map((item, key) => (
              <span className="selected-currency" key={key}>
                {item}
                <IconButton onClick={() => {
                    removeSelected(selectedCurrencyList, item, seletcedPayinAssetsName)
                }}>
                  <Iconify icon="eva:close-outline" color="red" />
                </IconButton>
              </span>
          ))}
        </div>
      </Grid>
    </Grid>
  );
};

export default CurrencyAssetsInput;
