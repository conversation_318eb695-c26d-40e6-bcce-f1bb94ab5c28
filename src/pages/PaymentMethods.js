import { useState, useEffect } from 'react';
import * as Yup from 'yup';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { 
  But<PERSON>, 
  Container, 
  Stack, 
  Typography, 
  Dialog, 
  DialogTitle, 
  DialogContent, 
  DialogActions, 
  TextField, 
  Select, 
  MenuItem, 
  IconButton,
  Box,
  Card,
  Paper,
  Divider,
  Grid,
  Chip,
  CircularProgress,
  Tooltip,
  Alert
} from '@mui/material';
import { DataGrid, GridActionsCellItem } from '@mui/x-data-grid';

import PhoneInput from 'react-phone-input-2'
// import 'react-phone-input-2/lib/style.css'
import 'react-phone-input-2/lib/material.css'
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import Iconify from '../components/Iconify';
import { post, get } from '../api';
import { addPaymentMethod } from '../services';
import { FormProvider } from '../components/hook-form';
import NewPaymentMethodPop from '../components/settingsForms/NewPaymentMethodPopup';
import Page from '../components/Page';

const paymentTypes = [
    { currency: 'UGX', country_code: 'UG', country: 'Uganda', phone_code: '+256', networks: ['MTN', 'Airtel'] },
    { currency: 'KES', country_code: 'KE', country: 'Kenya', phone_code: '+254', networks: ['MPESA'] },
    { currency: 'ZAR', country_code: 'ZA', country: 'South Africa', phone_code: '+27', networks: [] },
    { currency: 'TZS', country_code: 'TZ', country: 'Tanzania', phone_code: '+255', networks: [] }
];

const paymentTypeOptions = [
    { value: 'mobile_money', label: 'Mobile Money' },
    { value: 'bank', label: 'Bank' },
];

export default function PaymentMethods() {
    const [paymentMethods, setPaymentMethods] = useState([]);
    const [networkSwitch, setNetworkSwitch] = useState(0);
    const [phoneDelete, setPhoneDelete] = useState(false);
    const [pMDetails, setPMDetails] = useState(false);
    const [phonePaymentMethodDetails, setPhonePaymentMethodDetails] = useState({});
    const [phoneDeleteDetails, setPhoneDeleteDetails] = useState({});
    const [schema, setSchema] = useState(""); 
    const [isOpen, setIsOpen] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [isDeleting, setIsDeleting] = useState(false);
    
    const [open, setOpen] = useState(false);
    const [formData, setFormData] = useState({
        type: '',
        currency: '',
        country_code: '',
        network: '',
        phone_number: '',
        account_name: '',
    });
                                            
    useEffect(() => {
        fetchPaymentMethods();
    }, []);

    const WalletSchema = Yup.object().shape({
        type: Yup.string().required("Type is required"),
        country_code: Yup.string().required("Country is required"),
        currency: Yup.string().required("Currency is required"),
        network: Yup.string().required("Network is required"),
        phone_number: Yup.string().required("Phone number is required"),
        account_name: Yup.string().required("Account name is required")
    });
    
    const BankSchema = Yup.object().shape({
        type: Yup.string().required("Type is required"),
        country_code: Yup.string().required("Country is required"),
        currency: Yup.string().required("Currency is required"),
        account_name: Yup.string().required("Account name is required"),
        bank_name: Yup.string().required("Bank name is required"),
        bank_code: Yup.string().required("Bank code is required"),
        bank_address: Yup.string().required("Bank address is required"),
        account_number: Yup.string().required("Account number is required"),
        bank_country: Yup.string().required("Bank country is required"),
        bank_phone_number: Yup.string().required("Bank phone number is required"),
    });                        
      
    const methods = useForm({
        resolver: yupResolver(schema),
        defaultValues: formData
    });

    const {
        handleSubmit,
        control,
        reset,
        setValue,
        formState: { errors, isSubmitting },
    } = methods;
           
    useEffect(() => {
        const newSchema = formData?.type === 'bank' ? BankSchema : WalletSchema;
        setSchema(newSchema);
        methods.reset(formData);
    }, [formData?.type]);

    const fetchPaymentMethods = async () => {
        try {
            setIsLoading(true);
            const response = await get(`accounts/getPaymentMethods`);
            setPaymentMethods(response || []);
        } catch (error) {
            // toast.error("Failed to fetch payment methods");
            console.error("Error fetching payment methods:", error);
        } finally {
            setIsLoading(false);
        }
    };

    const confirmToDelete = async (data) => {
        try {
            setIsDeleting(true);
            const deletedAddress = await get(`accounts/deletePhoneNumber/${data.payment_method_id}`);
            if (deletedAddress.status === 200 || deletedAddress.status === 200) {
                toast.success(`${deletedAddress.message}`);
                setPhoneDeleteDetails({}); 
                setPhoneDelete(false);
                await fetchPaymentMethods();
            } else {
                toast.error(`${deletedAddress.message}`);
            }
        } catch (error) {
            toast.error("Error deleting payment method");
            console.error("Error deleting payment method:", error);
        } finally {
            setIsDeleting(false);
        }
    };

    const getTypeLabel = (type) => {
        if (!type) return '';
        return type.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ');
    };

    const columns = [
        { 
            field: 'id', 
            headerName: 'ID', 
            width: 70,
            renderCell: (params) => (
                <Typography variant="body2" fontWeight={500}>
                    #{params.value}
                </Typography>
            )
        },
        { 
            field: 'type', 
            headerName: 'Type', 
            width: 150,
            renderCell: (params) => (
                <Chip 
                    label={getTypeLabel(params.value)} 
                    size="small"
                    sx={{ 
                        bgcolor: params.value === 'bank' ? 'rgba(0, 171, 85, 0.08)' : 'rgba(24, 144, 255, 0.08)',
                        color: params.value === 'bank' ? 'success.main' : 'primary.main',
                        fontWeight: 600,
                        textTransform: 'capitalize'
                    }}
                />
            )
        },
        { 
            field: 'currency', 
            headerName: 'Currency', 
            width: 120,
            renderCell: (params) => (
                <Box sx={{ 
                    backgroundColor: 'rgba(32, 101, 209, 0.08)',
                    py: 0.5,
                    px: 1.5,
                    borderRadius: 1,
                    display: 'inline-flex'
                }}>
                    <Typography variant="body2" fontWeight={500}>
                        {params.value}
                    </Typography>
                </Box>
            )
        },
        { 
            field: 'account_name', 
            headerName: 'Account Name', 
            width: 200,
            renderCell: (params) => (
                <Typography variant="body2" fontWeight={500}>
                    {params.value}
                </Typography>
            )
        },
        {
            field: 'acc_number',
            headerName: 'Account/Phone Number',
            width: 220,
            renderCell: (params) => (
                <Typography variant="body2" fontFamily={params.row.type === 'bank' ? 'monospace' : 'inherit'}>
                    {(params.row.type === 'bank') ? params.row.account_number : params.row.phone_number}
                </Typography>
            )
        },
        {
            field: 'actions',
            headerName: 'Actions',
            width: 120,
            type: 'actions',
            getActions: (params) => [
                <GridActionsCellItem
                    icon={
                        <Tooltip title="View Details">
                            <IconButton color="primary" size="small">
                                <Iconify icon="eva:eye-outline" width={20} height={20} />
                            </IconButton>
                        </Tooltip>
                    }
                    onClick={() => {
                        setPMDetails(true);
                        setPhonePaymentMethodDetails(params.row);
                    }}
                    label="View"
                />,
                <GridActionsCellItem
                    icon={
                        <Tooltip title="Delete">
                            <IconButton color="error" size="small">
                                <Iconify icon="eva:trash-2-outline" width={20} height={20} />
                            </IconButton>
                        </Tooltip>
                    }
                    onClick={() => {
                        setPhoneDelete(true);
                        setPhoneDeleteDetails(params.row);
                    }}
                    label="Delete"
                />
            ]
        }
    ];

    const onClose = () => {
        setIsOpen(false);
    };

    const onSuccess = async () => {
        setIsOpen(false);
        // toast.success("Payment method added successfully");
        await fetchPaymentMethods();
    };
  
    const onOpen = () => {
        setIsOpen(true);
    };

    return (
        <Page title="Payment Methods">
            <Container>
                <NewPaymentMethodPop isOpen={isOpen} onClose={() => onClose()} onSuccess={() => onSuccess()} />

                <Box sx={{ mb: 4 }}>
                    <Typography variant="h4" sx={{ fontWeight: 700, color: 'text.primary' }}>
                        Payment Methods
                    </Typography>
                    <Typography variant="body2" sx={{ mt: 1, color: 'text.secondary' }}>
                        Manage your payment methods for receiving funds
                    </Typography>
                </Box>

                <Card sx={{ 
                    p: 3, 
                    mb: 3, 
                    borderRadius: 2, 
                    boxShadow: '0 0 10px rgba(0,0,0,0.05)' 
                }}>
                    <Stack direction="row" alignItems="center" justifyContent="space-between" mb={3}>
                        <Typography variant="h6" sx={{ fontWeight: 600 }}>
                            Your Payment Methods
                        </Typography>
                        <Button 
                            variant="contained" 
                            onClick={() => onOpen()} 
                            startIcon={<Iconify icon="eva:plus-fill" />}
                            sx={{
                                bgcolor: '#1F50C2',
                                '&:hover': {
                                    bgcolor: '#0F2B77',
                                },
                                boxShadow: '0 4px 12px rgba(31, 80, 194, 0.15)',
                                borderRadius: '8px'
                            }}
                        >
                            Add Payment Method
                        </Button>
                    </Stack>

                    <Divider sx={{ mb: 3 }} />

                    {paymentMethods.length === 0 && !isLoading ? (
                        <Alert severity="info" sx={{ mb: 3 }}>
                            You don't have any payment methods yet. Click "Add Payment Method" to create one.
                        </Alert>
                    ) : null}

                    <Box sx={{ height: 400, width: '100%' }}>
                        <DataGrid 
                            rows={paymentMethods} 
                            columns={columns} 
                            pageSize={5}
                            rowsPerPageOptions={[5, 10, 25]}
                            loading={isLoading}
                            disableSelectionOnClick
                            disableColumnMenu
                            sx={{
                                border: 'none',
                                '& .MuiDataGrid-cell': { 
                                    py: 2,
                                    borderBottom: '1px solid #f0f0f0'
                                },
                                '& .MuiDataGrid-columnHeaders': {
                                    bgcolor: 'rgba(145, 158, 171, 0.08)',
                                    borderRadius: 1
                                },
                                '& .MuiDataGrid-columnHeaderTitle': {
                                    fontWeight: 600
                                }
                            }}
                        />
                    </Box>
                </Card>
           
                {/* Delete Confirmation Dialog */}
                <Dialog 
                    open={phoneDelete} 
                    onClose={() => setPhoneDelete(false)}
                    PaperProps={{
                        sx: { borderRadius: 2, p: 1 }
                    }}
                >
                    <DialogTitle sx={{ fontWeight: 600 }}>
                        Confirm Deletion
                    </DialogTitle>
                    <DialogContent>
                        <Box sx={{ mb: 3 }}>
                            <Typography variant="body1" sx={{ mb: 2 }}>
                                Are you sure you want to delete this payment method?
                            </Typography>
                            <Paper sx={{ 
                                p: 2, 
                                bgcolor: 'rgba(255, 72, 66, 0.08)',
                                borderRadius: 1
                            }}>
                                <Stack spacing={1}>
                                    <Typography variant="subtitle2">
                                        Type: <Box component="span" fontWeight={600}>{getTypeLabel(phoneDeleteDetails?.type)}</Box>
                                    </Typography>
                                    <Typography variant="subtitle2">
                                        Name: <Box component="span" fontWeight={600}>{phoneDeleteDetails?.account_name}</Box>
                                    </Typography>
                                    <Typography variant="subtitle2">
                                        {phoneDeleteDetails?.type === 'bank' ? 'Account Number:' : 'Phone Number:'}
                                        <Box component="span" fontWeight={600} sx={{ ml: 1 }}>
                                            {phoneDeleteDetails?.type === 'bank' ? phoneDeleteDetails?.account_number : phoneDeleteDetails?.phone_number}
                                        </Box>
                                    </Typography>
                                </Stack>
                            </Paper>
                        </Box>
                        <Typography variant="body2" color="error" fontWeight={500}>
                            Warning: This action cannot be undone.
                        </Typography>
                    </DialogContent> 
                    <DialogActions sx={{ px: 3, pb: 3 }}>
                        <Button 
                            onClick={() => {  
                                setPhoneDeleteDetails({}); 
                                setPhoneDelete(false);
                            }}
                            sx={{ mr: 1 }}
                        >
                            Cancel
                        </Button>
                        <Button 
                            variant="contained" 
                            color="error"
                            disabled={isDeleting} 
                            onClick={() => confirmToDelete(phoneDeleteDetails)}
                            startIcon={isDeleting ? <CircularProgress size={20} color="inherit" /> : <Iconify icon="eva:trash-2-outline" />}
                        >
                            {isDeleting ? 'Deleting...' : 'Delete'}
                        </Button>
                    </DialogActions>
                </Dialog>

                {/* View Details Dialog */}
                <Dialog 
                    open={pMDetails} 
                    onClose={() => setPMDetails(false)}
                    maxWidth="sm"
                    fullWidth
                    PaperProps={{
                        sx: { borderRadius: 2 }
                    }}
                >
                    <DialogTitle sx={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        p: 2
                    }}>
                        <Typography variant="h6" fontWeight={600}>
                            Payment Method Details
                        </Typography>
                        <IconButton 
                            onClick={() => { 
                                setPhonePaymentMethodDetails({}); 
                                setPMDetails(false);
                            }}
                            size="small"
                            sx={{ color: 'grey.500' }}
                        >
                            <Iconify icon="eva:close-fill" width={24} height={24} />
                        </IconButton>
                    </DialogTitle>
                    
                    <Divider />
                    
                    <DialogContent sx={{ p: 3 }}>
                        <Grid container spacing={2}>
                            <Grid item xs={12}>
                                <Paper sx={{ 
                                    p: 2, 
                                    bgcolor: phonePaymentMethodDetails?.type === 'bank' 
                                        ? 'rgba(0, 171, 85, 0.08)' 
                                        : 'rgba(24, 144, 255, 0.08)',
                                    borderRadius: 1,
                                    mb: 2
                                }}>
                                    <Typography variant="h6" fontWeight={600} sx={{ 
                                        color: phonePaymentMethodDetails?.type === 'bank' ? 'success.main' : 'primary.main'
                                    }}>
                                        {getTypeLabel(phonePaymentMethodDetails?.type)}
                                    </Typography>
                                </Paper>
                            </Grid>
                            
                            <Grid item xs={12} md={6}>
                                <DetailItem 
                                    label="Currency" 
                                    value={phonePaymentMethodDetails?.currency}
                                />
                            </Grid>
                            
                            <Grid item xs={12} md={6}>
                                <DetailItem 
                                    label="Account Name" 
                                    value={phonePaymentMethodDetails?.account_name}
                                />
                            </Grid>
                            
                            <Grid item xs={12}>
                                <DetailItem 
                                    label={phonePaymentMethodDetails?.type === 'bank' ? 'Account Number' : 'Phone Number'} 
                                    value={phonePaymentMethodDetails?.type === 'bank' 
                                        ? phonePaymentMethodDetails?.account_number 
                                        : phonePaymentMethodDetails?.phone_number
                                    }
                                    monospace={phonePaymentMethodDetails?.type === 'bank'}
                                />
                            </Grid>
                            
                            {phonePaymentMethodDetails?.type !== 'bank' && (
                                <Grid item xs={12} md={6}>
                                    <DetailItem 
                                        label="Network" 
                                        value={phonePaymentMethodDetails?.network}
                                    />
                                </Grid>
                            )}
                            
                            <Grid item xs={12} md={6}>
                                <DetailItem 
                                    label="Country Code" 
                                    value={phonePaymentMethodDetails?.country_code}
                                />
                            </Grid>
                            
                            {phonePaymentMethodDetails?.type === 'bank' && (
                                <>
                                    <Grid item xs={12} md={6}>
                                        <DetailItem 
                                            label="Bank Name" 
                                            value={phonePaymentMethodDetails?.bank_name}
                                        />
                                    </Grid>
                                    
                                    <Grid item xs={12} md={6}>
                                        <DetailItem 
                                            label="Bank Code" 
                                            value={phonePaymentMethodDetails?.bank_code}
                                        />
                                    </Grid>
                                    
                                    <Grid item xs={12}>
                                        <DetailItem 
                                            label="Bank Address" 
                                            value={phonePaymentMethodDetails?.bank_address}
                                        />
                                    </Grid>
                                    
                                    <Grid item xs={12} md={6}>
                                        <DetailItem 
                                            label="Bank Country" 
                                            value={phonePaymentMethodDetails?.bank_country}
                                        />
                                    </Grid>
                                    
                                    <Grid item xs={12} md={6}>
                                        <DetailItem 
                                            label="Bank Phone Number" 
                                            value={phonePaymentMethodDetails?.bank_phone_number}
                                        />
                                    </Grid>
                                </>
                            )}
                        </Grid>
                    </DialogContent> 
                    
                    <Divider />
                    
                    <DialogActions sx={{ p: 2 }}>
                        <Button 
                            variant="contained"
                            onClick={() => {
                                setPhonePaymentMethodDetails({}); 
                                setPMDetails(false);
                            }}
                        >
                            Close
                        </Button>
                    </DialogActions>
                </Dialog>
            </Container>
        </Page>
    );
}

// Helper component for detail items
const DetailItem = ({ label, value, monospace = false }) => (
    <Box sx={{ mb: 1 }}>
        <Typography variant="body2" color="text.secondary" gutterBottom>
            {label}
        </Typography>
        <Typography 
            variant="body1" 
            fontWeight={500}
            sx={{ 
                fontFamily: monospace ? 'monospace' : 'inherit',
                wordBreak: 'break-all'
            }}
        >
            {value || '-'}
        </Typography>
    </Box>
);
