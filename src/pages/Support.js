import { useState, useEffect } from 'react';
import { Link as RouterLink } from 'react-router-dom';
// material
import { <PERSON><PERSON>, Container, Stack, Typography } from '@mui/material';
import { DataGrid } from '@mui/x-data-grid';
import { getAddress } from '../services';

// components
import Page from '../components/Page';
import Iconify from '../components/Iconify';
import  ModalPop from '../sections/modals/MainModalView'
// mock

// ----------------------------------------------------------------------


// ----------------------------------------------------------------------


const columns = [
  { field: 'id', headerName: 'ID', width: 70 },
  { field: 'token', headerName: 'Token', width: 200},
  { field: 'address', headerName: 'Address', width: 280},
];

const rows = [
  { id: 1, token: 'USDT(Tron)', address: '8989d3bc-7b89-48e0-b78a-d9567d1df904' },
  { id: 2, token: 'USDT(Stellar)', address: '8989d3bc-7b89-48e0-b78a-d9567d1df904' },
];

const columns2 = [
  { field: 'id', headerName: 'ID', width: 70 },
  { field: 'network', headerName: 'Networl' , width: 250},
  { field: 'networkCode', headerName: 'Code', width: 250},
  {
    field: 'currency',
    headerName: 'currency',
    type: 'string',
    width: 120,
  }
];

const rows2 = [
  { id: 1, network: 'Mobile Money', networkCode: '8989d3b', currency: 'UGX' },
  { id: 2, network: 'Mpesa', networkCode: '8989d3bc', currency: 'KES' },
];


export default function Support() {
  const [modelData, setModelData] = useState({});
  const [open, setOpen] = useState(false);
  const [modelType, setModelType] = useState('');
  const [allWallets, setAllWallets] = useState([]);
  const [tabChange, setTabChange] = useState('knowledge-base');

  const showWalletModel = async (optionTrend) => {
    setOpen(true)
    setModelType(optionTrend);
    setModelData({ "type": optionTrend, "content": "" })
  }


  const chamgeSection = async (tabChange) => {
     setTabChange(tabChange);
  } 


  const onModelComplete = async (options) => {
     console.log("model compele", options)
  }
  
   useEffect(() => { 

      const initialContent = async() => {
          
        const transact = await getAddress();
        if(transact?.data?.length > 0){
           const addressArr = [];
           transact?.data?.map((address) => {
               return addressArr.push({ id: address?.address_id, token: address?.chain, address: address?.address })
           });
           setAllWallets(addressArr)
        }
        
      } 

      initialContent();
  }, [getAddress])


  return (
    <>

    <ModalPop openModel={open} 
              modalToggler={setOpen} 
              theModelType={modelType} 
              dataContent={modelData}
              onComplete={onModelComplete} />

    <Page title="Dashboard: Blog">
      <Container>
        <Stack direction="row" alignItems="center" justifyContent="space-between" mb={5}>
          <Typography variant="h4" gutterBottom>
            Support
          </Typography>
        </Stack>

        <Stack  className="tabing-section">
          <p> 
           <Button onClick={() => chamgeSection('knowledge-base')} className={(tabChange === "knowledge-base")? "button active-tab":"button default-tab"}>Knowledge Base</Button> 
           <Button onClick={() => chamgeSection('tickets')} className={(tabChange === "tickets")? "button active-tab":"button default-tab"}>My Tickets</Button>  
          </p>  
        </Stack>







        <div className={(tabChange === "knowledge-base")? "item-form-top-10":"item-form-top-10 hidden"}> 
         <Stack direction="row" alignItems="center" justifyContent="space-between" mb={5}>
          <Typography variant="h6" gutterBottom>
             Support & Documentation
          </Typography>
          <Button variant="contained" component={RouterLink} 
                  onClick={() => { showWalletModel('support')}}
                  to="#" startIcon={<Iconify icon="eva:plus-fill" />}>
            New Ticket
          </Button>
         </Stack>
         <DataGrid
                  className="fullWidth hidden"
                  rows={allWallets}
                  columns={columns}
                  initialState={{
                    pagination: {
                      paginationModel: { page: 0, pageSize: 5 },
                    },
                  }}
                  pageSizeOptions={[5, 10]}
                  checkboxSelection
                />
       </div>     
           
      </Container>
    </Page>
    </> 
  );
}
