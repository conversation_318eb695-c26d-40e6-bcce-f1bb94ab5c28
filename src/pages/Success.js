import React from 'react';
import { Container, Typography, Box, Button, Paper } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import HomeIcon from '@mui/icons-material/Home';
import DashboardIcon from '@mui/icons-material/Dashboard';
import Logo from '../components/Logo';

const Success = () => {
  const navigate = useNavigate();

  return (
    <Box sx={{ 
      minHeight: '100vh', 
      backgroundColor: '#F8F9FA',
      display: 'flex',
      flexDirection: 'column'
    }}>
      {/* Header */}
      <Box 
        component="header" 
        sx={{ 
          display: 'flex', 
          alignItems: 'center', 
          p: 3,
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%'
        }}
      >
        <Logo />
      </Box>
      
      <Container maxWidth="sm" sx={{ 
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        flexGrow: 1,
        py: 5
      }}>
        <Paper elevation={3} sx={{ 
          textAlign: "center", 
          p: 5, 
          borderRadius: 3,
          width: '100%',
          boxShadow: '0 10px 40px rgba(0,0,0,0.1)'
        }}>
          <Box sx={{ mb: 4 }}>
            <CheckCircleOutlineIcon 
              sx={{ 
                fontSize: 80, 
                color: '#4CAF50',
                p: 1,
                borderRadius: '50%',
                backgroundColor: 'rgba(76, 175, 80, 0.1)'
              }} 
            />
          </Box>
          
          <Typography variant="h4" fontWeight="700" sx={{ mb: 2 }}>
            Payment Successful!
          </Typography>
          
          <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
            Your transaction has been successfully processed. A confirmation email has been sent to your registered email address.
          </Typography>
          
          <Typography variant="body2" sx={{ mb: 2, mt: 3, fontWeight: 500 }}>
            Transaction ID: <Box component="span" sx={{ color: '#1F50C2' }}>TRX-{Math.random().toString(36).substring(2, 10).toUpperCase()}</Box>
          </Typography>
          
          <Box sx={{ 
            mt: 5,
            display: 'flex',
            gap: 2,
            justifyContent: 'center',
            flexDirection: { xs: 'column', sm: 'row' }
          }}>
            <Button 
              variant="outlined" 
              color="primary"
              startIcon={<HomeIcon />}
              onClick={() => navigate('/')}
              sx={{ 
                py: 1.25,
                borderRadius: '8px',
                fontWeight: 600,
                borderColor: '#1F50C2',
                color: '#1F50C2',
                '&:hover': { borderColor: '#0F2B77', backgroundColor: 'rgba(31, 80, 194, 0.08)' }
              }}
            >
              Back to Home
            </Button>
            
            <Button 
              variant="contained" 
              color="primary"
              startIcon={<DashboardIcon />}
              onClick={() => navigate('/dashboard')}
              sx={{ 
                py: 1.25,
                backgroundColor: '#1F50C2',
                borderRadius: '8px',
                fontWeight: 600,
                '&:hover': { backgroundColor: '#0F2B77' }
              }}
            >
              Go to Dashboard
            </Button>
          </Box>
        </Paper>
      </Container>
    </Box>
  );
};

export default Success;
