
import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>alog, DialogTitle, DialogContent, DialogActions, TextField, Select, MenuItem, IconButton } from '@mui/material';
import { DataGrid } from '@mui/x-data-grid';
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import Iconify from '../components/Iconify';
import NewProviderExchangeEndPointPopup from '../components/settingsForms/NewProviderExchangeEndPointPopup';
import { post, get } from '../api';

export default function ExchangeEndPoint() {
    const [endpoints, setEndPintUrl] = useState([]);
    const [open, setOpen] = useState(false);
    const [isOpen, setIsOpen] = useState(false);
    const [formData, setFormData] = useState({
        address: '',
        chain: 'STELLAR'
    });

    useEffect(() => {
        fetchAddresses();
    }, []);

    const fetchAddresses = async () => {
        const response = await get(`accounts/updatedRatesUrl`);
        if(!response?.data && response?.data !== undefined ){
           setEndPintUrl(response?.data || []);
        }
    };

    const handleChange = (e) => {
        setFormData({ ...formData, [e.target.name]: e.target.value });
    };

    const handleSubmit = async () => {
        await post(`accounts/addAddress`, formData);
        toast.success('Exchange url added successfully!');
        setOpen(false);
        fetchAddresses();
    };

    const handleDelete = async (id) => {
        await get(`accounts/deleteAddress/${id}`);
        toast.success('Exchange url deleted successfully!');
        fetchAddresses();
    };

    const columns = [
        { field: 'label', headerName: 'Label', width: 150 },
        { field: 'url', headerName: 'End Point Url', width: 450 },
        {
            field: 'actions',
            headerName: 'Actions',
            width: 120,
            renderCell: (params) => (
                <>
                    <IconButton onClick={() => handleDelete(params.row.address_id)}>
                        <Iconify icon="eva:trash-2-outline" color="red" />
                    </IconButton>
                </>
            )
        }
    ];


    const onClose = () => {
       setIsOpen(false)
    }
    
    const onSuccess = async() => {
       setIsOpen(false)
    }
    
    const onOpen = () => {
        setIsOpen(true)
    }
    


    return (
        <Container>
            
            <NewProviderExchangeEndPointPopup isOpen={isOpen} onClose={() => onClose()} onSuccess={() => onSuccess()} />

            <Stack direction="row" alignItems="center" justifyContent="space-between" mb={5} pl={0}  className="margin-left-0">
              <Typography variant="h4"  pl={0} ml={0}>Provider Exchange End Point</Typography>
              <Button variant="contained" onClick={() => onOpen()} startIcon={<Iconify icon="eva:edit-fill" />}>Edit End Point</Button>
            </Stack>


            {/* Fix: Use getRowId to ensure a unique ID */}
            <DataGrid rows={endpoints} columns={columns} pageSize={5} getRowId={(row) => row.address_id} />
            
            

        </Container>
    );
}
