import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Button,
  TextField,
  Box,
  Typography,
  IconButton,
  InputAdornment,
  Link,
  CircularProgress
} from '@mui/material';
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
// Icons
import VisibilityIcon from '@mui/icons-material/Visibility';
import VisibilityOffIcon from '@mui/icons-material/VisibilityOff';
import CloseIcon from '@mui/icons-material/Close';
import { post, get } from '../api';
import Logo from '../components/Logo';

const LoginPopup = ({ isOpen, onClose, onSuccess }) => {
  const navigate = useNavigate();
  
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const handleLogin = async () => {
    if (!username || !password) {
      toast.error('Please enter both email and password');
      return;
    }
    
    setIsSubmitting(true);
    try {
      // Replace with your actual login API endpoint
      const response = await post('accounts/login', { email: username, password });

      if (response.status === 200) {
        localStorage.setItem('userObject', JSON.stringify(response.data));
        localStorage.setItem('authJWT', response.data.jwt);
        localStorage.setItem('serviceToken', response.data.jwt);
        toast.success('Login successful!');
        onSuccess(response.data);
      } else {
        toast.error('Login failed. Please check your credentials.');
      }
    } catch (error) {
      console.error('Login error:', error);
      toast.error('Error during login. Please try again later.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleRegisterClick = () => {
    onClose();
    navigate('/register');
  };

  return (
    <Dialog 
      open={isOpen} 
      onClose={onClose} 
      maxWidth="sm" 
      fullWidth 
      PaperProps={{
        sx: {
          borderRadius: '12px',
          boxShadow: '0 8px 24px rgba(0,0,0,0.15)',
        }
      }}
    >
      <Box sx={{ textAlign: 'center', pt: 3, pb: 1 }}>
        <Logo />
      </Box>
      
      <DialogTitle sx={{ 
        pb: 1, 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        textAlign: 'center'
      }}>
        <Box sx={{ width: '100%' }}>
          <Typography variant="h5" fontWeight={700} color="#1F50C2">
            Welcome Back
          </Typography>
        </Box>
        <IconButton 
          onClick={onClose} 
          edge="end" 
          aria-label="close"
          sx={{ position: 'absolute', right: 8, top: 8 }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      
      <DialogContent sx={{ px: { xs: 3, md: 4 }, py: 2 }}>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 3, textAlign: 'center' }}>
          Please log in to continue with your transaction
        </Typography>
        
        <Box component="form" sx={{ mt: 1 }}>
          <TextField
            label="Email Address"
            fullWidth
            variant="outlined"
            margin="normal"
            value={username}
            onChange={(e) => setUsername(e.target.value)}
            placeholder="<EMAIL>"
            type="email"
            autoFocus
            sx={{ mb: 2 }}
          />
          
          <TextField
            label="Password"
            type={showPassword ? 'text' : 'password'}
            fullWidth
            variant="outlined"
            margin="normal"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    onClick={() => setShowPassword(!showPassword)}
                    edge="end"
                  >
                    {showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                  </IconButton>
                </InputAdornment>
              )
            }}
            sx={{ mb: 1 }}
          />
          
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 3 }}>
            <Link 
              href="/password-reset" 
              variant="body2" 
              sx={{ color: '#1F50C2', textDecoration: 'none', '&:hover': { textDecoration: 'underline' } }}
            >
              Forgot password?
            </Link>
          </Box>

          <Button
            onClick={handleLogin}
            variant="contained"
            fullWidth
            disabled={isSubmitting}
            sx={{ 
              py: 1.5, 
              backgroundColor: '#1F50C2', 
              '&:hover': { backgroundColor: '#0F2B77' },
              borderRadius: '8px',
              fontWeight: 600
            }}
          >
            {isSubmitting ? (
              <CircularProgress size={24} color="inherit" />
            ) : (
              'Sign In'
            )}
          </Button>
        </Box>
        
        <Typography variant="body2" align="center" sx={{ mt: 3 }}>
          Don't have an account?{' '}
          <Link 
            component="button" 
            variant="subtitle2" 
            onClick={handleRegisterClick}
            sx={{ color: '#1F50C2', fontWeight: 600 }}
          >
            Get started
          </Link>
        </Typography>
      </DialogContent>
    </Dialog>
  );
};

export default LoginPopup;
