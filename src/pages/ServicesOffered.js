import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Con<PERSON>er, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Dialog, DialogTitle, DialogContent, DialogActions, TextField, Select, MenuItem, IconButton } from '@mui/material';
import { DataGrid } from '@mui/x-data-grid';
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import Iconify from '../components/Iconify';
import { post, get } from '../api';
import NewProviderAcceptedServicePopup from '../components/settingsForms/NewProviderAcceptedServicePopup';

export default function ServicesOffered() {
    const [addresses, setAddresses] = useState([]);
    const [open, setOpen] = useState(false);
    const [isOpen, setIsOpen] = useState(false);
    const [formData, setFormData] = useState({
        address: '',
        chain: 'STELLAR'
    });

    useEffect(() => {
        fetchAddresses();
    }, []);

    const fetchAddresses = async () => {
        const response = await get(`accounts/getproviderServices`);
        setAddresses(response?.data || []);
    };

    const handleChange = (e) => {
        setFormData({ ...formData, [e.target.name]: e.target.value });
    };

    const handleSubmit = async () => {
        await post(`accounts/addAddress`, formData);
        toast.success('Service added successfully!');
        setOpen(false);
        fetchAddresses();
    };

    const handleDelete = async (id) => {
        await get(`accounts/deleteAddress/${id}`);
        toast.success('Service deleted successfully!');
        fetchAddresses();
    };

    const columns = [
        { field: 'address_id', headerName: 'ID', width: 70 },
        { field: 'chain', headerName: 'Service Name', width: 150 },
        { field: 'address', headerName: 'Network', width: 450 },
        {
            field: 'actions',
            headerName: 'Actions',
            width: 120,
            renderCell: (params) => (
                <>
                    <IconButton onClick={() => handleDelete(params.row.address_id)}>
                        <Iconify icon="eva:trash-2-outline" color="red" />
                    </IconButton>
                </>
            )
        }
    ];

    
    const onClose = () => {
        setIsOpen(false)
    }
     
    const onSuccess = async() => {
        setIsOpen(false)
    }
     
    const onOpen = () => {
         setIsOpen(true)
    }
     

    return (
        <Container>
            <NewProviderAcceptedServicePopup isOpen={isOpen} onClose={() => onClose()} onSuccess={() => onSuccess()} />
            <Stack direction="row" alignItems="center" justifyContent="space-between" mb={5}>
                <Typography variant="h4">Services Offered</Typography>
                <Button variant="contained" onClick={() => onOpen()}  startIcon={<Iconify icon="eva:plus-fill" />}>Add Service</Button>
            </Stack>

            {/* Fix: Use getRowId to ensure a unique ID */}
            <DataGrid rows={addresses} columns={columns} pageSize={5} getRowId={(row) => row.address_id} />
        </Container>
    );
}
