import React, { useState, useEffect } from 'react';
import { Link as RouterLink, useNavigate } from 'react-router-dom';
// @mui
import {
  styled,
  Container,
  Typography,
  Box,
  TextField,
  Button,
  Link,
  InputAdornment,
  IconButton,
  FormControlLabel,
  Checkbox,
  Stack,
  CircularProgress,
  Tabs,
  Tab,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  ListSubheader,
  Autocomplete
} from '@mui/material';
import axios from 'axios';
// icons
import VisibilityIcon from '@mui/icons-material/Visibility';
import VisibilityOffIcon from '@mui/icons-material/VisibilityOff';
import PersonOutlineIcon from '@mui/icons-material/PersonOutline';
import BusinessIcon from '@mui/icons-material/Business';
import { toast } from 'react-toastify';
import { post, get } from '../api';
// hooks
import useResponsive from '../hooks/useResponsive';
// components
import Page from '../components/Page';
import Logo from '../components/Logo';

// ----------------------------------------------------------------------

const RootStyle = styled('div')(({ theme }) => ({
  minHeight: '100vh',
  display: 'flex',
  backgroundColor: '#F8F9FA',
}));

// List of African countries with their associated currencies
const AFRICAN_COUNTRIES = [
  { country: 'Algeria', currency: 'DZD' },
  { country: 'Angola', currency: 'AOA' },
  { country: 'Benin', currency: 'XOF' },
  { country: 'Botswana', currency: 'BWP' },
  { country: 'Burkina Faso', currency: 'XOF' },
  { country: 'Burundi', currency: 'BIF' },
  { country: 'Cabo Verde', currency: 'CVE' },
  { country: 'Cameroon', currency: 'XAF' },
  { country: 'Central African Republic', currency: 'XAF' },
  { country: 'Chad', currency: 'XAF' },
  { country: 'Comoros', currency: 'KMF' },
  { country: 'Congo', currency: 'XAF' },
  { country: 'Côte d\'Ivoire', currency: 'XOF' },
  { country: 'Djibouti', currency: 'DJF' },
  { country: 'Egypt', currency: 'EGP' },
  { country: 'Equatorial Guinea', currency: 'XAF' },
  { country: 'Eritrea', currency: 'ERN' },
  { country: 'Eswatini', currency: 'SZL' },
  { country: 'Ethiopia', currency: 'ETB' },
  { country: 'Gabon', currency: 'XAF' },
  { country: 'Gambia', currency: 'GMD' },
  { country: 'Ghana', currency: 'GHS' },
  { country: 'Guinea', currency: 'GNF' },
  { country: 'Guinea-Bissau', currency: 'XOF' },
  { country: 'Kenya', currency: 'KES' },
  { country: 'Lesotho', currency: 'LSL' },
  { country: 'Liberia', currency: 'LRD' },
  { country: 'Libya', currency: 'LYD' },
  { country: 'Madagascar', currency: 'MGA' },
  { country: 'Malawi', currency: 'MWK' },
  { country: 'Mali', currency: 'XOF' },
  { country: 'Mauritania', currency: 'MRU' },
  { country: 'Mauritius', currency: 'MUR' },
  { country: 'Morocco', currency: 'MAD' },
  { country: 'Mozambique', currency: 'MZN' },
  { country: 'Namibia', currency: 'NAD' },
  { country: 'Niger', currency: 'XOF' },
  { country: 'Nigeria', currency: 'NGN' },
  { country: 'Rwanda', currency: 'RWF' },
  { country: 'Sao Tome and Principe', currency: 'STN' },
  { country: 'Senegal', currency: 'XOF' },
  { country: 'Seychelles', currency: 'SCR' },
  { country: 'Sierra Leone', currency: 'SLL' },
  { country: 'Somalia', currency: 'SOS' },
  { country: 'South Africa', currency: 'ZAR' },
  { country: 'South Sudan', currency: 'SSP' },
  { country: 'Sudan', currency: 'SDG' },
  { country: 'Tanzania', currency: 'TZS' },
  { country: 'Togo', currency: 'XOF' },
  { country: 'Tunisia', currency: 'TND' },
  { country: 'Uganda', currency: 'UGX' },
  { country: 'Zambia', currency: 'ZMW' },
  { country: 'Zimbabwe', currency: 'ZWL' }
];

// Transfer types
const TRANSFER_TYPES = [
  { value: 'bank_transfer', label: 'Bank Transfer' },
  { value: 'mobile_transfer', label: 'Mobile Transfer' }
];

// ----------------------------------------------------------------------

export default function Register() {
  const navigate = useNavigate();
  const smUp = useResponsive('up', 'sm');
  const mdUp = useResponsive('up', 'md');
  
  const [accountType, setAccountType] = useState('client');
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
    agreeToTerms: false,
    accountType: 'client',
    business_name: '',
    country: '',
    payout_currency: '',
    transfer_type: '',
    assets: []
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  
  // Added states for assets and services
  const [assets, setAssets] = useState([]);
  const [selectedAssets, setSelectedAssets] = useState([]);
  const [selectedTransferTypes, setSelectedTransferTypes] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedCountry, setSelectedCountry] = useState(null);

  // Fetch assets on component mount
  useEffect(() => {
   
  }, []);

  const backToIndex = async () => {
    navigate('/');
  }

  return (
    <Page title="Register">
      <RootStyle>
        <Container maxWidth="sm" sx={{ margibnTop: '30px', display: 'flex', alignItems: 'center', minHeight: '100vh' }}>
         
          <Paper sx={{ 
            width: '100%',
            mx: 'auto', 
            overflow: 'hidden',
            borderRadius: 3,
            boxShadow: '0 10px 40px rgba(0,0,0,0.1)',
            bgcolor: 'white',
            p: { xs: 3, md: 4 },
          }}>

            <Box sx={{ mb: 4, textAlign: 'center' }}>
              <Box sx={{ display: 'flex', justifyContent: 'center', mb: 3 }}>
                <Logo />
              </Box>
              <Typography variant="h4" gutterBottom fontWeight="700" color="#1F50C2">
                 Your provider account request was successfully submitted.
              </Typography>
              <Typography variant="body2" color="text.secondary">
                 Our support team will respond to your request shortly.
              </Typography>
            </Box>

          
            <Box component="form">
                <Button
                  fullWidth
                  size="large"
                  type="submit"
                  variant="contained"
                  color="primary"
                  disabled={isSubmitting}
                  onClick={() => backToIndex()}
                  sx={{ 
                    height: 56, 
                    mt: 2,
                    bgcolor: '#1F50C2',
                    '&:hover': {
                      bgcolor: '#1a3fa3',
                    },
                  }}
                >
                  {isSubmitting ? <CircularProgress size={24} color="inherit" /> : 'Back to Home'}
                </Button>
            </Box>


          </Paper>
        </Container>
      </RootStyle>
    </Page>
  );
}
