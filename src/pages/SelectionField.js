import React from 'react';
import { Grid, FormControl, InputLabel, Select, MenuItem, Typography } from '@mui/material';
import { Controller } from 'react-hook-form';

const SelectionField = ({ name, label, control, options, error }) => (
  <Grid item xs={12}>
    <Controller
      name={name}
      control={control}
      render={({ field }) => (
        <FormControl fullWidth error={!!error}>
          <InputLabel>{label}</InputLabel>
          <Select {...field}>
            {options.map(option => (
              <MenuItem key={option.value} value={option.value}>
                {option.label}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      )}
    />
    {error && <Typography color="error">{error.message}</Typography>}
  </Grid>
);

export default SelectionField;
