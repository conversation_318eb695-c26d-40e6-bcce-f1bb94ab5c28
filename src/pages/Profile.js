import React, { useState, useEffect } from 'react';
// @mui
import {
  Container,
  Typography,
  Box,
  Paper,
  Grid,
  Divider,
  Chip,
  <PERSON>ton,
  Card,
  CardContent,
  Avatar,
  Stack
} from '@mui/material';
import {
  User,
  Mail,
  Building,
  Globe,
  CreditCard,
  Shield,
  ExternalLink
} from 'lucide-react';
// components
import Page from '../components/Page';

// ----------------------------------------------------------------------

const KYC_STATUS_CONFIG = {
  unverified: {
    color: 'warning',
    label: 'Unverified',
    description: 'Your account is pending verification. Submit your KYC documents to unlock all features.'
  },
  pending: {
    color: 'info',
    label: 'Pending Review',
    description: 'Your KYC documents are being reviewed. This process may take 1-2 business days.'
  },
  verified: {
    color: 'success',
    label: 'Verified',
    description: 'Your account is fully verified. You have access to all platform features.'
  },
  rejected: {
    color: 'error',
    label: 'Rejected',
    description: 'Your verification was not approved. Please submit new documents or contact support.'
  }
};

export default function Profile() {
  const [userData, setUserData] = useState(null);
  const [userType, setUserType] = useState(null);
  const [kycStatus, setKycStatus] = useState('unverified');

  useEffect(() => {
    // Get user data from localStorage
    const storedUserData = localStorage.getItem('userObject');
    const storedUserType = localStorage.getItem('userType');
    const storedKycStatus = localStorage.getItem('kycStatus');
    
    if (storedUserData) {
      try {
        const parsedData = JSON.parse(storedUserData);
        setUserData(parsedData);
      } catch (error) {
        console.error('Error parsing user data:', error);
      }
    }
    
    if (storedUserType) {
      setUserType(storedUserType);
    }
    
    if (storedKycStatus) {
      setKycStatus(storedKycStatus);
    }
  }, []);

  // Get KYC status configuration based on current status
  const kycStatusConfig = KYC_STATUS_CONFIG[kycStatus] || KYC_STATUS_CONFIG.unverified;

  if (!userData) {
    return (
      <Page title="Profile">
        <Container maxWidth="lg" sx={{ py: 5 }}>
          <Typography variant="h5">Loading profile information...</Typography>
        </Container>
      </Page>
    );
  }

  return (
  
        <Page title="Profile">
          <Container maxWidth="lg" sx={{ py: 5 }}>
            <Typography variant="h4" gutterBottom fontWeight={600}>
              My Profile
            </Typography>
            
            <Grid container spacing={3}>
              {/* User Information Card */}
              <Grid item xs={12} md={4}>
                <Card sx={{ height: '100%' }}>
                  <CardContent>
                    <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mb: 3 }}>
                      <Avatar
                        sx={{
                          width: 100,
                          height: 100,
                          backgroundColor: 'primary.main',
                          fontSize: 40,
                          mb: 2
                        }}
                      >
                        {userData.first_name?.charAt(0) || ''}
                      </Avatar>
                      <Typography variant="h5" fontWeight={600}>
                        {userData.first_name} {userData.last_name}
                      </Typography>
                      <Chip
                        label={userType === 'provider' ? 'Provider' : 'Client'}
                        color={userType === 'provider' ? 'primary' : 'default'}
                        size="small"
                        sx={{ mt: 1 }}
                      />
                    </Box>
                    
                    <Divider sx={{ my: 2 }} />
                    
                    <Stack spacing={2}>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Mail size={20} color="#666" style={{ marginRight: 12 }} />
                        <Typography variant="body2">{userData.email || 'N/A'}</Typography>
                      </Box>
                      
                      {userType === 'provider' && (
                        <>
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <Building size={20} color="#666" style={{ marginRight: 12 }} />
                            <Typography variant="body2">{userData.business_name || 'N/A'}</Typography>
                          </Box>
                          
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <Globe size={20} color="#666" style={{ marginRight: 12 }} />
                            <Typography variant="body2">{userData.country || 'N/A'}</Typography>
                          </Box>
                          
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <CreditCard size={20} color="#666" style={{ marginRight: 12 }} />
                            <Typography variant="body2">{userData.payout_currency || 'N/A'}</Typography>
                          </Box>
                        </>
                      )}
                    </Stack>
                  </CardContent>
                </Card>
              </Grid>
              
              {/* KYC Status Card */}
              <Grid item xs={12} md={8}>
                <Card>
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                      <Shield size={24} style={{ marginRight: 12 }} />
                      <Typography variant="h6" fontWeight={600}>
                        KYC Verification Status
                      </Typography>
                    </Box>
                    
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                      <Chip
                        label={kycStatusConfig.label}
                        color={kycStatusConfig.color}
                        sx={{ mr: 2 }}
                      />
                      <Typography variant="body2" color="text.secondary">
                        {kycStatusConfig.description}
                      </Typography>
                    </Box>
                    
                    {kycStatus === 'unverified' && (
                      <Button
                        variant="contained"
                        color="primary"
                        startIcon={<Shield size={16} />}
                        endIcon={<ExternalLink size={16} />}
                        component="a"
                        href="https://b2b.unifid.io"
                        target="_blank"
                        rel="noopener noreferrer"
                        sx={{ mt: 2 }}
                      >
                        Complete KYC Verification
                      </Button>
                    )}
                    
                    {kycStatus === 'rejected' && (
                      <Button
                        variant="contained"
                        color="error"
                        startIcon={<Shield size={16} />}
                        endIcon={<ExternalLink size={16} />}
                        component="a"
                        href="https://b2b.unifid.io"
                        target="_blank"
                        rel="noopener noreferrer"
                        sx={{ mt: 2 }}
                      >
                        Resubmit KYC Documents
                      </Button>
                    )}
                  </CardContent>
                </Card>
                
                {userType === 'provider' && (
                  <Paper sx={{ p: 3, mt: 3 }}>
                    <Typography variant="h6" gutterBottom fontWeight={600}>
                      Provider Resources
                    </Typography>
                    <Typography variant="body2" paragraph color="text.secondary">
                      Access important documents and resources to help you operate effectively on our platform.
                    </Typography>
                    <Button
                      variant="outlined"
                      endIcon={<ExternalLink size={16} />}
                      component="a"
                      href="https://docs.liquidityrail.com"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      Provider Documentation
                    </Button>
                  </Paper>
                )}
              </Grid>
            </Grid>
          </Container>
        </Page>

  );
}
