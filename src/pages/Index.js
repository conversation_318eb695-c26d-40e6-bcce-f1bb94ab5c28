import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { useNavigate } from 'react-router-dom';
import {
  AppBar,
  Toolbar,
  Typography,
  Button,
  Box,
  Container,
  Grid,
  Link,
  Card,
  CardContent,
  IconButton,
  useMediaQuery,
  Drawer,
  List,
  ListItem,
  ListItemText
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { Facebook, Twitter, LinkedIn, Instagram, Menu as MenuIcon, Security, Speed, Payments, Public } from '@mui/icons-material';
import PaymentWidget from './PaymentWidget';
import PaymentForm from './PaymentForm';
import Logo from '../components/Logo';
import "../assets/css/style.css";
import Collections from '../assets/images/casestudies/1.gif';
import Payouts from '../assets/images/casestudies/2.gif';
import Transfers from '../assets/images/casestudies/3.gif';

import LoginPopup from '../components/auth/LoginPopup';

const Tab = ({ label, isActive, onClick }) => (
  <button
    type="button"
    onClick={onClick}
    className={`flex items-center gap-2 px-6 py-3 rounded-full transition-all duration-300 default-casestudy-btn ${
      isActive 
        ? 'bg-blue-500 text-white shadow-lg default-casestudy-btn-active' 
        : 'bg-white text-gray-600 hover:bg-gray-50'
    }`}
  >
    <span className="font-medium">{label}</span>
  </button>
);

Tab.propTypes = {
  label: PropTypes.string.isRequired,
  isActive: PropTypes.bool.isRequired,
  onClick: PropTypes.func.isRequired,
};

// Import the about image asset
const P2PWebsiteLayout = () => {
  const [user, setUser] = useState(null);
  const [userAuth, setUserAuth] = useState(null);
  const [userCheck, setUserCheck] = useState(true);
  const [openForm, setopenForm] = useState(false);

  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('wallets');
  
  const theme = useTheme();
  const navigate = useNavigate();

  // Check localStorage for JWT on mount
  useEffect(() => {
    const userAuthToken = localStorage.getItem('authJWT');
    setUserAuth(userAuthToken);

    const userObject = localStorage.getItem('userObject');
    if (userObject) {
      try {
        const decoded = JSON.parse(userObject);
        setUser(decoded);
      } catch (error) {
        console.error('Invalid user object in local storage');
        localStorage.removeItem('userObject');
      }
    }
  }, []);

  // on successful login
  const onLogin = (e) => {
    setUser(e);
    setopenForm(false);
    const returnPath = sessionStorage.getItem('afterLoginRedirect');
    const userAuthToken = localStorage.getItem('authJWT');
    setUserAuth(userAuthToken);
    if (returnPath) {
      sessionStorage.removeItem('afterLoginRedirect');
      navigate(returnPath);
    }
  };

  const closeForm = () => {
    setopenForm(false);
  };
  
  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  const scrollToSection = (sectionId) => {
    const section = document.getElementById(sectionId);
    if (section) {
      section.scrollIntoView({ behavior: 'smooth' });
    }
    setMobileMenuOpen(false);
  };

  const APIDocumentationRedirect = () => {
    window.open('https://payments-doc.muda.tech', '_blank');
  };

  const tabs = [
    { id: 'wallets', label: 'Wallets', icon: "" },
    { id: 'fintechs-and-aggregators', label: 'Fintechs and Aggregators', icon: "" },
    { id: 'remittance', label: 'Remittance', icon: "" },
  ];

  const onLoginOpen = () => {
    setopenForm(true);
  };
  
  return (
    <Box sx={{ 
      minHeight: '100vh', 
      bgcolor: '#0C1323', 
      display: 'flex',
      flexDirection: 'column',
    }}>

      {/* Header/Navigation */}
      <AppBar 
        position="static" 
        color="transparent" 
        elevation={0}
        sx={{ 
          borderBottom: 'none',
          background: 'transparent'
        }}
      >
        <Container maxWidth="xl">
          <Toolbar sx={{ justifyContent: 'space-between', py: 2 }}>
            {/* Replace text logo with actual Logo component */}
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Logo sx={{ height: 90, mr: 2 }} />
            </Box>
            <Box sx={{ display: { xs: 'none', md: 'flex' }, gap: 2 }}>
              <Button 
                variant="text" 
                color="inherit" 
                onClick={() => scrollToSection('hero')}
                sx={{ 
                  color: 'white',
                  '&:hover': {
                    backgroundColor: 'rgba(255, 255, 255, 0.1)'
                  }
                }}
              >
                Home
              </Button>
              <Button 
                variant="text" 
                color="inherit" 
                onClick={() => scrollToSection('about')}
                sx={{ 
                  color: 'white',
                  '&:hover': {
                    backgroundColor: 'rgba(255, 255, 255, 0.1)'
                  }
                }}
              >
                About
              </Button>
              <Button 
                variant="text" 
                color="inherit" 
                component="a"
                href="https://payments-doc.muda.tech"
                target="_blank"
                rel="noopener noreferrer"
                sx={{ 
                  color: 'white',
                  '&:hover': {
                    backgroundColor: 'rgba(255, 255, 255, 0.1)'
                  }
                }}
              >
                Docs
              </Button>
              {(userAuth != null) ? (
                <Button 
                  variant="contained" 
                  color="primary"
                  onClick={() => navigate('/dashboard')}
                  sx={{ 
                    fontWeight: 'bold'
                  }}
                >
                  Dashboard
                </Button>
              ) : (
                <Button 
                  variant="outlined" 
                  color="primary"
                  onClick={() => navigate('/login')}
                  sx={{ 
                    color: 'white',
                    borderColor: 'white',
                    '&:hover': {
                      borderColor: '#1F50C2',
                      backgroundColor: 'rgba(31, 80, 194, 0.1)'
                    }
                  }}
                >
                  Login
                </Button>
              )}
            </Box>
            
            {/* Mobile menu button */}
            <IconButton 
              color="inherit" 
              onClick={toggleMobileMenu} 
              sx={{ 
                display: { xs: 'block', md: 'none' },
                color: 'white'
              }}
            >
              <MenuIcon />
            </IconButton>


          </Toolbar>
        </Container>
      </AppBar>

      {/* Hero Section with Form */}
      <Box 
        component="main" 
        id="hero"
        sx={{ 
          flexGrow: 1,
          display: 'flex',
          alignItems: 'center',
          pt: { xs: 0, md: 2 },
          pb: { xs: 2, md: 4 }
        }}
      >
        <Container maxWidth="xl">
          <Grid container spacing={3} alignItems="center">
            {/* Left side - Hero Text */}
            <Grid item xs={12} md={6} sx={{ color: 'white' }}>
              <Typography 
                variant="h2" 
                component="h1" 
                sx={{ 
                  fontWeight: 700, 
                  mb: 2,
                  mt: { xs: 0, md: -6 },
                  fontSize: { xs: '3.0rem', md: '3.1rem' },
                  color: '#1F50C2'
                }}
              >
                Send money anywhere, <span style={{ color: 'white' }}>instantly.</span>
              </Typography>
              <Typography 
                variant="h6" 
                sx={{ 
                  mb: 3,
                  fontWeight: 400,
                  lineHeight: 1.5,
                  fontSize: { xs: '1.2rem', md: '1.2rem' },
                  maxWidth: '90%'
                }}
              >
                The Liquidity Rail unifies different payment systems using stablecoins for faster payments. Our mission is to eliminate pre-funding, simplify currency conversion, streamline liquidity sourcing and facilitate global currency settlement.
              </Typography>
              <Button 
                variant="contained" 
                size="large" 
                sx={{ 
                  py: 1.5,
                  px: 4,
                  fontSize: '1.1rem',
                  fontWeight: 600,
                  borderRadius: '50px',
                  backgroundColor: '#1F50C2',
                  '&:hover': {
                    backgroundColor: '#0F2B77',
                  }
                }}
                onClick={() => {
                    const url = (userAuth != null) ? '/dashboard' : '/login';
                    navigate(url)
                }}
              >
                Try it now
              </Button>
            </Grid>
            
            {/* Right side - Exchange Form */}
            <Grid item xs={12} md={6}>
              <Card sx={{ 
                maxWidth: 550, 
                mx: 'auto',
                borderRadius: '16px',
                overflow: 'hidden',
                boxShadow: '0 10px 40px rgba(0, 0, 0, 0.2)',
              }}>
                <CardContent sx={{ p: 0 }}>
                  {userAuth != null? 
                   (<PaymentWidget   isOpen={openForm} onClose={closeForm} onSuccess={onLogin} onLoginOpen={onLoginOpen} />)
                  :(<PaymentForm  isOpen={openForm} onClose={closeForm} onSuccess={onLogin} onLoginOpen={onLoginOpen} />)}
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Container>
      </Box>


      {/* About Liquidity Rail section */}
      <Box id="about" sx={{ py: 8, bgcolor: '#ffffff' }}>
        <Container maxWidth="lg">
          <Box
                sx={{ textAlign: 'center', mb: 6, ml: 2, mr: 2 }}
                sm={{ textAlign: 'center', mb: 6, ml: 12, mr: 12 }} >
            <Typography variant="h3" component="h2" gutterBottom fontWeight="700">
              A better and more seamless way to transact in and out of stablecoins
            </Typography>
          </Box>

          <Grid container spacing={4} alignItems="center">

                  {/* Feature 1 */}
                  <Grid item xs={12} sm={6} md={3}>
                    <Box sx={{ 
                      textAlign: 'center', 
                      py: 4,
                      px: 3,
                      border: '1px solid #e0e0e0',
                      borderRadius: '12px',
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      backgroundColor: 'white',
                      transition: 'transform 0.3s ease, box-shadow 0.3s ease',
                      '&:hover': {
                        transform: 'translateY(-8px)',
                        boxShadow: '0 10px 30px rgba(0, 0, 0, 0.1)',
                      }
                    }}
                    className="service-main-box">
                      <Box 
                          component="img" 
                          src="assets/images/rail/StableCoin.png"
                          alt="stable coins"
                          sx={{
                            width: '30%',
                            maxWidth: '60px',
                            height: 'auto',
                            borderRadius: 3,
                            marginBottom: '8px',
                            background: 'transparent',
                            boxShadow: '0 10px 30px rgba(0, 0, 0, 0)'
                          }}
                          className="service-main-image"
                      />

                      <Typography variant="h6" component="h3" gutterBottom fontWeight="600">
                        Stablecoin On-Ramp
                      </Typography>
                      <Typography variant="body2" 
                                  color="text.secondary" 
                                  className="service-main-content">
                        Our rails enable merchants and fintechs to collect payments from their customer 
                        in fiat or digital currency across several payment methods
                      </Typography>
                    </Box>
                  </Grid>


                  <Grid item xs={12} sm={6} md={3}>
                    <Box sx={{ 
                      textAlign: 'center', 
                      py: 4,
                      px: 3,
                      border: '1px solid #e0e0e0',
                      borderRadius: '12px',
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      backgroundColor: 'white',
                      transition: 'transform 0.3s ease, box-shadow 0.3s ease',
                      '&:hover': {
                        transform: 'translateY(-8px)',
                        boxShadow: '0 10px 30px rgba(0, 0, 0, 0.1)',
                      }
                    }} 
                    className="service-main-box">
                      <Box 
                          component="img"
                          src="assets/images/rail/MobileMoney.png"
                          alt="mobile money"
                          sx={{
                            width: '30%',
                            maxWidth: '60px',
                            height: 'auto',
                            borderRadius: 0,
                            marginBottom: '8px',
                            marginTop: '2px',
                            background: 'transparent',
                            boxShadow: '0 10px 30px rgba(0, 0, 0, 0)'
                          }}
                          className="service-main-image"
                      />
                      <Typography variant="h6" component="h3" gutterBottom fontWeight="600">
                         Fiat Off-Ramp
                      </Typography>

                      <Typography variant="body2" color="text.secondary" 
                                  className="service-main-content">
                        Convert digital currency to fiat for seemless global settlements
                      </Typography>
                    </Box>
                  </Grid>

                  <Grid item xs={12} sm={6} md={3}>
                    <Box sx={{ 
                      textAlign: 'center', 
                      py: 4,
                      px: 3,
                      border: '1px solid #e0e0e0',
                      borderRadius: '12px',
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      backgroundColor: 'white',
                      transition: 'transform 0.3s ease, box-shadow 0.3s ease',
                      '&:hover': {
                        transform: 'translateY(-8px)',
                        boxShadow: '0 10px 30px rgba(0, 0, 0, 0.1)',
                      }
                    }}
                    className="service-main-box">
                     <Box 
                          component="img" 
                          src="assets/images/rail/CurrencySwapping.png"
                          alt="mobile money"
                          sx={{
                            width: '30%',
                            maxWidth: '60px',
                            height: 'auto',
                            borderRadius: 3,
                            marginBottom: '8px',
                            background: 'transparent',
                            boxShadow: '0 10px 30px rgba(0, 0, 0, 0)'
                          }}
                          className="service-main-image"
                      />
                      <Typography variant="h6" component="h3" gutterBottom fontWeight="600">
                        Currency Swapping Tool
                      </Typography>
                      <Typography variant="body2" color="text.secondary" 
                                  className="service-main-content">
                        Effortlessly exchange currencies or bridge stablecoins from one chain to another
                      </Typography>
                    </Box>
                  </Grid>

                  <Grid item xs={12} sm={6} md={3}>
                    <Box sx={{ 
                      textAlign: 'center', 
                      py: 4,
                      px: 3,
                      border: '1px solid #e0e0e0',
                      borderRadius: '12px',
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      backgroundColor: 'white',
                      transition: 'transform 0.3s ease, box-shadow 0.3s ease',
                      '&:hover': {
                        transform: 'translateY(-8px)',
                        boxShadow: '0 10px 30px rgba(0, 0, 0, 0.1)',
                      }
                    }} 
                    className="service-main-box">
                      <Box 
                          component="img" 
                          src="assets/images/rail/Integratedcompliance2.png"
                          alt="Integrated compliance"
                          sx={{
                            width: '30%',
                            maxWidth: '60px',
                            height: 'auto',
                            borderRadius: 3,
                            marginBottom: '8px',
                            background: 'transparent',
                            boxShadow: '0 10px 30px rgba(0, 0, 0, 0)'
                          }}
                          className="service-main-image"
                      />
                      <Typography variant="h6" component="h3" gutterBottom fontWeight="600">
                        Integrated Compliance.
                      </Typography>
                      <Typography variant="body2" color="text.secondary" 
                                  className="service-main-content">
                        Ensures all regulatory standards are fully met, providing a secure and reliable experience for users
                      </Typography>
                    </Box>
                  </Grid>
            
          </Grid>
        </Container>
      </Box>

      {/* prefund Liquidity Rail section */}
      <Box id="about" sx={{ py: 3, bgcolor: '#F3F3F5' }} 
                      md={{ py: 8, bgcolor: '#F3F3F5' }}>
        <Container maxWidth="lg">

          <Box sx={{ textAlign: 'center', mb: 2, ml: 3, mr: 3 }}
               md={{ textAlign: 'center', mb: 6, ml: 12, mr: 12 }}>
            <Typography variant="h3" component="h2" gutterBottom fontWeight="700">
              Our Difference
            </Typography>
            <Typography variant="p" component="p" gutterBottom fontWeight="500">
              We are eliminating the need to pre-fund with partners by using stablecoins for payment notification and settlement
            </Typography>
          </Box>
          <Grid container spacing={2} alignItems="center">
            {/* Feature 1 */}
            <Grid item xs={12} sm={12} md={12}>
                    <Box sx={{ 
                      textAlign: 'center', 
                      py: 4,
                      px: 3,
                      borderRadius: '12px',
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      backgroundColor: 'transparent',
                      transition: 'transform 0.3s ease, box-shadow 0.3s ease',
                      '&:hover': {
                        transform: 'translateY(-8px)',
                      }
                    }}>
                      <Box 
                          component="img" 
                          src="assets/images/rail/prefunding.jpg"
                          alt="The Future of Cross-Border Payments"

                          sx={{
                            width: '95%',
                            height: 'auto',
                            borderRadius: 3,
                            boxShadow: '0 10px 30px rgba(0, 0, 0, 0)'
                          }}

                          sm={{
                            width: '60%',
                            height: 'auto',
                            borderRadius: 3,
                            boxShadow: '0 10px 30px rgba(0, 0, 0, 0)'
                          }}
                          />
                      </Box>

            </Grid>
          </Grid>         
        </Container>
      </Box>
      

      {/* prefund Liquidity Rail section */}
      <Box id="about" sx={{ py: 8, bgcolor: '#ffffff' }}>
        <Container maxWidth="lg">
          <Grid container spacing={4} alignItems="center">
          <Grid item xs={12} sm={5} md={5}>
                    <Box sx={{ 
                      textAlign: 'left', 
                      py: 4,
                      px: 3,
                      borderRadius: '12px',
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'left',
                    }}>

                      <Typography variant="h4" component="h3" gutterBottom fontWeight="600" align="left">
                        Multi-Purpose Rails Support
                      </Typography>
                      <Typography variant="body2" color="text.secondary" align="left" size="22">
                        Liquidity Rail provides a single API with multiple fiat rails in Africa and beyond.
                      </Typography>
                    </Box>
            </Grid>

            {/* Feature 1 */}
            <Grid item xs={12} sm={7} md={7}>
                    <Box sx={{ 
                      textAlign: 'center', 
                      py: 4,
                      px: 3,
                      border: '0 solid #e0e0e0',
                      borderRadius: '12px',
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      backgroundColor: 'white',
                      transition: 'transform 0.3s ease, box-shadow 0.3s ease',
                      '&:hover': {
                        transform: 'translateY(-8px)',
                        boxShadow: '0 10px 30px rgba(0, 0, 0, 0)',
                      }
                    }}>
                      
                      <Box 
                          component="img" 
                          src="assets/images/rail/GlobalIntegration.png"
                          alt="mobile money"
                          sx={{
                            width: '70%',
                            height: 'auto',
                            borderRadius: 3,
                            marginBottom: '5px',
                            background: 'transparent',
                            boxShadow: '0 10px 30px rgba(0, 0, 0, 0)'
                          }}
                      />
                      </Box>
                    
            </Grid>

          </Grid>         
        </Container>
      </Box>


      <Box id="about" sx={{ py: 3, bgcolor: '#0C1323' }} 
                      md={{ py: 8, bgcolor: '#0C1323' }}>
        <Container maxWidth="lg">
          <Box sx={{ 
                      textAlign: 'center', 
                      py: 2,
                      px: 3,
                      border: '0 solid #e0e0e0',
                      borderRadius: '0',
                      height: '90%',
                      display: 'flex',
                      flexDirection: 'column',
                      justifyContent: 'left',
                      alignItems: 'left',
                      backgroundColor: '#0C1323',
                      transition: 'transform 0.3s ease, box-shadow 0.3s ease',
                      '&:hover': {
                        transform: 'translateY(-8px)'
                      }
                    }}
                    
               md={{ 
                      textAlign: 'center', 
                      py: 2,
                      px: 15,
                      border: '0 solid #e0e0e0',
                      borderRadius: '0',
                      height: '70%',
                      display: 'flex',
                      flexDirection: 'column',
                      justifyContent: 'left',
                      alignItems: 'left',
                      backgroundColor: '#0C1323',
                      transition: 'transform 0.3s ease, box-shadow 0.3s ease',
                      '&:hover': {
                        transform: 'translateY(-8px)'
                      }
                    }}>
                      
                <iframe 
                    src="https://www.youtube.com/embed/v87DEmwRAYU?si=uxrUWjo4zZK67BQ9" 
                    title="Liquidity rail by MUDA" 
                    style={{ height: '450px', border: 'none' }} 
                    allowFullScreen 
                />

            </Box>
        </Container>
      </Box>

      {/* prefund Liquidity Rail section */}
      <Box id="about" sx={{ pt: 8, pb: 5, bgcolor: '#ffffff' }}>
        <Container maxWidth="lg">
         
          <Box sx={{ textAlign: 'center', mb: 3 }}>
            
            <Typography variant="h3" component="h2" gutterBottom fontWeight="700">
              Liquidity Rail Use Cases
            </Typography>

            <Typography variant="body1" color="text.secondary" sx={{ maxWidth: '800px', mx: 'auto', mb: 2 }}>
              The infrastructure provides efficient transfer of assets across different parties, networks, or markets, ensuring quick and seamless exchanges in a number of real-world aspects.
            </Typography>

          </Box>   

          <Grid container spacing={3} alignItems="center"  sx={{ mx: 'auto', mb: 6, pl: 1, pr: 6 }}>
            <Grid item xs={12} sm={12} md={12} sx={{ mx: 'auto', mb: 6, pl: 1, pr: 0 }}>
             <div className="min-h-screen bg-gray-50">
               

              <Grid container spacing={6} alignItems="center"  className="mt-6">
                  <Grid item xs={12} md={12} className="flex gap-3 text-center justify-center">
                    {tabs.map((tab) => (
                            <Tab
                              key={tab.id}
                              label={tab.label}
                              isActive={activeTab === tab.id}
                              onClick={() => setActiveTab(tab.id)}
                            />
                          ))}
                  </Grid>  
                    

                  <Grid item xs={12} md={5}>
                    <div className="mb-8 mt-8">
                      <h1 className="text-4xl font-bold text-gray-900">{activeTab.charAt(0).toUpperCase() + activeTab.slice(1).replaceAll('-', ' ')}</h1>
                      {activeTab === 'wallets' && (
                        <p className="mt-4 text-lg text-gray-600 max-w-3xl">
                          Use a single stablecoin balance to enable direct withdrawals and payouts into several fiat currencies.
                        </p>
                      )}
                      {activeTab === 'fintechs-and-aggregators' && (
                        <p className="mt-4 text-lg text-gray-600 max-w-3xl">
                          Tap into our open-source technologies to join the Web3 revolution and become a Liquidity Rail node. This allows you to access transaction flows from all the connected partners.
                        </p>
                      )}
                      {activeTab === 'remittance' && (
                        <p className="mt-4 text-lg text-gray-600 max-w-3xl">
                          Cut out pre-funding and join the Liquidity Rail to get access to faster payments, better forex rates, multiple partners through a single connection, and deep liquidity across emerging markets.
                        </p>
                      )}
                    </div>
                  </Grid>



                  <Grid item xs={12} md={7}>
                    <div className="mb-8 mt-8">
                      {activeTab === 'wallets' && (

                            <Box 
                                component="img" 
                                src={Collections}
                                alt="wallets"
                                sx={{
                                  maxWidth: '100%',
                                  height: 'auto',
                                  maxHeight: '320px',
                                  float:   'left',
                                  borderRadius: 3,
                                  marginBottom: '5px',
                                  background: 'transparent',
                                  boxShadow: '0 10px 30px rgba(0, 0, 0, 0)'
                                }}
                              />

                      )}
                      {activeTab === 'fintechs-and-aggregators' && (

                          <Box 
                            component="img" 
                            src={Payouts}
                            alt="fintechs-and-aggregators"
                            sx={{
                              maxWidth: '100%',
                              height: 'auto',
                              maxHeight: '320px',
                              float:   'left',
                              borderRadius: 3,
                              marginBottom: '5px',
                              background: 'transparent',
                              boxShadow: '0 10px 30px rgba(0, 0, 0, 0)'
                            }}
                          />
                      
                      )}
                      {activeTab === 'remittance' && (
                        
                        <Box 
                            component="img" 
                            src={Transfers}
                            alt="remittance"
                            sx={{
                              maxWidth: '100%',
                              height: 'auto',
                              maxHeight: '320px',
                              float:   'left',
                              borderRadius: 3,
                              marginBottom: '5px',
                              background: 'transparent',
                              boxShadow: '0 10px 30px rgba(0, 0, 0, 0)'
                            }}
                        />
                      )}
                    </div>
                  </Grid>


                </Grid>    
              </div>
            </Grid> 
          </Grid>

          <Grid container spacing={3} alignItems="center" className="hidden show_hide"
          sx={{ mx: 'auto', mb: 6, pl: 1, pr: 6 }}>
          
            <Grid item xs={12} sm={4} md={4} >
              <Box sx={{ 
                      textAlign: 'center', 
                      py: 2,
                      px: 3,
                      border: '1px solid #e0e0e0',
                      borderRadius: '0',
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      justifyContent: 'left',
                      alignItems: 'left',
                      backgroundColor: 'white',
                      transition: 'transform 0.3s ease, box-shadow 0.3s ease',
                      '&:hover': {
                        transform: 'translateY(-8px)'
                      }
                    }}
                    className="casestudy-main-box" >
                      
                      <Box 
                          component="img" 
                          src="assets/images/rail/ForWallets.png"
                          alt="mobile money"
                          sx={{
                            width: '40px',
                            height: 'auto',
                            float:   'left',
                            borderRadius: 3,
                            marginBottom: '5px',
                            background: 'transparent',
                            boxShadow: '0 10px 30px rgba(0, 0, 0, 0)'
                          }}
                      />
                      <Box sx={{ fontSize: 16}}>
                        <Typography variant="h5" component="h3" gutterBottom fontWeight="700" className="case-study-title"  align="left">
                          For wallets
                        </Typography>
                        <Typography variant="body1" paragraph align="left">
                          Use a single stablecoin balance to enable direct withdrawals and payouts into several fiat currencies.
                        </Typography>
                      </Box>
                      
              </Box>
            </Grid>

            
            <Grid item xs={12} sm={4} md={4} >
              <Box sx={{ 
                      textAlign: 'left', 
                      py: 2,
                      px: 3,
                      border: '1px solid #e0e0e0',
                      borderRadius: '0',
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      justifyContent: 'left',
                      alignItems: 'left',
                      backgroundColor: 'white',
                      transition: 'transform 0.3s ease, box-shadow 0.3s ease',
                      '&:hover': {
                        transform: 'translateY(-8px)'
                      }
                    }}
                    className="casestudy-main-box">
                      
                      <Box 
                          component="img" 
                          src="assets/images/rail/ForAggregators.png"
                          alt="mobile money"
                          sx={{
                            width: '40px',
                            height: 'auto',
                            float:   'left',
                            borderRadius: 3,
                            marginBottom: '5px',
                            background: 'transparent',
                            boxShadow: '0 10px 30px rgba(0, 0, 0, 0)'
                          }}
                      />

                      <Box sx={{ fontSize: 16}}>
                        <Typography variant="h5" component="h3" gutterBottom fontWeight="700" className="case-study-title"  align="left">
                          For fintechs and aggregators
                        </Typography>
                        <Typography variant="body1" paragraph align="left">
                          Tap into our open-source technologies to join the Web3 revolution and become a Liquidity Rail node. This allows you to access transaction flows from all the connected partners.
                        </Typography>
                      </Box>
              </Box>
            </Grid>

            <Grid item xs={12} sm={4} md={4} >
              <Box sx={{ 
                      textAlign: 'left', 
                      py: 2,
                      px: 3,
                      border: '1px solid #e0e0e0',
                      borderRadius: '0',
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      justifyContent: 'left',
                      alignItems: 'left',
                      backgroundColor: 'white',
                      transition: 'transform 0.3s ease, box-shadow 0.3s ease',
                      '&:hover': {
                        transform: 'translateY(-8px)'
                      }
                    }}
                    className="casestudy-main-box">
                      <Box 
                          component="img" 
                          src="assets/images/rail/ForFxRemittances.png"
                          alt="mobile money"
                          sx={{
                            width: '45px',
                            height: 'auto',
                            float:   'left',
                            borderRadius: 3,
                            marginBottom: '5px',
                            background: 'transparent',
                            boxShadow: '0 10px 30px rgba(0, 0, 0, 0)'
                          }}
                      />
                      <Box sx={{ fontSize: 16}}>
                        <Typography variant="h5" component="h3" gutterBottom fontWeight="700" className="case-study-title"  align="left">
                          For remittance 
                        </Typography>
                        <Typography variant="body1" paragraph align="left">
                          Cut out pre-funding and join the Liquidity Rail to get access to faster payments, better forex rates, multiple partners through a single connection, and deep liquidity across emerging markets.
                        </Typography>
                      </Box>
              </Box>
            </Grid>
          </Grid>         
        </Container>
      </Box>

      <Box id="about" className="lower-background" sx={{ py: 8}}>
        <Container maxWidth="lg">
          <Box sx={{ textAlign: 'center', mb: 6 }}>
            <Typography variant="h3" component="h2" gutterBottom fontWeight="700">
              Designed for developers first
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ maxWidth: '800px', mx: 'auto', mb: 6 }}>
            We provide tools that enable developers to help Web2 businesses transition to Web3 with ease
            </Typography>
          </Box>

          <Grid container spacing={6} alignItems="center">


            <Grid item xs={12} md={6}>
              <Box>
                <Typography variant="h5" component="h3" gutterBottom fontWeight="700">
                  Open source system  
                </Typography>
                <Typography variant="body1" paragraph>
                  We believe in empowering our community with the tools to create great applications
                </Typography>

                <Typography variant="h5" component="h3" gutterBottom fontWeight="700" sx={{ mt: 4 }}>
                  Integration is done in just a few lines of code
                </Typography>
                <Typography variant="body1" paragraph>
                  We have significantly streamlined the core abstractions, allowing your developers to get production-ready API integrations that support multiple stacks with real-time webhooks and event handling.
                </Typography>

                <Typography variant="h5" component="h3" gutterBottom fontWeight="700" sx={{ mt: 4 }}>
                  Customizable use cases made simple.  
                </Typography>
                <Typography variant="body1" paragraph>
                  Our robust APIs provide a highly customizable experience to meet even your most complex requirements.
                </Typography>

                <Typography variant="h5" component="h3" gutterBottom fontWeight="700" sx={{ mt: 4 }}>
                  Accelerated integrations
                </Typography>
                <Typography variant="body1" paragraph>
                  Our platform can be integrated into any app in under 20 minutes, offering a fully customizable solution tailored to your stablecoin payment needs.
                </Typography>

                <Button 
                    variant="contained" 
                    size="large" 
                    onClick={() => APIDocumentationRedirect()}
                    sx={{ 
                      py: 1.5,
                      px: 4,
                      fontSize: '1.1rem',
                      fontWeight: 600,
                      borderRadius: '50px',
                      backgroundColor: '#1F50C2',
                      '&:hover': {
                        backgroundColor: '#0F2B77',
                      }
                    }}
                  >
                    Explore the API documentation
                  </Button>
                
              </Box>
            </Grid>

            <Grid item xs={12} md={6}>
              <Box 
                          component="img" 
                          src="assets/images/the_dev_snippet.png"
                          alt="The Future of Cross-Border Payments"
                          sm={{
                            width: '50%',
                            height: 'auto',
                            borderRadius: 3,
                            boxShadow: '0 10px 30px rgba(0, 0, 0, 0.15)'
                          }}

                          sx={{
                            width: '90%',
                            height: 'auto',
                            borderRadius: 3,
                            boxShadow: '0 10px 30px rgba(0, 0, 0, 0.15)'
                          }}
                        />
            </Grid>  

          </Grid>
        </Container>
      </Box>


































      {/* About Liquidity Rail section */}
      <Box id="about" sx={{ py: 8, bgcolor: '#ffffff' }}  className="hidden show_hide">
        <Container maxWidth="lg">
          <Box sx={{ textAlign: 'center', mb: 6 }}>
            <Typography variant="h3" component="h2" gutterBottom fontWeight="700">
              About Liquidity Rail
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ maxWidth: '800px', mx: 'auto', mb: 6 }}>
              Liquidity Rail is the premier platform for peer-to-peer transactions, offering secure, fast, and transparent
              services that connect you with a global network of trusted partners.
            </Typography>
          </Box>

          <Grid container spacing={6} alignItems="center">
            <Grid item xs={12} md={6}>
              <Box 
                component="img" 
                src="assets/images/about.png"
                alt="The Future of Cross-Border Payments"
                sx={{
                  width: '100%',
                  height: 'auto',
                  borderRadius: 3,
                  boxShadow: '0 10px 30px rgba(0, 0, 0, 0.15)'
                }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Box>
                <Typography variant="h5" component="h3" gutterBottom fontWeight="700">
                  Our Mission
                </Typography>
                <Typography variant="body1" paragraph>
                  To empower individuals and businesses through a secure and user-friendly
                  platform that enables seamless peer-to-peer transactions.
                </Typography>

                <Typography variant="h5" component="h3" gutterBottom fontWeight="700" sx={{ mt: 4 }}>
                  Our Vision
                </Typography>
                <Typography variant="body1" paragraph>
                  To be the leading global network for P2P transactions, connecting
                  communities and fostering trust in every interaction.
                </Typography>

                <Button 
                  variant="outlined" 
                  color="primary" 
                  size="large"
                  sx={{ mt: 2, borderRadius: 2, px: 3, py: 1 }}
                >
                  Learn More
                </Button>
              </Box>
            </Grid>
          </Grid>
        </Container>
      </Box>

      {/* Our Features section */}
      <Box id="features" sx={{ py: 10, bgcolor: '#f8f9fa' }} className="hidden show_hide">
        <Container maxWidth="lg">
          <Box sx={{ textAlign: 'center', mb: 6 }}>
            <Typography variant="h3" component="h2" gutterBottom fontWeight="700">
              Our Features
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ maxWidth: '800px', mx: 'auto', mb: 6 }}>
              Experience the power of a secure and efficient P2P payment platform designed to meet your needs.
            </Typography>
          </Box>

          <Grid container spacing={4}>
            {/* Feature 1 */}
            <Grid item xs={12} sm={6} md={3}>
              <Box sx={{ 
                textAlign: 'center', 
                py: 4,
                px: 3,
                border: '1px solid #e0e0e0',
                borderRadius: '12px',
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                backgroundColor: 'white',
                transition: 'transform 0.3s ease, box-shadow 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-8px)',
                  boxShadow: '0 10px 30px rgba(0, 0, 0, 0.1)',
                }
              }}>
                <Box sx={{ 
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: 80,
                  height: 80,
                  borderRadius: '50%',
                  backgroundColor: 'rgba(31, 80, 194, 0.1)',
                  mb: 3
                }}>
                  <Security sx={{ fontSize: 45, color: '#1F50C2' }} />
                </Box>
                <Typography variant="h6" component="h3" gutterBottom fontWeight="600">
                  Secure Transactions
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Robust encryption and security protocols ensure your transactions are safe.
                </Typography>
              </Box>
            </Grid>

            {/* Feature 2 */}
            <Grid item xs={12} sm={6} md={3}>
              <Box sx={{ 
                textAlign: 'center', 
                py: 4,
                px: 3,
                border: '1px solid #e0e0e0',
                borderRadius: '12px',
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                backgroundColor: 'white',
                transition: 'transform 0.3s ease, box-shadow 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-8px)',
                  boxShadow: '0 10px 30px rgba(0, 0, 0, 0.1)',
                }
              }}>
                <Box sx={{ 
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: 80,
                  height: 80,
                  borderRadius: '50%',
                  backgroundColor: 'rgba(31, 80, 194, 0.1)',
                  mb: 3
                }}>
                  <Speed sx={{ fontSize: 45, color: '#1F50C2' }} />
                </Box>
                <Typography variant="h6" component="h3" gutterBottom fontWeight="600">
                  Fast & Instant
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Experience near-instant transaction speeds for a smooth user experience.
                </Typography>
              </Box>
            </Grid>

            {/* Feature 3 */}
            <Grid item xs={12} sm={6} md={3}>
              <Box sx={{ 
                textAlign: 'center', 
                py: 4,
                px: 3,
                border: '1px solid #e0e0e0',
                borderRadius: '12px',
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                backgroundColor: 'white',
                transition: 'transform 0.3s ease, box-shadow 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-8px)',
                  boxShadow: '0 10px 30px rgba(0, 0, 0, 0.1)',
                }
              }}>
                <Box sx={{ 
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: 80,
                  height: 80,
                  borderRadius: '50%',
                  backgroundColor: 'rgba(31, 80, 194, 0.1)',
                  mb: 3
                }}>
                  <Payments sx={{ fontSize: 45, color: '#1F50C2' }} />
                </Box>
                <Typography variant="h6" component="h3" gutterBottom fontWeight="600">
                  Low Fees
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Enjoy competitive fees with transparent pricing.
                </Typography>
              </Box>
            </Grid>

            {/* Feature 4 */}
            <Grid item xs={12} sm={6} md={3}>
              <Box sx={{ 
                textAlign: 'center', 
                py: 4,
                px: 3,
                border: '1px solid #e0e0e0',
                borderRadius: '12px',
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                backgroundColor: 'white',
                transition: 'transform 0.3s ease, box-shadow 0.3s ease',
                '&:hover': {
                  transform: 'translateY(-8px)',
                  boxShadow: '0 10px 30px rgba(0, 0, 0, 0.1)',
                }
              }}>
                <Box sx={{ 
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: 80,
                  height: 80,
                  borderRadius: '50%',
                  backgroundColor: 'rgba(31, 80, 194, 0.1)',
                  mb: 3
                }}>
                  <Public sx={{ fontSize: 45, color: '#1F50C2' }} />
                </Box>
                <Typography variant="h6" component="h3" gutterBottom fontWeight="600">
                  Global Network
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Connect with users around the world in a trusted network.
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </Container>
      </Box>

      {/* How It Works section */}
      <Box id="how-it-works" sx={{ py: 10, bgcolor: '#ffffff' }} className="hidden show_hide">
        <Container maxWidth="lg">
          <Box sx={{ textAlign: 'center', mb: 6 }}>
            <Typography variant="h3" component="h2" gutterBottom fontWeight="700">
              How It Works
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ maxWidth: '800px', mx: 'auto', mb: 6 }}>
              Our easy-to-follow process helps you send and receive payments effortlessly.
            </Typography>
          </Box>

          <Grid container spacing={4}>
            {/* Step 1 */}
            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{ 
                height: '100%', 
                display: 'flex', 
                flexDirection: 'column', 
                alignItems: 'center',
                p: 3,
                boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                borderRadius: 3
              }}>
                <Typography variant="h2" color="primary" fontWeight="700" sx={{ mb: 2 }}>
                  1
                </Typography>
                <Typography variant="h6" component="h3" gutterBottom fontWeight="600" textAlign="center">
                  Sign Up
                </Typography>
                <Typography variant="body2" color="text.secondary" textAlign="center">
                  Create your free account in minutes.
                </Typography>
              </Card>
            </Grid>

            {/* Step 2 */}
            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{ 
                height: '100%', 
                display: 'flex', 
                flexDirection: 'column', 
                alignItems: 'center',
                p: 3,
                boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                borderRadius: 3
              }}>
                <Typography variant="h2" color="primary" fontWeight="700" sx={{ mb: 2 }}>
                  2
                </Typography>
                <Typography variant="h6" component="h3" gutterBottom fontWeight="600" textAlign="center">
                  Verify Identity
                </Typography>
                <Typography variant="body2" color="text.secondary" textAlign="center">
                  Secure your account with a quick verification process.
                </Typography>
              </Card>
            </Grid>

            {/* Step 3 */}
            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{ 
                height: '100%', 
                display: 'flex', 
                flexDirection: 'column', 
                alignItems: 'center',
                p: 3,
                boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                borderRadius: 3
              }}>
                <Typography variant="h2" color="primary" fontWeight="700" sx={{ mb: 2 }}>
                  3
                </Typography>
                <Typography variant="h6" component="h3" gutterBottom fontWeight="600" textAlign="center">
                  Connect
                </Typography>
                <Typography variant="body2" color="text.secondary" textAlign="center">
                  Find and connect with trusted peers instantly.
                </Typography>
              </Card>
            </Grid>

            {/* Step 4 */}
            <Grid item xs={12} sm={6} md={3}>
              <Card sx={{ 
                height: '100%', 
                display: 'flex', 
                flexDirection: 'column', 
                alignItems: 'center',
                p: 3,
                boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                borderRadius: 3
              }}>
                <Typography variant="h2" color="primary" fontWeight="700" sx={{ mb: 2 }}>
                  4
                </Typography>
                <Typography variant="h6" component="h3" gutterBottom fontWeight="600" textAlign="center">
                  Transact
                </Typography>
                <Typography variant="body2" color="text.secondary" textAlign="center">
                  Send and receive payments seamlessly.
                </Typography>
              </Card>
            </Grid>
          </Grid>
        </Container>
      </Box>
      


      {/* Mobile Menu Drawer - Updated with new menu items */}
      <Drawer
        anchor="right"
        open={mobileMenuOpen}
        onClose={toggleMobileMenu}
        sx={{ 
          '& .MuiDrawer-paper': { 
            width: '70%', 
            maxWidth: '300px',
            paddingTop: '20px' 
          } 
        }}
      >
        <Box sx={{ p: 2 }}>
          <Typography 
            variant="h5" 
            component="div" 
            sx={{ 
              fontWeight: 700, 
              mb: 4
            }}
          >
            <span style={{ color: '#1F50C2' }}>Liquidity</span>Rail
          </Typography>
          <List component="nav">
            <ListItem button onClick={() => scrollToSection('hero')}>
              <ListItemText primary="Home" />
            </ListItem>
            <ListItem button onClick={() => scrollToSection('about')}>
              <ListItemText primary="About" />
            </ListItem>
            <ListItem 
              button 
              component="a" 
              href="https://payments-doc.muda.tech" 
              target="_blank"
              rel="noopener noreferrer"
              onClick={toggleMobileMenu}
            >
              <ListItemText primary="Docs" />
            </ListItem>
            {user ? (
              <ListItem button onClick={() => { navigate('/dashboard'); toggleMobileMenu(); }}>
                <ListItemText primary="Dashboard" />
              </ListItem>
            ) : (
              <ListItem button onClick={() => { navigate('/login'); toggleMobileMenu(); }}>
                <ListItemText primary="Login" />
              </ListItem>
            )}
          </List>
        </Box>
      </Drawer>
      


      {/* Footer */}
      <Box sx={{ bgcolor: '#0C1323', color: 'white', py: 6 }}>
        <Container maxWidth="lg">
          <Grid container spacing={4}>
            <Grid item xs={12} md={4}>
              <Box sx={{ mb: 3 }}>
                <Logo sx={{ height: 100, mb: 2 }} disabledLink />
              </Box>
              <Typography variant="body2" sx={{ opacity: 0.8, mb: 2 }}>
                 We are eliminating pre-funding, simplifying currency conversion, streamlining liquidity sourcing, and facilitating currency settlement.
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, mt: 2 }}>
                <IconButton size="small" sx={{ color: 'white' }}>
                  <Facebook />
                </IconButton>
                <IconButton size="small" sx={{ color: 'white' }}>
                  <Twitter />
                </IconButton>
                <IconButton size="small" sx={{ color: 'white' }}>
                  <LinkedIn />
                </IconButton>
                <IconButton size="small" sx={{ color: 'white' }}>
                  <Instagram />
                </IconButton>
              </Box>
            </Grid>
            
            <Grid item xs={12} sm={6} md={2}>
              <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                Products
              </Typography>
              <Box component="ul" sx={{ listStyle: 'none', p: 0, m: 0 }}>
                <Box component="li" sx={{ mb: 1 }}>
                  <Link href="#" color="inherit" underline="hover" sx={{ opacity: 0.8 }}>Buy</Link>
                </Box>
                <Box component="li" sx={{ mb: 1 }}>
                  <Link href="#" color="inherit" underline="hover" sx={{ opacity: 0.8 }}>Sell</Link>
                </Box>
                <Box component="li" sx={{ mb: 1 }}>
                  <Link href="#" color="inherit" underline="hover" sx={{ opacity: 0.8 }}>Exchange</Link>
                </Box>
                <Box component="li" sx={{ mb: 1 }}>
                  <Link href="#" color="inherit" underline="hover" sx={{ opacity: 0.8 }}>API</Link>
                </Box>
              </Box>
            </Grid>
            
            <Grid item xs={12} sm={6} md={2}>
              <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                Company
              </Typography>
              <Box component="ul" sx={{ listStyle: 'none', p: 0, m: 0 }}>
                <Box component="li" sx={{ mb: 1 }}>
                  <Link href="#" color="inherit" underline="hover" sx={{ opacity: 0.8 }}>About</Link>
                </Box>
                <Box component="li" sx={{ mb: 1 }}>
                  <Link href="#" color="inherit" underline="hover" sx={{ opacity: 0.8 }}>Careers</Link>
                </Box>
                <Box component="li" sx={{ mb: 1 }}>
                  <Link href="#" color="inherit" underline="hover" sx={{ opacity: 0.8 }}>Press</Link>
                </Box>
                <Box component="li" sx={{ mb: 1 }}>
                  <Link href="#" color="inherit" underline="hover" sx={{ opacity: 0.8 }}>Blog</Link>
                </Box>
              </Box>
            </Grid>
            
            <Grid item xs={12} sm={6} md={2}>
              <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                Support
              </Typography>
              <Box component="ul" sx={{ listStyle: 'none', p: 0, m: 0 }}>
                <Box component="li" sx={{ mb: 1 }}>
                  <Link href="#" color="inherit" underline="hover" sx={{ opacity: 0.8 }}>Help Center</Link>
                </Box>
                <Box component="li" sx={{ mb: 1 }}>
                  <Link href="#" color="inherit" underline="hover" sx={{ opacity: 0.8 }}>Contact Us</Link>
                </Box>
                <Box component="li" sx={{ mb: 1 }}>
                  <Link href="#" color="inherit" underline="hover" sx={{ opacity: 0.8 }}>Status</Link>
                </Box>
                <Box component="li" sx={{ mb: 1 }}>
                  <Link href="/faq" color="inherit" underline="hover" sx={{ opacity: 0.8 }}>FAQ</Link>
                </Box>
              </Box>
            </Grid>
            
            <Grid item xs={12} sm={6} md={2}>
              <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                Legal
              </Typography>
              <Box component="ul" sx={{ listStyle: 'none', p: 0, m: 0 }}>
                <Box component="li" sx={{ mb: 1 }}>
                  <Link href="/privacy" color="inherit" underline="hover" sx={{ opacity: 0.8 }}>Privacy</Link>
                </Box>
                <Box component="li" sx={{ mb: 1 }}>
                  <Link href="/terms-of-use" color="inherit" underline="hover" sx={{ opacity: 0.8 }}>Terms</Link>
                </Box>
                <Box component="li" sx={{ mb: 1 }}>
                  <Link href="#" color="inherit" underline="hover" sx={{ opacity: 0.8 }}>Compliance</Link>
                </Box>
              </Box>
            </Grid>
          </Grid>
          
          <Box sx={{ borderTop: '1px solid rgba(255,255,255,0.1)', mt: 4, pt: 4, textAlign: 'center' }}>
            <Typography variant="body2" sx={{ opacity: 0.7 }}>
              &copy; {new Date().getFullYear()} Liquidity Rail. All rights reserved.
            </Typography>
          </Box>
        </Container>
      </Box>
      
      {/* Keep the login popup */}
      {/* <LoginPopup isOpen={openForm} onClose={closeForm} onSuccess={onLogin} /> */}
    </Box>
  );
};

export default P2PWebsiteLayout;
