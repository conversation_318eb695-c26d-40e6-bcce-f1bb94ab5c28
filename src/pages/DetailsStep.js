import React from 'react';
import { Grid, TextField } from '@mui/material';
import { Controller } from 'react-hook-form';

const DetailsStep = ({ control, errors }) => (
  <Grid container spacing={2} sx={{ mt: 2 }}>
    <Grid item xs={6}>
      <Controller
        name="send_amount"
        control={control}
        defaultValue=""
        render={({ field }) => (
          <TextField
            {...field}
            label="Send Amount"
            type="number"
            error={!!errors.send_amount}
            helperText={errors.send_amount?.message}
            fullWidth
          />
        )}
      />
    </Grid>
    <Grid item xs={6}>
      <Controller
        name="receive_amount"
        control={control}
        defaultValue=""
        render={({ field }) => (
          <TextField
            {...field}
            label="Receive Amount"
            type="number"
            error={!!errors.receive_amount}
            helperText={errors.receive_amount?.message}
            fullWidth
          />
        )}
      />
    </Grid>
    <Grid item xs={12}>
      <Controller
        name="account_number"
        control={control}
        defaultValue=""
        render={({ field }) => (
          <TextField
            {...field}
            label="Account Number"
            type="text"
            error={!!errors.account_number}
            helperText={errors.account_number?.message}
            fullWidth
          />
        )}
      />
    </Grid>
  </Grid>
);

export default DetailsStep;
