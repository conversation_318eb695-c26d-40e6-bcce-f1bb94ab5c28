import { useState, useEffect } from 'react';
import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
// material
import {
  Card,
  Table,
  Stack,
  Avatar,
  Button,
  Checkbox,
  TableRow,
  TableBody,
  TableCell,
  Container,
  Typography,
  TableContainer,
  TablePagination,
  Select, 
  MenuItem,
  Dialog, 
  DialogTitle, 
  DialogContent, 
  DialogActions,
  Box,
  Paper,
  FormControl,
  InputLabel,
  Divider,
  IconButton,
  Tooltip
} from '@mui/material';
// components
import Page from '../components/Page';
import Label from '../components/Label';
import Scrollbar from '../components/Scrollbar';
import Iconify from '../components/Iconify';
import SearchNotFound from '../components/SearchNotFound';
import { UserListHead, UserListToolbar, UserMoreMenu } from '../sections/@dashboard/user';
// mock
import { getTransactions, getPendingQuotes, cancelQuote } from '../services';

// ----------------------------------------------------------------------

const TABLE_HEAD = [
  { id: 'date', label: 'Date', alignRight: false },
  { id: 'Service', label: 'Service', alignRight: false },
  { id: 'receive', label: 'Receive', alignRight: false },
  { id: 'send', label: 'Send', alignRight: false },
  { id: 'account', label: 'Account', alignRight: false },
  { id: 'status', label: 'Status', alignRight: false },
  { id: '', label: 'Actions', alignRight: true },
];

// ----------------------------------------------------------------------

function descendingComparator(a, b, orderBy) {
  if (b[orderBy] < a[orderBy]) {
    return -1;
  }
  if (b[orderBy] > a[orderBy]) {
    return 1;
  }
  return 0;
}

function getComparator(order, orderBy) {
  return order === 'desc'
    ? (a, b) => descendingComparator(a, b, orderBy)
    : (a, b) => -descendingComparator(a, b, orderBy);
}

function applySortFilter(array, comparator, query) {
  const stabilizedThis = array.map((el, index) => [el, index]);
  stabilizedThis.sort((a, b) => {
    const order = comparator(a[0], b[0]);
    if (order !== 0) return order;
    return a[1] - b[1];
  });
  if (query) {
    return array.filter((_user) => _user.name.toLowerCase().indexOf(query.toLowerCase()) !== -1);
  }
  return stabilizedThis.map((el) => el[0]);
}

export default function Transactions() {
  const [page, setPage] = useState(0);
  const [order, setOrder] = useState('asc');
  const [selected, setSelected] = useState([]);
  const [orderBy, setOrderBy] = useState('name');
  const [filterName, setFilterName] = useState('');
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [transactions, setTransactions] = useState([]);
  const [trxnDelete, setTrxnDelete] = useState(false);
  const [trxnDeleteDetails, setTrxnDeleteDetails] = useState({});
  const [data, setData] = useState({
    transactionFilter: ['All','PENDING','SUCCESSFUL', 'Expired', 'Cancelled'],
    selectedFilter: "All"
  });
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();

  const initialContent = async () => {
    setIsLoading(true);
    try {
      let transact = "";



      if(data.selectedFilter === "PENDING"){
        // transact = await getPendingQuotes();
        transact = await getTransactions();
        transact = transact.filter((trxn) => trxn.status === 'PENDING'); 
      } else if(data.selectedFilter === "SUCCESSFUL"){
        transact = await getTransactions();
        transact = transact.filter((trxn) => trxn.status === 'SUCCESSFUL'); 
      } else if(data.selectedFilter === "Expired"){
        transact = await getTransactions();
        transact = transact.filter((trxn) => trxn.status.toLowerCase() === 'expired'); 
      } else if(data.selectedFilter === "Cancelled"){
        transact = await getTransactions();
        transact = transact.filter((trxn) => trxn.status.toLowerCase() === 'cancelled'); 
      } else {
        transact = await getTransactions();
      }
      

      if (transact && transact.length > 0) {
        setTransactions(transact);
      } else {
        setTransactions([]);
      }
    } catch (error) {
      
      console.error("Error loading transactions:", error);
      toast.error("Failed to load transactions");
      setTransactions([]);
    } finally {
      setIsLoading(false);
    }
  }

  useEffect(() => {
    initialContent();
  }, [data.selectedFilter]);

  const methods = useForm({
    defaultValues: {},
  });

  const {
    handleSubmit,
    control,
    reset,
    formState: { errors, isSubmitting },
  } = methods;

  const handleRequestSort = (event, property) => {
    const isAsc = orderBy === property && order === 'asc';
    setOrder(isAsc ? 'desc' : 'asc');
    setOrderBy(property);
  };

  const handleSelectAllClick = (event) => {
    if (event.target.checked) {
      // This should ideally select all transaction IDs, not mock user names
      const newSelecteds = transactions.map((n) => n.id);
      setSelected(newSelecteds);
      return;
    }
    setSelected([]);
  };

  // handleClick is not used in the current table, so it can be removed for clarity

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleFilterByName = (event) => {
    setFilterName(event.target.value);
  };

  const emptyRows = page > 0 ? Math.max(0, (1 + page) * rowsPerPage - transactions.length) : 0;

  const filteredUsers = transactions;
  // const isUserNotFound = filteredUsers.length === 0; // Not used

  const isUserNotFound = filteredUsers.length === 0;

  const navigatingToSection = async (transId) => {
    navigate(`/dashboard/confirmation?id=${transId}`);
  }

  function getColorForStatus(status) {
    status = status.toUpperCase();
    switch (status) {
      case 'PENDING':
        return 'primary';
      case 'CANCELLED':
        return 'error';
      case 'EXPIRED':
        return 'error';
      case 'FAILED':
        return 'error';
      case 'SUCCESSFUL':
        return 'success';
      default:
        return 'primary';
    }   
  }

  const changeFilterSelection = (e) => {
    setData({
      ...data,
      selectedFilter: e
    });
  }

  const confirmTrxnCancel = (e) => {
    setTrxnDelete(true);
    setTrxnDeleteDetails(e);
  }
                  
  const confirmToDelete = async (e) => {
    try {
      const transactionCancel = await cancelQuote({id: e?.transId});
      if (transactionCancel.status === 200 || transactionCancel.status === 200) {
        toast.success(`${transactionCancel.message}`);
        setTrxnDeleteDetails({}); 
        setTrxnDelete(false);
        await initialContent();
      } else {
        toast.error(`${transactionCancel.message}`);
      }
    } catch (error) {
      toast.error("Error cancelling transaction");
    }
  }                                   

  return (
    <Page title="Transactions">
      <Container maxWidth="xl">
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" sx={{ color: 'text.primary', fontWeight: 700 }}>
            Transactions
          </Typography>
          <Typography variant="body2" sx={{ color: 'text.secondary', mt: 1 }}>
            View and manage your transaction history
          </Typography>
        </Box>

        <Paper elevation={0} sx={{ p: 3, mb: 4, borderRadius: 2, border: '1px solid #e0e0e0' }}>
          <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
            Filter Options
          </Typography>
          <Divider sx={{ mb: 3 }} />
          
          <FormControl fullWidth variant="outlined" sx={{ maxWidth: 300 }}>
            <InputLabel id="filter-transaction-label">Transaction Status</InputLabel>
            <Controller
              name="filter_transaction_type"
              control={control}
              defaultValue={data?.selectedFilter ?? ""}
              render={({ field }) => (
                <Select 
                  {...field} 
                  labelId="filter-transaction-label"
                  label="Transaction Status"
                  value={field.value}
                  onChange={(e) => {
                    field.onChange(e.target.value); 
                    changeFilterSelection(e.target.value);
                  }}
                >
                  {data?.transactionFilter.map((status) => (
                    <MenuItem key={status} value={status}>
                      {status}
                    </MenuItem>
                  ))}
                </Select>
              )}
            />
          </FormControl>
        </Paper>

        <Card sx={{ borderRadius: 2, boxShadow: '0 0 10px rgba(0,0,0,0.05)' }}>
          <Scrollbar>
            <TableContainer sx={{ minWidth: 800 }}>
              <Table>
                <UserListHead
                  order={order}
                  orderBy={orderBy}
                  headLabel={TABLE_HEAD}
                  rowCount={transactions.length}
                  numSelected={selected.length}
                  onRequestSort={handleRequestSort}
                  onSelectAllClick={handleSelectAllClick}
                />
                <TableBody>
                  {(() => {
                    // Loading state
                    if (isLoading) {
                      return (
                        <TableRow>
                          <TableCell colSpan={7} align="center" sx={{ py: 3 }}>
                            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', py: 3 }}>
                              <Typography variant="body1" sx={{ mr: 2 }}>Loading transactions...</Typography>
                            </Box>
                          </TableCell>
                        </TableRow>
                      );
                    }
                    
                    // Empty state
                    if (filteredUsers.length === 0) {
                      return (
                        <TableRow>
                          <TableCell colSpan={7} align="center" sx={{ py: 3 }}>
                            <SearchNotFound searchQuery={filterName} />
                          </TableCell>
                        </TableRow>
                      );
                    }
                    
                    // Data state
                    return filteredUsers
                      .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                      .map((row) => {
                        const { id, status } = row;
                        const isItemSelected = selected.indexOf(id) !== -1;
                        return (
                          <TableRow
                            hover
                            key={id}
                            tabIndex={-1}
                            role="checkbox"
                            selected={isItemSelected}
                            aria-checked={isItemSelected}
                            sx={{ '&:hover': { backgroundColor: 'rgba(32, 101, 209, 0.04)' } }}
                          >
                            <TableCell align="left">{row.created_on}</TableCell>
                            <TableCell align="left">{row.service_name}</TableCell>
                            <TableCell align="left">{row.receive_currency} {row.receive_amount}</TableCell>
                            <TableCell align="left">{row.send_asset} {row.send_amount}</TableCell>
                            <TableCell align="left">{row.account_number}</TableCell>
                            <TableCell align="left">
                              <Label
                                variant="filled"
                                color={getColorForStatus(status)}
                                sx={{ px: 2, py: 0.75, borderRadius: 1, fontSize: '0.75rem' }}
                              >
                                {status.toUpperCase()}
                              </Label>
                            </TableCell>

                            <TableCell align="right">
                              <Tooltip title="View Details">
                                <IconButton
                                  size="small"
                                  color="primary"
                                  onClick={() => { navigatingToSection(row?.transId) }}
                                  sx={{ mr: 1 }}
                                >
                                  <Iconify icon="eva:eye-fill" width={20} height={20} />
                                </IconButton>
                              </Tooltip>

                              {row.status === "PENDING" && (
                                <Tooltip title="Cancel Transaction">
                                  <IconButton
                                    size="small"
                                    color="error"
                                    onClick={() => { confirmTrxnCancel(row) }}
                                  >
                                    <Iconify icon="eva:close-circle-fill" width={20} height={20} />
                                  </IconButton>
                                </Tooltip>
                              )}
                            </TableCell>
                          </TableRow>
                        );
                      });
                  })()}
                  
                  {emptyRows > 0 && (
                    <TableRow style={{ height: 53 * emptyRows }}>
                      <TableCell colSpan={7} />
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </Scrollbar>

          <TablePagination
            rowsPerPageOptions={[10, 20, 30]}
            component="div"
            count={transactions.length}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={handleChangePage}
            onRowsPerPageChange={handleChangeRowsPerPage}
          />
        </Card>

        <Dialog 
          open={trxnDelete} 
          onClose={() => {
            setTrxnDeleteDetails({}); 
            setTrxnDelete(false);
          }}
          PaperProps={{
            sx: { borderRadius: 2, p: 1 }
          }}
        >
          <DialogTitle sx={{ fontWeight: 600 }}>Confirm Transaction Cancellation</DialogTitle>
          <DialogContent>
            <Typography variant="body1" sx={{ mb: 2 }}>
              Are you sure you want to cancel this transaction?
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Transaction ID: {trxnDeleteDetails?.transId || trxnDeleteDetails?.id}
            </Typography>
          </DialogContent> 

          <DialogActions sx={{ px: 3, pb: 3 }}>
            <Button 
              onClick={() => {  
                setTrxnDeleteDetails({}); 
                setTrxnDelete(false);  
              }}
              sx={{ mr: 1 }}
            >
              Cancel
            </Button>

            <Button 
              variant="contained" 
              color="error"
              disabled={isSubmitting} 
              onClick={() => confirmToDelete(trxnDeleteDetails)}
            >
              Confirm Cancellation
            </Button>
          </DialogActions>
        </Dialog>
      </Container>
    </Page>
  );
}
