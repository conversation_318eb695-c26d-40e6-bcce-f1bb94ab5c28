import { Link as RouterLink } from 'react-router-dom';
// @mui
import { styled } from '@mui/material/styles';
import { Box, Button, Typography, Container } from '@mui/material';
// components
import Page from '../components/Page';
import Logo from '../components/Logo';

// ----------------------------------------------------------------------

const RootStyle = styled('div')(({ theme }) => ({
  display: 'flex',
  minHeight: '100vh',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  padding: theme.spacing(3),
  backgroundColor: theme.palette.background.default,
}));

const HeaderStyle = styled('header')(({ theme }) => ({
  top: 0,
  zIndex: 9,
  lineHeight: 0,
  width: '100%',
  display: 'flex',
  alignItems: 'center',
  position: 'absolute',
  padding: theme.spacing(3),
  justifyContent: 'space-between',
  [theme.breakpoints.up('md')]: {
    padding: theme.spacing(5),
  },
}));

// ----------------------------------------------------------------------

export default function Page404() {
  return (
    <Page title="404 Page Not Found">
      <HeaderStyle>
        <Logo />
      </HeaderStyle>
      
      <RootStyle>
        <Container>
          <Box sx={{ textAlign: 'center', mb: 5 }}>
            <Box
              component="img"
              src="https://minimal-assets-api-dev.vercel.app/assets/illustrations/illustration_404.svg"
              sx={{ height: 260, mx: 'auto', my: { xs: 3, sm: 5 } }}
            />

            <Typography variant="h3" paragraph>
              Sorry, page not found!
            </Typography>
            
            <Typography sx={{ color: 'text.secondary', mb: 5 }}>
              We couldn't find the page you're looking for. Perhaps you've mistyped the URL? Be
              sure to check your spelling.
            </Typography>

            <Button 
              to="/" 
              size="large" 
              variant="contained" 
              component={RouterLink}
              sx={{ 
                py: 1.5, 
                px: 3,
                backgroundColor: '#1F50C2', 
                '&:hover': { backgroundColor: '#0F2B77' },
                borderRadius: '8px',
                fontWeight: 600
              }}
            >
              Go to Home
            </Button>
          </Box>
        </Container>
      </RootStyle>
    </Page>
  );
}
