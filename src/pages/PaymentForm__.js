import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useForm, Controller, FormProvider } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as Yup from 'yup';
import {
  Card,
  CardContent,
  Grid,
  Typography,
  FormControl,
  Button,
  OutlinedInput,
  Select,
  MenuItem,
  InputLabel,
  Box,
  Chip,
  Paper,
  ToggleButton,
  ToggleButtonGroup,
  Stack,
  Divider,
  TextField,
  Avatar,
  CircularProgress
} from '@mui/material';
import { toast } from 'react-toastify';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import AccessTimeIcon from '@mui/icons-material/AccessTime';

import { post, get } from '../api';
import AmountCurrencyInput from './AmountCurrencyInput';

// Updated ProviderCard shows rate and fee details when selected
const ProviderCard = ({ provider, isSelected, onSelect, sendAsset, receiveCurrency, fee }) => {
  // Format the service name for better display
  const formatServiceName = (service) => {
    if (!service) return 'Service';
    
    // For known service codes, provide user-friendly names
    if (service.toLowerCase().includes('mobile')) {
      return 'Mobile Money';
    }
    if (service.toLowerCase().includes('bank')) {
      return 'Bank Transfer';
    }
    
    // Format by removing underscores and capitalizing each word
    return service
      .replace(/_/g, ' ')
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  };
  
  // Get the provider name or a default if not available
  const providerName = provider.name || `Provider ${provider.provider_id || provider.id || ''}`;
  
  // Get the formatted service name
  const serviceName = formatServiceName(provider.service_name || provider.service_code);

  return (
    <Card
      onClick={() => onSelect(provider)}
      className={`provider-card ${isSelected ? 'active' : ''}`}
      sx={{
        border: isSelected ? `2px solid #1F50C2` : '1px solid #ddd',
        borderRadius: '8px',
        p: 2,
        cursor: 'pointer',
        height: '100%',  // Equal height for all cards in a grid
        transition: 'all 0.2s ease',
        '&:hover': {
          borderColor: '#1F50C2',
          transform: 'translateY(-2px)',
          boxShadow: '0 4px 16px rgba(31, 80, 194, 0.12)',
        },
        background: isSelected ? 'rgba(31, 80, 194, 0.05)' : 'white',
        display: 'flex',
        flexDirection: 'column',
        position: 'relative',
      }}
    >
      {/* Provider Name and Type Tag */}
      <Box sx={{ mb: 2, display: 'flex', flexDirection: 'column', alignItems: 'flex-start' }}>
      
        
        <Chip 
          label={serviceName} 
          size="small" 
          color={isSelected ? "primary" : "default"}
          sx={{ 
            borderRadius: '4px',
            fontSize: '0.7rem',
            height: '22px',
            bgcolor: isSelected ? 'rgba(31, 80, 194, 0.15)' : 'rgba(0, 0, 0, 0.08)',
            color: isSelected ? '#1F50C2' : 'text.secondary'
          }}
        />
      </Box>
      
      {/* Min/Max Amount */}
      <Box sx={{ mb: 1 }}>
        <Typography variant="caption" color="text.secondary" display="block">
          Min: {provider.min_amount || 0} | Max: {provider.max_amount || 'Unlimited'}
        </Typography>
      </Box>
      
      {/* Selected Indicator */}
      {isSelected && (
        <Box 
          sx={{ 
            position: 'absolute', 
            top: -8, 
            right: -8, 
            bgcolor: '#1F50C2', 
            borderRadius: '50%', 
            width: 24, 
            height: 24,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'white',
            fontSize: '0.8rem',
            fontWeight: 'bold'
          }}
        >
          ✓
        </Box>
      )}
    
      <Box sx={{ mt: 'auto' }}>
        <Typography variant="body2" fontWeight={500}>
          1 {sendAsset} ≈ {provider.rate} {receiveCurrency}
        </Typography>
        <Typography variant="caption" color="text.secondary" display="block">
          Fee: {fee} {sendAsset}
        </Typography>
      </Box>
    </Card>
  );
};

// Service Button component
const ServiceButton = ({ service, isSelected, onClick }) => (
  <Button
    variant={isSelected ? "contained" : "outlined"}
    color="primary"
    onClick={() => onClick(service)}
    sx={{
      borderRadius: '8px',
      py: 1,
      px: 2,
      mr: 1,
      mb: 1,
      transition: 'all 0.2s ease',
      display: 'flex',
      alignItems: 'center',
      gap: 1,
      '&:hover': {
        transform: 'translateY(-2px)',
        boxShadow: '0 4px 16px rgba(31, 80, 194, 0.12)',
      }
    }}
  >
    {service.logo && (
      <img 
        src={service.logo} 
        alt={service.name || service.service_id} 
        style={{ height: '20px', width: '20px', objectFit: 'contain' }} 
      />
    )}
    <Typography variant="body2" fontWeight={500}>
      {service.name || service.service_id}
    </Typography>
  </Button>
);

const PaymentForm = ({ onSubmitSuccess }) => {
  const navigate = useNavigate();
  const [assets, setAssets] = useState([]);
  const [coinAssets, setCoinAssets] = useState([]);
  const [fiatAssets, setFiatAssets] = useState([]);
  const [providers, setProviders] = useState([]);
  const [selectedProvider, setSelectedProvider] = useState(null);
  const [selectedService, setSelectedService] = useState(null);
  const [rate, setRate] = useState(3495);
  const [fee, setFee] = useState(0);
  const [processingFee, setProcessingFee] = useState(0);
  const [convertedAmount, setConvertedAmount] = useState("34950");
  const [fiatCurrencies, setFiatCurrencies] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isProvidersLoading, setIsProvidersLoading] = useState(false);
  const [availableServices, setAvailableServices] = useState([]);
  const [servicesByType, setServicesByType] = useState({});
  
  const step1Schema = Yup.object().shape({
    service_id: Yup.string().required('Service is required'),
    send_asset: Yup.string().required('Pay currency is required'),
    receive_currency: Yup.string().required('Receiving currency is required'),
    send_amount: Yup.number().positive().required('Sending amount is required'),
    receive_amount: Yup.number().positive().required('Receivable amount is required')
  });

  const methods = useForm({
    resolver: yupResolver(step1Schema),
    defaultValues: {
      service_id: "",
      send_asset: "USDT",
      receive_currency: "UGX",
      send_amount: "10",
      receive_amount: "34950"
    }
  });

  const {
    handleSubmit,
    control,
    watch,
    setValue,
    formState: { errors, isSubmitting }
  } = methods;

  const watchedSendAmount = watch("send_amount", "10");
  const watchedSendAsset = watch("send_asset", "USDT");
  const watchedReceiveCurrency = watch("receive_currency", "UGX");
  const watchedServiceId = watch("service_id", "");

  // Extract unique services from providers
  const extractServicesFromProviders = (providersData) => {
    if (!Array.isArray(providersData) || providersData.length === 0) return [];
    
    const services = [];
    const serviceIds = new Set();
    
    providersData.forEach(provider => {
      if (provider.service_id && !serviceIds.has(provider.service_id)) {
        serviceIds.add(provider.service_id);
        services.push({
          service_id: provider.service_id,
          currency: provider.currency || provider.receive_currency || watchedReceiveCurrency,
          name: provider.service_name || provider.service_id,
          logo: provider.service_logo || null,
          type: provider.service_code,
          service_code: provider.service_code,
        });
      }
    });
    
    return services;
  };

  // Group services by type (mobile_money, airtime, etc.)
  const groupServicesByType = (services) => {
    const grouped = {};
    
    services.forEach(service => {
      console.log(`service: ${JSON.stringify(service)}`)
      const type = service.service_code
      if (!grouped[type]) {
        grouped[type] = [];
      }
      grouped[type].push(service);
    });
    
    return grouped;
  };

  // Load assets
  useEffect(() => {
    const loadInitialData = async () => {
      setIsLoading(true);
      try {
        // Load assets
        const assetsData = await get("accounts/getAssets");
        setAssets(assetsData.data);
        
        // Separate assets by type (coin or fiat)
        const coins = assetsData.data.filter(asset => asset.type === 'coin');
        const fiats = assetsData.data.filter(asset => asset.type === 'fiat');
        
        setCoinAssets(coins);
        setFiatAssets(fiats);
        
        // Set default values - selecting first coin asset for "You Pay" if available
        if (coins.length > 0) {
          setValue("send_asset", coins[0].asset_code);
        } else {
          setValue("send_asset", "USDT"); // Fallback
        }
        
        setValue("receive_currency", "UGX");
        setValue("send_amount", "10");
        setValue("receive_amount", "34950");
      } catch (error) {
        console.error("Error loading initial data:", error);
        toast.error("Failed to load data. Please try again.");
      } finally {
        setIsLoading(false);
      }
    };
    
    loadInitialData();
  }, [setValue]);

  // Fetch providers whenever currencies change
  const fetchProviders = async (sendAsset, receiveCurrency) => {
    if (!sendAsset || !receiveCurrency) return;
    
    try {
      setIsProvidersLoading(true);
      // Reset selected provider when currencies change
      setSelectedProvider(null);
      
      // Prepare data for the API call
      const params = {
        asset: sendAsset,
        currency: receiveCurrency,
      };
      
      // If service is selected, include it
      if (watchedServiceId) {
        params.service_id = watchedServiceId;
      }
      
      console.log("Fetching providers with params:", params);
      const providersData = await post("accounts/provider", params);
      
      if (Array.isArray(providersData?.data) && providersData?.data.length > 0) {
        console.log("Raw providers data:", providersData);
        setProviders(providersData.data);
        
        // Extract services from providers
        const services = extractServicesFromProviders(providersData.data);
        setAvailableServices(services);
        
        // Group services by type
        const groupedByType = groupServicesByType(services);
        setServicesByType(groupedByType);
        
        // Build fiat currency options from providers
        const fiatOptions = Array.from(new Set(providersData.data.map(p => p.currency || p.receive_currency)))
          .map(currency => ({
            label: currency,
            value: currency,
            logo: null, // We don't have logo from providers
            countryCode: 'UG', // Default
            type: 'mobile_money' // Default
          }));
        
        setFiatCurrencies(fiatOptions);
        
        // Auto-select the first provider
        const firstProvider = providersData.data[0];
        console.log("Auto-selecting first provider:", firstProvider);
        setSelectedProvider(firstProvider);
        setRate(firstProvider.rate || 3495);
        
        // Find matching service for the first provider
        if (firstProvider.service_id) {
          const matchingService = services.find(s => s.service_id === firstProvider.service_id);
          if (matchingService) {
            console.log("Found matching service:", matchingService);
            setSelectedService(matchingService);
            setValue("service_id", matchingService.service_id);
          } else {
            console.log("No matching service found for service_id:", firstProvider.service_id);
            
            // Create a default service based on the provider's data
            const defaultService = {
              service_id: firstProvider.service_id,
              name: firstProvider.service_name || 'Default Service',
              currency: firstProvider.currency,
              service_code: firstProvider.service_code
            };
            console.log("Created default service:", defaultService);
            setSelectedService(defaultService);
            setValue("service_id", defaultService.service_id);
          }
        } else {
          console.log("Provider has no service_id, using service_code:", firstProvider.service_code);
          
          // Create a default service based on the provider's data
          const defaultService = {
            service_id: firstProvider.service_id || 1000, // Default ID if none exists
            name: firstProvider.service_name || 'Default Service',
            currency: firstProvider.currency,
            service_code: firstProvider.service_code
          };
          console.log("Created default service:", defaultService);
          setSelectedService(defaultService);
          setValue("service_id", defaultService.service_id);
        }
        
        console.log("Providers loaded:", providersData.data.length);
      } else {
        setProviders([]);
        setAvailableServices([]);
        console.log("No providers available for the selected currencies");
      }
    } catch (error) {
      console.error("Error fetching providers:", error);
      toast.error("Failed to fetch providers. Please try again.");
      setProviders([]);
      setAvailableServices([]);
    } finally {
      setIsProvidersLoading(false);
    }
  };

  // Watch for currency changes and fetch providers
  useEffect(() => {
    fetchProviders(watchedSendAsset, watchedReceiveCurrency);
  }, [watchedSendAsset, watchedReceiveCurrency]);

  // Handle service selection
  const handleServiceSelect = (service) => {
    setSelectedService(service);
    setValue("service_id", service.service_id);
    setValue("receive_currency", service.currency);
    
    // Find providers that support this service
    fetchProviders(watchedSendAsset, service.currency);
  };

  // Calculate the converted receive amount
  useEffect(() => {
    if (watchedSendAmount && rate) {
      const calculatedReceive = (watchedSendAmount * rate).toFixed(0);
      setValue("receive_amount", calculatedReceive);
      setConvertedAmount(calculatedReceive);
    }
  }, [watchedSendAmount, rate, setValue]);

  // Handle provider selection
  const handleProviderSelect = (provider) => {
    setSelectedProvider(provider);
    setRate(provider.rate || 0); // Fallback to default rate if not available
    
    // If the provider has a service_id, select the corresponding service
    if (provider.service_id) {
      const matchingService = availableServices.find(s => s.service_id === provider.service_id);
      if (matchingService) {
        setSelectedService(matchingService);
        setValue("service_id", matchingService.service_id);
        setValue("receive_currency", matchingService.currency || provider.currency || provider.receive_currency || watchedReceiveCurrency);
      }
    }
    
    // Log the selected provider details
    console.log("Selected provider:", provider);
  };
  
  // Add this function to help with debugging
  const logFormData = (data, stage) => {
    // Create a structured object with all the current form state
    const formDebugData = {
      stage,
      timestamp: new Date().toISOString(),
      formData: data,
      selected: {
        provider: selectedProvider ? {
          id: selectedProvider.id,
          provider_id: selectedProvider.provider_id,
          provider_service_id: selectedProvider.provider_service_id,
          name: selectedProvider.name,
          service_code: selectedProvider.service_code,
          service_id: selectedProvider.service_id,
          service_name: selectedProvider.service_name
        } : null,
        service: selectedService ? {
          service_id: selectedService.service_id,
          name: selectedService.name,
          currency: selectedService.currency,
          service_code: selectedService.service_code
        } : null
      },
      watchedValues: {
        sendAmount: watchedSendAmount,
        sendAsset: watchedSendAsset,
        receiveCurrency: watchedReceiveCurrency,
        serviceId: watchedServiceId
      },
      state: {
        rate,
        fee,
        convertedAmount,
        providers: providers.length,
        availableServices: availableServices.length,
        assetOptions: paymentCurrencyOptions.length,
        receiveCurrencyOptions: receiveCurrencyOptions.length,
      }
    };
    
    console.log(`FORM DATA [${stage}]:`, formDebugData);
    return formDebugData;
  };
  
  const onSubmit = async (data) => {


    // Log complete form data at submission start
    logFormData(data, 'SUBMIT_START');
   
    if (!selectedProvider) {
      console.error("No provider selected.", { providers: providers.length > 0 ? providers : "No providers available" });
      toast.error("Please select a provider. If providers are shown but cannot be selected, try clicking on one again.", { position: 'top-right' });
      return;
    }
    
    if (!selectedService) {
      console.error("No service selected.", { provider: selectedProvider });
      toast.error("Please select a service. If no services are available, please contact support.", { position: 'top-right' });
      return;
    }
    
    // Check if user is logged in
    const userObject = localStorage.getItem('userObject');
    if (!userObject) {
      
      // User is not logged in, show login popup via event
      // const loginEvent = new CustomEvent('show-login-popup', { 
      //   detail: { returnTo: '/dashboard/payments' } 
      // });
      // window.dispatchEvent(loginEvent);
      return navigate(`/login?next=dashboard/payments`);
    }
    
    try {
      // Create a deep copy of the data to ensure we don't lose any values
      const formattedData = JSON.parse(JSON.stringify(data));
      
      // Find the asset details from our assets array
      const assetDetails = assets.find(asset => asset.asset_code === formattedData.send_asset);
      
      // Make sure all required fields have values
      formattedData.send_asset_id = assetDetails?.asset_id || '';
      formattedData.send_asset_chain = assetDetails?.chain || '';
      
      // Use provider_service_id from the API response
      formattedData.provider_service_id = selectedProvider?.provider_service_id || '';
      formattedData.provider_id = selectedProvider?.provider_id || '';
      formattedData.provider_name = selectedProvider?.name || '';
      formattedData.ex_rate = rate || 0;
      formattedData.provider_type = selectedProvider?.provider_type || '';
      formattedData.service_code = selectedProvider?.service_code || selectedService?.service_code || '';
      
      // Log the complete data being passed to parent
      console.log("PaymentForm submitting complete data:", formattedData);
      console.log("Selected provider being passed to parent:", selectedProvider);
      logFormData(formattedData, 'FORMATTED_FOR_SUBMISSION');
      
      // If onSubmitSuccess prop is provided, use it for integration with Payments.js
      if (onSubmitSuccess && typeof onSubmitSuccess === 'function') {
        // Pass the formatted data AND the selectedProvider to ensure it's available in Payments.js
        onSubmitSuccess(formattedData);
        return;
      }
      
      console.error("onSubmitSuccess is not a function or is undefined");
      // Default navigation behavior
      // navigate(`/dashboard/payments`);

    } catch (error) {
      console.error("Error in PaymentForm submission:", error);
      toast.error("Error submitting form. Please try again.");
    }
  };

  // Helper function to determine button border radius based on position
  const getButtonBorderRadius = (index, length) => {
    if (length === 1) {
      return '8px'; // If only one button, round all corners
    }
    
    if (index === 0) {
      return '8px 0 0 8px'; // First button
    }
    
    if (index === length - 1) {
      return '0 8px 8px 0'; // Last button
    }
    
    return '0'; // Middle buttons
  };

  // Create dropdown options - use coin assets for "You Pay" and fiat for "You Receive"
  const paymentCurrencyOptions = coinAssets.map(asset => ({
    value: asset.asset_code,
    label: `${asset.asset_name} - ${asset.chain}`,
    logo: asset.logo || `/static/images/currencies/${asset.asset_code.toLowerCase()}.png`
  }));

  const receiveCurrencyOptions = fiatAssets.map(asset => ({
    value: asset.asset_code,
    label: asset.asset_code,
    logo: asset.logo || `/static/images/currencies/${asset.asset_code.toLowerCase()}.png`
  }));

  // Handle asset selection from the asset logo grid
  const handleAssetSelect = (asset) => {
    if (asset.type === 'coin') {
      const previousAsset = watchedSendAsset;
      setValue("send_asset", asset.asset_code);
      
      // Only fetch providers if the asset has actually changed
      if (previousAsset !== asset.asset_code) {
        fetchProviders(asset.asset_code, watchedReceiveCurrency);
      }
    } else if (asset.type === 'fiat') {
      const previousCurrency = watchedReceiveCurrency;
      setValue("receive_currency", asset.asset_code);
      
      // Only fetch providers if the currency has actually changed
      if (previousCurrency !== asset.asset_code) {
        fetchProviders(watchedSendAsset, asset.asset_code);
      }
    }
  };
 

  return (
    <FormProvider {...methods}>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Box sx={{ bgcolor: 'white', p: 3, borderRadius: '12px', boxShadow: '0 4px 12px rgba(0,0,0,0.05)' }}>
          {/* Form Title */}
          <Typography variant="h5" fontWeight={700} sx={{ mb: 2, color: '#1F50C2', textAlign: 'center' }}>
            Quick Exchange
          </Typography>
          
          {/* Compact Layout with Grid */}
          <Grid container spacing={2}>
            {/* Currency Exchange Inputs - First Row */}
            <Grid item xs={12} md={12}>
              <AmountCurrencyInput 
                label="You Pay"
                amountName="send_amount"
                currencyName="send_asset"
                amountError={errors.send_amount}
                currencyError={errors.send_asset}
                currencyOptions={paymentCurrencyOptions}
                onAmountChange={(value) => {
                  setValue("send_amount", value);
                  if (value && rate) {
                    const converted = (parseFloat(value) * rate).toFixed(0);
                    setValue("receive_amount", converted);
                    setConvertedAmount(converted);
                  }
                }}
              />
            </Grid>

            {/* You Receive - First Row */}
            <Grid item xs={12} md={12}>
              <AmountCurrencyInput 
                label="You Receive"
                amountName="receive_amount"
                currencyName="receive_currency"
                amountError={errors.receive_amount}
                currencyError={errors.receive_currency}
                currencyOptions={receiveCurrencyOptions}
                onAmountChange={(value) => {
                  setValue("receive_amount", value);
                  if (value && rate) {
                    const sendValue = (parseFloat(value) / rate).toFixed(2);
                    setValue("send_amount", sendValue);
                  }
                }}
              />
            </Grid>
            
            {/* Available Providers */}
            <Grid item xs={12}>
              <Typography variant="body2" fontWeight={600} color="text.secondary" sx={{ mb: 0.5 }}>
                Select Provider
              </Typography>
              {(() => {
                if (isProvidersLoading) {
                  return (
                    <Box sx={{ 
                      display: 'flex', 
                      justifyContent: 'center', 
                      alignItems: 'center', 
                      height: '80px' 
                    }}>
                      <CircularProgress size={24} />
                    </Box>
                  );
                }
                
                if (providers.length === 0) {
                  return (
                    <Typography variant="body2" color="text.secondary" sx={{ py: 1, textAlign: 'center' }}>
                      No providers available for {watchedSendAsset} to {watchedReceiveCurrency}
                    </Typography>
                  );
                }
                
                // Display all providers in a single grid without grouping
                return (
                  <Grid container spacing={1}>
                    {providers.map((provider, index) => (
                      <Grid item xs={12} sm={6} key={provider.provider_service_id || index}>
                        <ProviderCard
                          provider={provider}
                          isSelected={selectedProvider && selectedProvider.provider_service_id === provider.provider_service_id}
                          onSelect={handleProviderSelect}
                          sendAsset={watchedSendAsset}
                          receiveCurrency={watchedReceiveCurrency}
                          fee={fee}
                        />
                      </Grid>
                    ))}
                  </Grid>
                );
              })()}
            </Grid>
            
            {/* Submit Button - Last Row */}
            <Grid item xs={12}>
              <Button
                type="submit"
                fullWidth
                variant="contained"
                size="medium"
                disabled={isSubmitting || !selectedProvider || !selectedService}
                sx={{ 
                  py: 1.5,
                  fontWeight: 600,
                  backgroundColor: '#1F50C2',
                  '&:hover': {
                    backgroundColor: '#0F2B77',
                  },
                  borderRadius: '6px',
                  mt: 1
                }}
              >
                Next
              </Button>
            </Grid>
          </Grid>
        </Box>
      </form>
    </FormProvider>
  );
};

export default PaymentForm;
