import React from 'react';
import { Grid, Typography } from '@mui/material';
import <PERSON><PERSON>ield from './SelectionField';

const SelectionStep = ({ control, services, assets, providers, rate, watchedPayCurrency, watchedCurrency, errors }) => (
  <Grid container spacing={2} sx={{ mt: 2 }}>
    <SelectionField
      name="service_id"
      label="Sending Service"
      control={control}
      options={services.map(service => ({ label: service.service_name, value: service.service_id }))}
      error={errors.service_id}
    />
    <Grid item xs={6}>
      <SelectionField
        name="send_asset"
        label="Select Pay Currency"
        control={control}
        options={assets.map(asset => ({ label: asset.asset_name, value: asset.asset_code }))}
        error={errors.send_asset}
      />
    </Grid>
    <Grid item xs={6}>
      <SelectionField
        name="receive_currency"
        label="Receiving Currency"
        control={control}
        options={services
          .filter(service => service.service_id === watchedPayCurrency)
          .map(service => ({ label: service.currency, value: service.currency }))}
        error={errors.receive_currency}
      />
    </Grid>
    <SelectionField
      name="provider_id"
      label="Available Providers"
      control={control}
      options={providers.map(provider => ({ label: provider.name, value: provider.provider_id }))}
      error={errors.provider_id}
    />
    {rate && (
      <Grid item xs={12}>
        <Typography variant="body2">
          Rate: 1 {watchedPayCurrency} = {rate} {watchedCurrency}
        </Typography>
      </Grid>
    )}
  </Grid>
);

export default SelectionStep;
