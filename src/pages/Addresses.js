import { useState, useEffect } from 'react';
import { 
  <PERSON><PERSON>, 
  Con<PERSON>er, 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  Dialog, 
  DialogTitle, 
  DialogContent, 
  DialogActions, 
  TextField, 
  Select, 
  MenuItem, 
  IconButton,
  Card,
  Box,
  Paper,
  Divider,
  Tooltip,
  CircularProgress,
  Alert
} from '@mui/material';
import { DataGrid, GridActionsCellItem } from '@mui/x-data-grid';
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import Iconify from '../components/Iconify';
import { post, get } from '../api';
import NewAddessPopup from '../components/settingsForms/NewAddressPopup';
import Page from '../components/Page';

const chainOptions = ['STELLAR', 'CELO', 'TRON'];

export default function AddressManagement() {
  const [addresses, setAddresses] = useState([]);
  const [open, setOpen] = useState(false);
  const [walletDelete, setWalletDelete] = useState(false);
  const [walletDeleteDetails, setWalletDeleteDetails] = useState({});
  const [isOpen, setIsOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    fetchAddresses();
  }, []);

  const fetchAddresses = async () => {
    try {
      setIsLoading(true);
      const response = await get("accounts/getAddresses");
      setAddresses(response.data || []);
    } catch (error) {
      // toast.error("Failed to fetch addresses");
    } finally {
      setIsLoading(false);
    }
  };

  const confirmToDelete = async (data) => {
    try {
      setIsSubmitting(true);
      const deletedAddress = await get(`accounts/deleteAddress/${data?.address_id}`);
      if (deletedAddress.status === 200 || deletedAddress.status === 200) {
        toast.success(`${deletedAddress.message}`);
        setWalletDeleteDetails({}); 
        setWalletDelete(false);
        fetchAddresses();
      } else {
        toast.error(`${deletedAddress.message}`);
      }
    } catch (error) {
      toast.error("Error deleting address");
    } finally {
      setIsSubmitting(false);
    }
  };

  const columns = [
    { 
      field: "wallet_name", 
      headerName: "Name", 
      flex: 0.5,
      minWidth: 120,
      renderCell: (params) => (
        <Typography variant="body2" fontWeight={500}>
          {params.value}
        </Typography>
      )
    },
    { 
      field: "chain", 
      headerName: "Chain", 
      flex: 0.5,
      minWidth: 120,
      renderCell: (params) => (
        <Box sx={{ 
          backgroundColor: 'rgba(32, 101, 209, 0.08)',
          py: 0.5,
          px: 1.5,
          borderRadius: 1,
          display: 'inline-flex'
        }}>
          <Typography variant="body2" fontWeight={500}>
            {params.value}
          </Typography>
        </Box>
      )
    },
    { 
      field: "address", 
      headerName: "Address", 
      flex: 1.5,
      minWidth: 250,
      renderCell: (params) => (
        <Typography variant="body2" sx={{ fontFamily: 'monospace', wordBreak: 'break-all' }}>
          {params.value}
        </Typography>
      )
    },
    {
      field: "actions",
      headerName: "Actions",
      width: 120,
      type: 'actions',
      getActions: (params) => [
        <GridActionsCellItem
          icon={
            <Tooltip title="Delete Address">
              <IconButton color="error" size="small">
                <Iconify icon="eva:trash-2-outline" width={20} height={20} />
              </IconButton>
            </Tooltip>
          }
          onClick={() => {
            setWalletDelete(true);
            setWalletDeleteDetails(params.row);
          }}
          label="Delete"
        />
      ]
    },
  ];

  const onClose = () => {
    setIsOpen(false);
  };

  const onSuccess = async() => {
    setIsOpen(false);
    toast.success("Address added successfully");
    await fetchAddresses();
  };

  const onOpen = () => {
    setIsOpen(true);
  };

  return (
    <Page title="Address Management">
      <Container>
        <NewAddessPopup isOpen={isOpen} onClose={() => onClose()} onSuccess={() => onSuccess()} />

        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" sx={{ fontWeight: 700, color: 'text.primary' }}>
            Addresses
          </Typography>
          <Typography variant="body2" sx={{ mt: 1, color: 'text.secondary' }}>
            Manage your cryptocurrency wallet addresses
          </Typography>
        </Box>

        <Card sx={{ 
          p: 3, 
          mb: 3, 
          borderRadius: 2, 
          boxShadow: '0 0 10px rgba(0,0,0,0.05)' 
        }}>
          <Stack direction="row" alignItems="center" justifyContent="space-between" mb={3}>
            <Typography variant="h6" sx={{ fontWeight: 600 }}>
              Your Wallet Addresses
            </Typography>
            <Button 
              variant="contained" 
              onClick={() => onOpen()} 
              startIcon={<Iconify icon="eva:plus-fill" />}
              sx={{
                bgcolor: '#1F50C2',
                '&:hover': {
                  bgcolor: '#0F2B77',
                },
                boxShadow: '0 4px 12px rgba(31, 80, 194, 0.15)',
                borderRadius: '8px'
              }}
            >
              Add New Address
            </Button>
          </Stack>

          <Divider sx={{ mb: 3 }} />

          {addresses.length === 0 && !isLoading ? (
            <Alert severity="info" sx={{ mb: 3 }}>
              You don't have any addresses yet. Click "Add New Address" to create one.
            </Alert>
          ) : null}

          <Box sx={{ height: 400, width: '100%' }}>
            <DataGrid 
              rows={addresses} 
              columns={columns} 
              pageSize={5}
              rowsPerPageOptions={[5, 10, 25]}
              getRowId={(row) => row.address_id}
              loading={isLoading}
              disableSelectionOnClick
              disableColumnMenu
              sx={{
                border: 'none',
                '& .MuiDataGrid-cell': { 
                  py: 2,
                  borderBottom: '1px solid #f0f0f0'
                },
                '& .MuiDataGrid-columnHeaders': {
                  bgcolor: 'rgba(145, 158, 171, 0.08)',
                  borderRadius: 1
                },
                '& .MuiDataGrid-columnHeaderTitle': {
                  fontWeight: 600
                }
              }}
            />
          </Box>
        </Card>

        <Dialog 
          open={walletDelete} 
          onClose={() => setWalletDelete(false)}
          PaperProps={{
            sx: { borderRadius: 2, p: 1 }
          }}
        >
          <DialogTitle sx={{ fontWeight: 600 }}>Confirm Deletion</DialogTitle>
          <DialogContent>
            <Box sx={{ mb: 3 }}>
              <Typography variant="body1" sx={{ mb: 2 }}>
                Are you sure you want to delete this address?
              </Typography>
              <Paper sx={{ 
                p: 2, 
                bgcolor: 'rgba(255, 72, 66, 0.08)',
                borderRadius: 1
              }}>
                <Typography variant="subtitle2" sx={{ mb: 1 }}>
                  Chain: <Box component="span" fontWeight={600}>{walletDeleteDetails?.chain}</Box>
                </Typography>
                <Typography variant="subtitle2" sx={{ wordBreak: 'break-all', fontFamily: 'monospace' }}>
                  {walletDeleteDetails?.address}
                </Typography>
              </Paper>
            </Box>
            <Typography variant="body2" color="error" fontWeight={500}>
              Warning: This action cannot be undone.
            </Typography>
          </DialogContent> 
          <DialogActions sx={{ px: 3, pb: 3 }}>
            <Button 
              onClick={() => {  
                setWalletDeleteDetails({}); 
                setWalletDelete(false);
              }}
              sx={{ mr: 1 }}
            >
              Cancel
            </Button>
            <Button 
              variant="contained" 
              color="error"
              disabled={isSubmitting} 
              onClick={() => confirmToDelete(walletDeleteDetails)}
              startIcon={isSubmitting ? <CircularProgress size={20} /> : <Iconify icon="eva:trash-2-outline" />}
            >
              {isSubmitting ? 'Deleting...' : 'Delete Address'}
            </Button>
          </DialogActions>
        </Dialog>
      </Container>
    </Page>
  );
}