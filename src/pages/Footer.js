import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Typography,
  Box,
  Container,
  Grid,
  Link,
  IconButton,
  useMediaQuery
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { Facebook, Twitter, LinkedIn, Instagram, Menu as MenuIcon, Security, Speed, Payments, Public, Send } from '@mui/icons-material';
import Logo from '../components/Logo';
import "../assets/css/style.css";

const Tab = ({ label, isActive, onClick }) => (
  <button
    onClick={onClick}
    className={`flex items-center gap-2 px-6 py-3 rounded-full transition-all duration-300 default-casestudy-btn ${
      isActive 
        ? 'bg-blue-500 text-white shadow-lg default-casestudy-btn-active' 
        : 'bg-white text-gray-600 hover:bg-gray-50'
    }`}
  >
    <span className="font-medium">{label}</span>
  </button>
);
 
const Footer = () => {

  const [user, setUser] = useState(null);
  const [userAuth, setUserAuth] = useState(null);
  const [userCheck, setUserCheck] = useState(true);
  const [loginDialogOpen, setLoginDialogOpen] = useState(false);
  const [openForm, setopenForm] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('wallets');
  
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const navigate = useNavigate();



  // Check localStorage for JWT on mount
  useEffect(() => {

    const userAuthToken = localStorage.getItem('authJWT');
    setUserAuth(userAuthToken);


    const userObject = localStorage.getItem('userObject');
    if (userObject) {
      try {
        
        const decoded = JSON.parse(userObject);
        setUser(decoded);

      } catch (error) {
        console.error('Invalid user object in local storage');
        localStorage.removeItem('userObject');
      }
    }
  }, []); // userCheck

  const handleLogout = () => {
    localStorage.removeItem('userObject');
    setUser(null);
  };

  const toggleUserCheck = () => {
    setUserCheck(!userCheck);
  };

  // on successful login
  const onLogin = (e) => {
    setUser(e);
    setopenForm(false);
    const returnPath    = sessionStorage.getItem('afterLoginRedirect');
    const userAuthToken = localStorage.getItem('authJWT');
    setUserAuth(userAuthToken);
    if (returnPath) {
      sessionStorage.removeItem('afterLoginRedirect');
      return navigate(returnPath);
    }

  }

  const closeForm = () => {
    setopenForm(false);
  }
  
  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  const scrollToSection = (sectionId) => {
    const section = document.getElementById(sectionId);
    if (section) {
      section.scrollIntoView({ behavior: 'smooth' });
    }
    setMobileMenuOpen(false);
  };
  
  const navigateToLogin = () => {
    navigate('/login');
    setMobileMenuOpen(false);
  };

  const APIDocumentationRedirect = () => {
    return window.open('https://payments-doc.muda.tech', '_blank'); 
  };

  const tabs = [
    { id: 'wallets', label: 'Wallets', icon: "" },
    { id: 'fintechs-and-aggregators', label: 'Fintechs and Aggregators', icon: "" },
    { id: 'remittance', label: 'Remittance', icon: "" },
  ];

  const onLoginOpen = () => {
    setopenForm(true);
  }
  
  return (
      <Box sx={{ bgcolor: '#0C1323', color: 'white', py: 6 }}>
        <Container maxWidth="lg">
          <Grid container spacing={4}>
            <Grid item xs={12} md={4}>
              <Box sx={{ mb: 3 }}>
                <Logo sx={{ height: 100, mb: 2 }} disabledLink />
              </Box>
              <Typography variant="body2" sx={{ opacity: 0.8, mb: 2 }}>
                 We are eliminating pre-funding, simplifying currency conversion, streamlining liquidity sourcing, and facilitating currency settlement.
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, mt: 2 }}>
                <IconButton size="small" sx={{ color: 'white' }}>
                  <Facebook />
                </IconButton>
                <IconButton size="small" sx={{ color: 'white' }}>
                  <Twitter />
                </IconButton>
                <IconButton size="small" sx={{ color: 'white' }}>
                  <LinkedIn />
                </IconButton>
                <IconButton size="small" sx={{ color: 'white' }}>
                  <Instagram />
                </IconButton>
              </Box>
            </Grid>
            
            <Grid item xs={12} sm={6} md={2}>
              <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                Products
              </Typography>
              <Box component="ul" sx={{ listStyle: 'none', p: 0, m: 0 }}>
                <Box component="li" sx={{ mb: 1 }}>
                  <Link href="#" color="inherit" underline="hover" sx={{ opacity: 0.8 }}>Buy</Link>
                </Box>
                <Box component="li" sx={{ mb: 1 }}>
                  <Link href="#" color="inherit" underline="hover" sx={{ opacity: 0.8 }}>Sell</Link>
                </Box>
                <Box component="li" sx={{ mb: 1 }}>
                  <Link href="#" color="inherit" underline="hover" sx={{ opacity: 0.8 }}>Exchange</Link>
                </Box>
                <Box component="li" sx={{ mb: 1 }}>
                  <Link href="#" color="inherit" underline="hover" sx={{ opacity: 0.8 }}>API</Link>
                </Box>
              </Box>
            </Grid>
            
            <Grid item xs={12} sm={6} md={2}>
              <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                Company
              </Typography>
              <Box component="ul" sx={{ listStyle: 'none', p: 0, m: 0 }}>
                <Box component="li" sx={{ mb: 1 }}>
                  <Link href="#" color="inherit" underline="hover" sx={{ opacity: 0.8 }}>About</Link>
                </Box>
                <Box component="li" sx={{ mb: 1 }}>
                  <Link href="#" color="inherit" underline="hover" sx={{ opacity: 0.8 }}>Careers</Link>
                </Box>
                <Box component="li" sx={{ mb: 1 }}>
                  <Link href="#" color="inherit" underline="hover" sx={{ opacity: 0.8 }}>Press</Link>
                </Box>
                <Box component="li" sx={{ mb: 1 }}>
                  <Link href="#" color="inherit" underline="hover" sx={{ opacity: 0.8 }}>Blog</Link>
                </Box>
              </Box>
            </Grid>
            
            <Grid item xs={12} sm={6} md={2}>
              <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                Support
              </Typography>
              <Box component="ul" sx={{ listStyle: 'none', p: 0, m: 0 }}>
                <Box component="li" sx={{ mb: 1 }}>
                  <Link href="#" color="inherit" underline="hover" sx={{ opacity: 0.8 }}>Help Center</Link>
                </Box>
                <Box component="li" sx={{ mb: 1 }}>
                  <Link href="#" color="inherit" underline="hover" sx={{ opacity: 0.8 }}>Contact Us</Link>
                </Box>
                <Box component="li" sx={{ mb: 1 }}>
                  <Link href="#" color="inherit" underline="hover" sx={{ opacity: 0.8 }}>Status</Link>
                </Box>
                <Box component="li" sx={{ mb: 1 }}>
                  <Link href="/faq" color="inherit" underline="hover" sx={{ opacity: 0.8 }}>FAQ</Link>
                </Box>
              </Box>
            </Grid>
            
            <Grid item xs={12} sm={6} md={2}>
              <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                Legal
              </Typography>
              <Box component="ul" sx={{ listStyle: 'none', p: 0, m: 0 }}>
                <Box component="li" sx={{ mb: 1 }}>
                  <Link href="/privacy" color="inherit" underline="hover" sx={{ opacity: 0.8 }}>Privacy</Link>
                </Box>
                <Box component="li" sx={{ mb: 1 }}>
                  <Link href="/terms-of-use" color="inherit" underline="hover" sx={{ opacity: 0.8 }}>Terms</Link>
                </Box>
                <Box component="li" sx={{ mb: 1 }}>
                  <Link href="#" color="inherit" underline="hover" sx={{ opacity: 0.8 }}>Compliance</Link>
                </Box>
              </Box>
            </Grid>
          </Grid>
          
          <Box sx={{ borderTop: '1px solid rgba(255,255,255,0.1)', mt: 4, pt: 4, textAlign: 'center' }}>
            <Typography variant="body2" sx={{ opacity: 0.7 }}>
              &copy; {new Date().getFullYear()} Liquidity Rail. All rights reserved.
            </Typography>
          </Box>
        </Container>
      </Box>
  );
};

export default Footer;
