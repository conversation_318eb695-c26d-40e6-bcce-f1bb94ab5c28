import React, { useState } from 'react';
import { Link as RouterLink, useNavigate } from 'react-router-dom';
// @mui
import {
  styled,
  Container,
  Typography,
  Box,
  TextField,
  Button,
  Link,
  CircularProgress,
  Alert,
  Paper
} from '@mui/material';
import { toast } from 'react-toastify';
// hooks
import useResponsive from '../hooks/useResponsive';
// components
import Page from '../components/Page';
import Logo from '../components/Logo';
import { post } from '../api';

// ----------------------------------------------------------------------

const RootStyle = styled('div')(({ theme }) => ({
  minHeight: '100vh',
  display: 'flex',
  backgroundColor: '#F8F9FA',
}));

// ----------------------------------------------------------------------

export default function PasswordReset() {
  const navigate = useNavigate();
  const smUp = useResponsive('up', 'sm');
  
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [success, setSuccess] = useState(false);

  const handleReset = async (e) => {
    e.preventDefault();
    
    if (!email) {
      toast.error('Please enter your email address');
      return;
    }
    
    setIsSubmitting(true);
    try {
      const response = await post('accounts/reset-password-request', { email });

      if (response.status === 200) {
        setSuccess(true);
        toast.success('Password reset instructions sent to your email.');
        localStorage.setItem('PasswordResetEmail', email);
        navigate('/password-reset-confirm');
      } else {
        toast.error(response.message || 'Failed to process your request.');
      }
    } catch (error) {
      console.error('Reset password error:', error);
      toast.error('Error processing your request. Please try again later.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Page title="Password Reset">
      <RootStyle>
        <Container maxWidth="sm" sx={{ display: 'flex', alignItems: 'center', height: '100vh' }}>
          <Paper sx={{ 
            width: '100%',
            mx: 'auto', 
            p: { xs: 3, md: 4 },
            borderRadius: 3,
            boxShadow: '0 10px 40px rgba(0,0,0,0.1)',
            bgcolor: 'white'
          }}>
            <Box sx={{ mb: 4, textAlign: 'center' }}>
              <Box sx={{ display: 'flex', justifyContent: 'center', mb: 3 }}>
                <Logo />
              </Box>
              <Typography variant="h4" gutterBottom fontWeight="700" color="#1F50C2">
                Forgot Your Password?
              </Typography>
              
              <Typography sx={{ color: 'text.secondary' }}>
                Don't worry, we'll send you instructions to reset your password.
              </Typography>
            </Box>

            {success ? (
              <Box sx={{ mb: 3 }}>
                <Alert severity="success" sx={{ mb: 3 }}>
                  Password reset instructions have been sent to your email.
                </Alert>
                <Typography variant="body2" sx={{ mb: 3 }}>
                  Please check your email inbox and follow the instructions to reset your password.
                  If you don't see the email, please check your spam folder.
                </Typography>
                <Button
                  fullWidth
                  component={RouterLink}
                  to="/login"
                  variant="contained"
                  sx={{ 
                    py: 1.5, 
                    backgroundColor: '#1F50C2', 
                    '&:hover': { backgroundColor: '#0F2B77' },
                    borderRadius: '8px',
                    fontWeight: 600
                  }}
                >
                  Back to Login
                </Button>
              </Box>
            ) : (
              <Box component="form" onSubmit={handleReset}>
                <TextField
                  label="Email Address"
                  fullWidth
                  variant="outlined"
                  margin="normal"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="<EMAIL>"
                  type="email"
                  required
                  autoFocus
                  sx={{ mb: 3 }}
                />
                
                <Button
                  type="submit"
                  variant="contained"
                  fullWidth
                  disabled={isSubmitting}
                  sx={{ 
                    py: 1.5, 
                    backgroundColor: '#1F50C2', 
                    '&:hover': { backgroundColor: '#0F2B77' },
                    borderRadius: '8px',
                    fontWeight: 600
                  }}
                >
                  {isSubmitting ? (
                    <CircularProgress size={24} color="inherit" />
                  ) : (
                    'Reset Password'
                  )}
                </Button>
                
                <Box sx={{ mt: 3, textAlign: 'center' }}>
                  <Typography variant="body2">
                    Remember your password?{' '}
                    <Link 
                      component={RouterLink} 
                      to="/login" 
                      variant="subtitle2"
                      sx={{ color: '#1F50C2', fontWeight: 600 }}
                    >
                      Back to Login
                    </Link>
                  </Typography>
                </Box>
              </Box>
            )}
            
            <Typography variant="body2" align="center" sx={{ mt: 3 }}>
              Don't have an account?{' '}
              <Link 
                variant="subtitle2" 
                component={RouterLink} 
                to="/register"
                sx={{ color: '#1F50C2', fontWeight: 600 }}
              >
                Get started
              </Link>
            </Typography>
          </Paper>
        </Container>
      </RootStyle>
    </Page>
  );
}
