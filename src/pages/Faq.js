  import React, {useState} from 'react';
  import { Link } from 'react-router-dom';
  import { IconButton } from '@mui/material';
  import Footer from './Footer'
  import Header from './Header'
  import Iconify from '../components/Iconify';


  const Accordion = ({ items }) => {
    const [openItem, setOpenItem] = useState(null);
  
    const toggle = (index) => {
      setOpenItem(openItem === index ? null : index);
    };
  
    return (
      <div className="w-full space-y-2">
        {items.map((item, index) => (
          <div key={index} className="border border-gray-200 rounded-md faq-answer-block">
            <button
              className="w-full text-left px-4 py-3 font-medium faq-answer-title"
              onClick={() => toggle(index)}
            >
               <span>{item.question}</span> 
               <IconButton className="toggle-btn">
                {(`${openItem}` === `${index}`)? (<Iconify icon="eva:arrow-down-outline" color="black" />):(<Iconify icon="eva:arrow-right-outline" color="black" />)} 
               </IconButton>
            </button>
            {openItem === index && (
              <div
                className="faq-answer"
                dangerouslySetInnerHTML={{ __html: item.answer }}
              />
            )}
          </div>
        ))}
      </div>
    );
  }

  
  const faqItems = [
    {
      question: 'What is Liquidity Rail?',
      answer: `<p>Liquidity Rail is a platform that allows enterprises to effortlessly connect to multiple payment methods and currencies through a single API connection. It also enables users to use stablecoins to make payments across multiple currencies.</p>`
    },
    {
      question: 'How does Liquidity Rail function?',
      answer: `<p>Using blockchain technology, Liquidity Rail combines the notification and settlement layers into a single transaction. This integration enables counterparties to transact without pre-funded float accounts, using stablecoins for instant interoperability and global transactions.</p>`
    },
    {
      question: 'Who can benefit from using Liquidity Rail?',
      answer: `<p>Financial entities such as fintech, banks, remittance companies, digital asset wallets and payment processors can utilize Liquidity Rail to execute efficient transactions without the necessity of pre-funding or exposure to counterparty risks.</p>`
    },
    {
      question: 'What are the key use cases of Liquidity Rail?',
      answer: `<p>Liquidity Rail supports various applications, including:</p>
                <ol>
                    <li><strong>Remittances:</strong> Transitioning remittance companies from traditional pre-funded models to stablecoin-based rails for faster settlements at competitive rates.</li>
                    <li><strong>Crypto Wallets:</strong> Facilitating instant fiat withdrawals for crypto wallet providers, enabling global fiat withdrawal options without multiple integrations.</li>
                    <li><strong>Cross-Border Payments:</strong> Empowering fintech companies to offer cross-border payment solutions with minimal technical overhead by accessing liquidity via stablecoin transfers.</li>
                </ol>`
    },
    {
      question: 'How does Liquidity Rail integrate with local financial systems?',
      answer: `<p>Liquidity Rail consists of an extensive network of local payment channels, including mobile money platforms like M-Pesa, ensuring users can receive funds through their preferred methods.</p>`
    },
    {
      question: 'What are the benefits of using Liquidity Rail?',
      answer: `<p>The platform offers several advantages:</p>
                <ol>
                    <li><strong>Simplified Integration:</strong> A single integration grants access to a vast and continually expanding network of payment partners and trading pairs.</li>
                    <li><strong>No pre-funding:</strong> Users can make payments without pre-funding float accounts allowing them to scale without stretching their liquidity.</li>
                    <li><strong>Compliance:</strong> Liquidity Rail collaborates with a comprehensive network of compliant payment partners and liquidity providers globally, ensuring all transactions adhere to regulatory standards.</li>
                </ol>`
    },
    {
      question: 'Is Liquidity Rail compliant with regulatory standards?',
      answer: `<p>Yes, Liquidity Rail ensures that all transactions are conducted in a regulatory-compliant manner by partnering with a global network of compliant payment partners and liquidity providers.</p>`
    },
    {
      question: 'How does Liquidity Rail enhance operational efficiency for financial institutions?',
      answer: `<p>By eliminating the need for pre-funded float accounts and integrating notification and settlement processes, Liquidity Rail reduces overhead costs and streamlines transaction workflows for financial institutions.</p>`
    },
    {
      question: 'Does using the Liquidity Rail require us to obtain a money transmitter license or any other financial services license?',
      answer: `<p>Core legal requirement for go-to-market; likely to be asked first.</p>`
    },
    {
      question: 'How does the Liquidity Rail ensure compliance with AML and KYC regulations?',
      answer: `<p>Mandatory for financial institutions and high-risk transactions.</p>`
    },
    {
      question: 'Are the stablecoins used in the Liquidity Rail compliant with local regulations in the jurisdictions where we operate?',
      answer: `<p>Ensures stablecoin legality in target markets.</p>`
    },
    {
      question: 'What is the average settlement time for a transaction, and how does it compare to traditional remittance methods?',
      answer: `<p>All transactions are processed instantly or t+0.</p>`
    },
    {
      question: 'Does the platform provide tools for regulatory reporting, such as transaction logs or compliance reports?',
      answer: `<p>Yes, we provide this through our API and dashboard.</p>`
    },
    {
      question: 'In which jurisdictions is the Liquidity Rail legally operable, and are there any restricted regions?',
      answer: `<p>Our geographic reach can be found on our geographical reach page.</p>`
    }
  ];
  

  export default function Faq() {
    return (
      <>
      <Header />  
      <div className="container privacy_container">
        <div className="policy-wrapper">
          <div className="policy-header">
            <h1 className="policy-title">Frequently Asked Questions</h1>
            <p className="text-left text-gray-600 mb-12">
               Everything you need to know about Liquidity Rail
            </p>
          </div>
  
          <div className="policy-content">
            <section id="introduction" className="policy-section">
              <Accordion items={faqItems} />
            </section>
          </div>
        </div>
      </div>
      <Footer />
     </>  
    )
  }
  