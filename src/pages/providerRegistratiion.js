import React, { useState, useEffect } from 'react';
import { Link as RouterLink, useNavigate } from 'react-router-dom';
// @mui
import {
  styled,
  Container,
  Typography,
  Box,
  TextField,
  Button,
  Link,
  InputAdornment,
  IconButton,
  FormControlLabel,
  Checkbox,
  Stack,
  CircularProgress,
  Tabs,
  Tab,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  ListSubheader,
  Autocomplete
} from '@mui/material';
import axios from 'axios';
// icons
import VisibilityIcon from '@mui/icons-material/Visibility';
import VisibilityOffIcon from '@mui/icons-material/VisibilityOff';
import PersonOutlineIcon from '@mui/icons-material/PersonOutline';
import BusinessIcon from '@mui/icons-material/Business';
import { toast } from 'react-toastify';
import { post, get, BASE_URL } from '../api';
// hooks
import useResponsive from '../hooks/useResponsive';
// components
import Page from '../components/Page';
import Logo from '../components/Logo';

// ----------------------------------------------------------------------

const RootStyle = styled('div')(({ theme }) => ({
  minHeight: '100vh',
  display: 'flex',
  backgroundColor: '#F8F9FA',
}));

// List of African countries with their associated currencies
const AFRICAN_COUNTRIES = [
  { country: 'Algeria', currency: 'DZD' },
  { country: 'Angola', currency: 'AOA' },
  { country: 'Benin', currency: 'XOF' },
  { country: 'Botswana', currency: 'BWP' },
  { country: 'Burkina Faso', currency: 'XOF' },
  { country: 'Burundi', currency: 'BIF' },
  { country: 'Cabo Verde', currency: 'CVE' },
  { country: 'Cameroon', currency: 'XAF' },
  { country: 'Central African Republic', currency: 'XAF' },
  { country: 'Chad', currency: 'XAF' },
  { country: 'Comoros', currency: 'KMF' },
  { country: 'Congo', currency: 'XAF' },
  { country: 'Côte d\'Ivoire', currency: 'XOF' },
  { country: 'Djibouti', currency: 'DJF' },
  { country: 'Egypt', currency: 'EGP' },
  { country: 'Equatorial Guinea', currency: 'XAF' },
  { country: 'Eritrea', currency: 'ERN' },
  { country: 'Eswatini', currency: 'SZL' },
  { country: 'Ethiopia', currency: 'ETB' },
  { country: 'Gabon', currency: 'XAF' },
  { country: 'Gambia', currency: 'GMD' },
  { country: 'Ghana', currency: 'GHS' },
  { country: 'Guinea', currency: 'GNF' },
  { country: 'Guinea-Bissau', currency: 'XOF' },
  { country: 'Kenya', currency: 'KES' },
  { country: 'Lesotho', currency: 'LSL' },
  { country: 'Liberia', currency: 'LRD' },
  { country: 'Libya', currency: 'LYD' },
  { country: 'Madagascar', currency: 'MGA' },
  { country: 'Malawi', currency: 'MWK' },
  { country: 'Mali', currency: 'XOF' },
  { country: 'Mauritania', currency: 'MRU' },
  { country: 'Mauritius', currency: 'MUR' },
  { country: 'Morocco', currency: 'MAD' },
  { country: 'Mozambique', currency: 'MZN' },
  { country: 'Namibia', currency: 'NAD' },
  { country: 'Niger', currency: 'XOF' },
  { country: 'Nigeria', currency: 'NGN' },
  { country: 'Rwanda', currency: 'RWF' },
  { country: 'Sao Tome and Principe', currency: 'STN' },
  { country: 'Senegal', currency: 'XOF' },
  { country: 'Seychelles', currency: 'SCR' },
  { country: 'Sierra Leone', currency: 'SLL' },
  { country: 'Somalia', currency: 'SOS' },
  { country: 'South Africa', currency: 'ZAR' },
  { country: 'South Sudan', currency: 'SSP' },
  { country: 'Sudan', currency: 'SDG' },
  { country: 'Tanzania', currency: 'TZS' },
  { country: 'Togo', currency: 'XOF' },
  { country: 'Tunisia', currency: 'TND' },
  { country: 'Uganda', currency: 'UGX' },
  { country: 'Zambia', currency: 'ZMW' },
  { country: 'Zimbabwe', currency: 'ZWL' }
];

// Transfer types
const TRANSFER_TYPES = [
  { value: 'bank_transfer', label: 'Bank Transfer' },
  { value: 'mobile_transfer', label: 'Mobile Transfer' }
];

// ----------------------------------------------------------------------

export default function Register() {
  const navigate = useNavigate();
  const smUp = useResponsive('up', 'sm');
  const mdUp = useResponsive('up', 'md');
  
  const [accountType, setAccountType] = useState('provider');
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
    agreeToTerms: false,
    accountType: 'provider',
    business_name: '',
    country: '',
    payout_currency: '',
    transfer_type: '',
    assets: []
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  
  // Added states for assets and services
  const [assets, setAssets] = useState([]);
  const [selectedAssets, setSelectedAssets] = useState([]);
  const [selectedTransferTypes, setSelectedTransferTypes] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedCountry, setSelectedCountry] = useState(null);

  // Fetch assets on component mount
  useEffect(() => {
    const loadAssets = async () => {
      setIsLoading(true);
      try {
        // Load assets
        const assetsData = await get('accounts/getAssets');
        if (assetsData && assetsData.data) {
          // Filter assets to only include type='coin' and sort with CINS at the top
          const filteredAssets = assetsData.data.filter(asset => asset.type === 'coin');
          
          // Sort assets with CINS at the top and fiat at the bottom
          const sortedAssets = [...filteredAssets].sort((a, b) => {
            // Check if asset contains "CINS" (case insensitive) to put it at the top
            const aIsCins = a.asset_code.toUpperCase().includes('CINS');
            const bIsCins = b.asset_code.toUpperCase().includes('CINS');
            
            if (aIsCins && !bIsCins) return -1;
            if (!aIsCins && bIsCins) return 1;
            return 0;
          });
          
          setAssets(sortedAssets);
        }
      } catch (error) {
        console.error('Error loading assets:', error);
        toast.error('Failed to load assets');
      } finally {
        setIsLoading(false);
      }
    };
    
    loadAssets();
  }, []);

  const handleChange = (e) => {
    const { name, value, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'agreeToTerms' ? checked : value
    }));
  };

  const handleAccountTypeChange = (event, newValue) => {
    if (newValue !== null) {
      setAccountType(newValue);
      setFormData(prev => ({
        ...prev,
        accountType: newValue
      }));
    }
  };

  const handleAssetChange = (e) => {
    const assetCode = e.target.value;
    if (!selectedAssets.includes(assetCode)) {
      setSelectedAssets([...selectedAssets, assetCode]);
      setFormData(prev => ({
        ...prev,
        assets: [...prev.assets, assetCode]
      }));
    }
  };

  const handleTransferTypeChange = (e) => {
    const transferType = e.target.value;
    if (!selectedTransferTypes.includes(transferType)) {
      setSelectedTransferTypes([...selectedTransferTypes, transferType]);
      setFormData(prev => ({
        ...prev,
        transfer_types: [...(prev.transfer_types || []), transferType]
      }));
    }
  };

  const removeAsset = (assetToRemove) => {
    const updatedAssets = selectedAssets.filter(asset => asset !== assetToRemove);
    setSelectedAssets(updatedAssets);
    setFormData(prev => ({
      ...prev,
      assets: updatedAssets
    }));
  };

  const removeTransferType = (typeToRemove) => {
    const updatedTypes = selectedTransferTypes.filter(type => type !== typeToRemove);
    setSelectedTransferTypes(updatedTypes);
    setFormData(prev => ({
      ...prev,
      transfer_types: updatedTypes
    }));
  };

  const handleRegister = async (e) => {
    e.preventDefault();
    


    // Basic validation
    if (accountType !== 'provider' && ( !formData.firstName || !formData.lastName || !formData.email || !formData.password  )) {
      toast.dismiss()
      toast.error('Please fill in all required fields');
      return;
    }
    
    if (accountType !== 'provider' && formData.password !== formData.confirmPassword) {
      toast.dismiss()
      toast.error('Passwords do not match');
      return;
    }





    // Additional validation for provider type
    if (accountType === 'provider') {

      if (!formData.firstName || !formData.lastName || !formData.email  ) {
        toast.dismiss()
        toast.error('Please fill in all required fields');
        return;
      }

      if (!formData.business_name) {
        toast.error('Business name is required for providers');
        return;
      }
      
      if (!formData.country) {
        toast.error('Please select a country');
        return;
      }

      // if (selectedAssets.length === 0) {
      //   toast.error('Please select at least one asset');
      //   return;
      // }
      //
      // if (selectedTransferTypes.length === 0) {
      //   toast.error('Please select at least one transfer type');
      //   return;
      // }
    }
    

    if (!formData.agreeToTerms) {
      toast.dismiss()
      toast.error('Please agree to the Terms of Service and Privacy Policy');
      return;
    }



    setIsSubmitting(true);
    try {


      const requestData = {
        first_name: formData.firstName,
        last_name: formData.lastName,
        email: formData.email,
        account_type: accountType
      };

      // Add provider-specific fields if account type is user
      if (accountType !== 'provider') {
        requestData.password = formData.password;
      }
      
      // Add provider-specific fields if account type is provider
      if (accountType === 'provider') {
        requestData.business_name = formData.business_name;
        requestData.country = formData.country;
        requestData.payin_assets = selectedAssets;
        requestData.transfer_types = selectedTransferTypes;
        // return alert(JSON.stringify(requestData));
        return completeZohoRequestSend(requestData);
      }
      


      const response = await post('accounts/register', requestData);
      if (response.status === 100) {
        toast.success('Registration successful! Please verify your email.');
        // Navigate to email confirmation page with email and account type
        navigate('/email-confirmation', { 
          state: { 
            email: formData.email,
            accountType
          } 
        });
      } else {
        toast.error(response.message || 'Registration failed. Please try again.');
      }



    } catch (error) {
      console.error('Registration error:', error);
      toast.error('Error during registration. Please try again later.');
    } finally {
      setIsSubmitting(false);
    }
  };



  const completeZohoRequestSend = async (dataCollection) => {
    try{

       setIsSubmitting(true);
       const axiosInstance = axios.create({
          baseURL: BASE_URL,   // "http://localhost:3000/zoho/add", //
          headers: {'Content-Type': 'application/json' },
       });
       const response = await axiosInstance.post("", dataCollection);
       setIsSubmitting(false);
       if(response.data.data[0].code === 'SUCCESS'){

         toast.dismiss()
         toast.success("Request has successfully been sent. The MUDA team will get back to you shortly");
         navigate('/complete/provider/registration?status=successful', {
           status: {
             message: response.data.data[0]
           }
         });
       } else {

         toast.dismiss()
         toast.error(`${response.data.data[0].message}. ${response.data.data[0].details.api_name} already recorded`);
       }

    }catch(error){

       setIsSubmitting(false);
       console.error("Zoho Error:", error.response?.data || error.message);
       toast.dismiss()
       toast.error("Updating provider account request placement");
    }
  }

  return (
    <Page title="Register">
      <RootStyle>
        <Container maxWidth="sm" sx={{ margibnTop: '30px', display: 'flex', alignItems: 'center', minHeight: '100vh' }}>
         
          <Paper sx={{ 
            width: '100%',
            mx: 'auto', 
            overflow: 'hidden',
            borderRadius: 3,
            boxShadow: '0 10px 40px rgba(0,0,0,0.1)',
            bgcolor: 'white',
            p: { xs: 3, md: 4 },
          }}>



            <Box sx={{ mb: 4, textAlign: 'center' }}>
              <Box sx={{ display: 'flex', justifyContent: 'center', mb: 3 }}>
                <Logo />
              </Box>
              <Typography variant="h4" gutterBottom fontWeight="700" color="#1F50C2">
                Create An Account
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Register with Liquidity Rail to start your journey
              </Typography>
            </Box>

            {/* Account Type Selection */}
            {/* <Tabs 
              value={accountType} 
              onChange={handleAccountTypeChange} 
              variant="fullWidth" 
              sx={{ 
                mb: 4,
                '& .MuiTab-root': {
                  minHeight: 64,
                  borderRadius: 2,
                  fontWeight: 600,
                  color: 'text.secondary',
                  backgroundColor: '#f5f5f5',
                  '&.Mui-selected': {
                    color: 'white',
                    backgroundColor: '#1F50C2',
                  },
                  mx: 1
                },
                '& .MuiTabs-indicator': {
                  display: 'none',
                },
              }}
            >
              <Tab 
                icon={<PersonOutlineIcon />} 
                label="Client" 
                value="client"
                iconPosition="start"
              />
              <Tab 
                icon={<BusinessIcon />} 
                label="Provider" 
                value="provider"
                iconPosition="start"
              />
            </Tabs> */}

            <Box component="form" onSubmit={handleRegister}>
              <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} sx={{ mb: 2 }}>
                <TextField
                  label="First Name"
                  name="firstName"
                  fullWidth
                  variant="outlined"
                  value={formData.firstName}
                  onChange={handleChange}
                  required
                />
                
                <TextField
                  label="Last Name"
                  name="lastName"
                  fullWidth
                  variant="outlined"
                  value={formData.lastName}
                  onChange={handleChange}
                  required
                />
              </Stack>
              
              <TextField
                label="Email Address"
                name="email"
                fullWidth
                variant="outlined"
                margin="normal"
                type="email"
                value={formData.email}
                onChange={handleChange}
                required
                sx={{ mb: 2 }}
              />
              
              {/* Business Name field for Provider account type */}
              {accountType === 'provider' && (
                <TextField
                  label="Business Name"
                  name="business_name"
                  fullWidth
                  variant="outlined"
                  margin="normal"
                  value={formData.business_name}
                  onChange={handleChange}
                  required
                  sx={{ mb: 2 }}
                />
              )}
              
              {/* Provider-specific fields */}
              {accountType === 'provider' && (
                <>
                  {/* Country Selection (Africa) */}
                  <Autocomplete
                    options={AFRICAN_COUNTRIES.map(item => item.country)}
                    sx={{ mb: 2 }}
                    fullWidth
                    renderInput={(params) => (
                      <TextField 
                        {...params} 
                        label="Select Country" 
                        required 
                        margin="normal"
                        helperText={selectedCountry ? `Payout Currency: ${AFRICAN_COUNTRIES.find(c => c.country === selectedCountry)?.currency || ''}` : ''}
                      />
                    )}
                    onChange={(event, newValue) => {
                      setSelectedCountry(newValue);
                      // Find the currency for the selected country
                      const countryData = AFRICAN_COUNTRIES.find(c => c.country === newValue);
                      setFormData(prev => ({
                        ...prev,
                        country: newValue,
                        payout_currency: countryData ? countryData.currency : ''
                      }));
                    }}
                  />
                </>
              )}


              
             {accountType !== 'provider' && (
                <>
                    <TextField
                      label="Password"
                      name="password"
                      type={showPassword ? 'text' : 'password'}
                      fullWidth
                      variant="outlined"
                      margin="normal"
                      value={formData.password}
                      onChange={handleChange}
                      required
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position="end">
                            <IconButton
                              onClick={() => setShowPassword(!showPassword)}
                              edge="end"
                            >
                              {showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                            </IconButton>
                          </InputAdornment>
                        )
                      }}
                      sx={{ mb: 2 }}
                      />

                      <TextField
                      label="Confirm Password"
                      name="confirmPassword"
                      type={showConfirmPassword ? 'text' : 'password'}
                      fullWidth
                      variant="outlined"
                      margin="normal"
                      value={formData.confirmPassword}
                      onChange={handleChange}
                      required
                      InputProps={{
                        endAdornment: (
                          <InputAdornment position="end">
                            <IconButton
                              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                              edge="end"
                            >
                              {showConfirmPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                            </IconButton>
                          </InputAdornment>
                        )
                      }}
                      sx={{ mb: 2 }}
                      />
                 </>     
             )}   
              
              <FormControlLabel
                control={
                  <Checkbox 
                    name="agreeToTerms" 
                    checked={formData.agreeToTerms} 
                    onChange={handleChange} 
                    color="primary" 
                  />
                }
                label={
                  <Typography variant="body2">
                    I agree to the{' '}
                    <Link component={RouterLink}
                          to="/terms-of-use"
                          target="_blank"
                          rel="noopener noreferrer"
                          color="primary"
                          fontWeight="600">
                      Terms of Service
                    </Link>{' '}
                    and{' '}
                    <Link component={RouterLink}
                          to="/privacy"
                          target="_blank"
                          rel="noopener noreferrer"
                          color="primary"
                          fontWeight="600">
                      Privacy Policy
                    </Link>
                  </Typography>
                }
                sx={{ mb: 2, mt: 1 }}
              />
              


              {accountType !== 'provider' &&(
                  <Button
                        fullWidth
                        size="large"
                        type="submit"
                        variant="contained"
                        color="primary"
                        disabled={isSubmitting}
                        sx={{ 
                          height: 56, 
                          mt: 2,
                          bgcolor: '#1F50C2',
                          '&:hover': {
                            bgcolor: '#1a3fa3',
                          },
                        }}
                        >
                     {isSubmitting ? <CircularProgress size={24} color="inherit" /> : 'Create Account'}
                  </Button>
              )} 

              
              {accountType === 'provider' && (
                
                <Button
                  fullWidth
                  size="large"
                  type="submit"
                  variant="contained"
                  color="primary"
                  disabled={isSubmitting}
                  sx={{ 
                    height: 56, 
                    mt: 2,
                    bgcolor: '#1F50C2',
                    '&:hover': {
                      bgcolor: '#1a3fa3',
                    },
                  }}
                >
                  {isSubmitting ? <CircularProgress size={24} color="inherit" /> :  `Apply For A Provider Account ${isSubmitting}`}
                </Button>
              )} 

              
              <Box sx={{ mt: 3, textAlign: 'center' }}>
                <Typography variant="body2">
                  Already have an account?{' '}
                  <Link component={RouterLink} to="/login" color="primary" fontWeight="600">
                    Sign In
                  </Link>
                </Typography>
              </Box>
            </Box>
          </Paper>
        </Container>
      </RootStyle>
    </Page>
  );
}
