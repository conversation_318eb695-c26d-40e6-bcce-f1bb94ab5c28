import { useState, useEffect } from 'react';
import { Link as RouterLink } from 'react-router-dom';
// material
import { DataGrid } from '@mui/x-data-grid';
import { Container, Stack, Typography, Button, Tabs, Tab, Box, Paper, Card, CardContent, Divider } from '@mui/material';

// components
import Page from '../components/Page';
import Iconify from '../components/Iconify';
import ModalPop from '../sections/modals/MainModalView';

import PaymentMethods from './PaymentMethods';
import Addresses from './Addresses';
import ProviderAddresses from './ProviderAddresses';

import AcceptedCurrencies from './AcceptedCurrencies';
import ServicesOffered from './ServicesOffered';
import ExchangeEndPoint from './ExchangeEndPoint';
import { getAddress, getProviderInfo, getProviderServices, getProviderAddresses } from '../services';

export default function Settings() {
  const [modelData, setModelData] = useState({});
  const [open, setOpen] = useState(false);
  const [modelType, setModelType] = useState('');
  const [allWallets, setAllWallets] = useState([]);
  const [allPaymentMethods, setAllPaymentMethods] = useState([]);
  const [tabChange, setTabChange] = useState('addresses');
  const [user, setUser] = useState({});
  const [providerInfo, setProviderInfo] = useState([]);
  const [tabIndex, setTabIndex] = useState(0);

  useEffect(() => {
    const userObj = localStorage.getItem('userObject');
    if (userObj !== null && userObj !== "") {
      const theUser = JSON.parse(userObj);
      setUser(theUser);
    }
  }, [setUser]);

  const showWalletModel = async (optionTrend) => {
    setOpen(true);
    setModelType(optionTrend);
    setModelData({ "type": optionTrend, "content": "" });
  };

  const proverInfoObj = async () => {
    await getProviderServices();
    await getProviderAddresses();
    const proverInfoDetails = await getProviderInfo();
    if (proverInfoDetails.data) {
      setProviderInfo(proverInfoDetails.data);
    }
  };

  useEffect(() => {
    proverInfoObj();
  }, []);

  const chamgeSection = async (tabChange) => {
    setTabChange(tabChange);
  };

  const onModelComplete = async (options) => {
    console.log("model complete", options);
  };

  useEffect(() => {
    const initialContent = async () => {
      const transact = await getAddress();
      if (transact?.data?.length > 0) {
        const addressArr = [];
        transact?.data?.map((address) => {
          return addressArr.push({ id: address?.address_id, token: address?.chain, address: address?.address });
        });
        setAllWallets(addressArr);
      }
    };
    initialContent();
  }, [getAddress]);

  const handleTabChange = (event, newIndex) => {
    setTabIndex(newIndex);
  };

  // Determine which tabs to show based on user type
  const isClient = user?.user_type === 'client';
  const isProvider = user?.user_type === 'provider';

  return (
    <Page title="Settings">
      <Container>
        <Card sx={{ mb: 4, borderRadius: 2, boxShadow: '0 4px 20px rgba(0,0,0,0.1)' }}>
          <CardContent>
            <Typography variant="h4" sx={{ mb: 3, fontWeight: 700, color: '#1F50C2' }}>
              Account Settings
            </Typography>
            <Divider sx={{ mb: 3 }} />
            
            <Tabs 
              value={tabIndex} 
              onChange={handleTabChange} 
              aria-label="Settings Tabs"
              sx={{
                '& .MuiTab-root': {
                  fontWeight: 600,
                  textTransform: 'none',
                  minHeight: 48,
                  mx: 1,
                  '&.Mui-selected': {
                    color: '#1F50C2',
                  },
                },
                '& .MuiTabs-indicator': {
                  backgroundColor: '#1F50C2',
                  height: 3,
                },
                mb: 4
              }}
              variant="scrollable"
              scrollButtons="auto"
            >
              {isClient && <Tab label="Payment Methods" />}
              {isClient && <Tab label="Addresses" />}
              {isProvider && <Tab label="Services Offered" />}
              {isProvider && <Tab label="Exchange End Point" />}
              {isProvider && <Tab label="Provider Addresses" />}
            </Tabs>

            <Box sx={{ py: 2 }}>
              {isClient && tabIndex === 0 && <PaymentMethods />}
              {isClient && tabIndex === 1 && <Addresses />}
              {isProvider && tabIndex === 0 && <ServicesOffered />}
              {isProvider && tabIndex === 1 && <ExchangeEndPoint />}
              {isProvider && tabIndex === 2 && <ProviderAddresses />}
            </Box>
          </CardContent>
        </Card>
      </Container>
    </Page>
  );
}
