import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
// @mui
import {
  styled,
  Container,
  Typography,
  Box,
  TextField,
  Button,
  CircularProgress,
  Paper
} from '@mui/material';
import { toast } from 'react-toastify';
import { post } from '../api';
// components
import Page from '../components/Page';
import Logo from '../components/Logo';

// ----------------------------------------------------------------------

const RootStyle = styled('div')(({ theme }) => ({
  minHeight: '100vh',
  display: 'flex',
  backgroundColor: '#F8F9FA',
}));

// ----------------------------------------------------------------------

export default function EmailConfirmation() {
  const navigate = useNavigate();
  const location = useLocation();
  const [otp, setOtp] = useState('');
  const [email, setEmail] = useState('');
  const [accountType, setAccountType] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [countdown, setCountdown] = useState(60);
  const [canResend, setCanResend] = useState(false);

  useEffect(() => {
    // Get email and account type from location state or localStorage
    const userData = location.state || {};
    if (userData.email) {
      setEmail(userData.email);
    }
    if (userData.accountType) {
      setAccountType(userData.accountType);
      // Save account type to localStorage for later use
      localStorage.setItem('accountType', userData.accountType);
    }
  }, [location]);

  useEffect(() => {
    // Countdown timer for resending OTP
    if (countdown > 0 && !canResend) {
      const timer = setTimeout(() => {
        setCountdown(countdown - 1);
      }, 1000);
      return () => clearTimeout(timer);
    }
    if (countdown === 0 && !canResend) {
      setCanResend(true);
    }
  }, [countdown, canResend]);

  const handleVerifyOtp = async (e) => {
    e.preventDefault();
    
    if (!otp) {
      toast.error('Please enter the verification code');
      return;
    }
    
    setIsSubmitting(true);
    try {
      const response = await post('accounts/verifyOtp', { 
        email, 
        code:otp 
      });

      if (response.status === 200) {
        toast.success('Email verification successful!');
        
        // Determine where to navigate based on account type
        if (accountType === 'provider') {
          navigate('/welcome-provider');
        } else {
          navigate('/login');
        }
      } else {
        toast.error(response.message || 'Verification failed. Please try again.');
      }
    } catch (error) {
      console.error('Verification error:', error);
      toast.error('Error during verification. Please try again later.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleResendOtp = async () => {
    if (!email) {
      toast.error('Email address is missing. Please go back to registration.');
      return;
    }
    
    try {
      setIsSubmitting(true);
      const response = await post('accounts/resendOTP', { email });
      
      if (response.status === 200) {
        toast.success('Verification code resent to your email');
        setCountdown(60);
        setCanResend(false);
      } else {
        toast.error(response.message || 'Failed to resend verification code');
      }
    } catch (error) {
      console.error('Resend OTP error:', error);
      toast.error('Error resending verification code');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Page title="Email Verification">
      <RootStyle>
        <Container maxWidth="sm" sx={{ display: 'flex', alignItems: 'center', height: '100vh' }}>
          <Paper sx={{ 
            width: '100%',
            mx: 'auto', 
            overflow: 'hidden',
            borderRadius: 3,
            boxShadow: '0 10px 40px rgba(0,0,0,0.1)',
            bgcolor: 'white',
            p: { xs: 3, md: 4 },
          }}>
            <Box sx={{ mb: 4, textAlign: 'center' }}>
              <Box sx={{ display: 'flex', justifyContent: 'center', mb: 3 }}>
                <Logo />
              </Box>
              <Typography variant="h4" gutterBottom fontWeight="700" color="#1F50C2">
                Verify Your Email
              </Typography>
              <Typography variant="body2" color="text.secondary">
                We've sent a verification code to {email || 'your email'}. 
                Please check your inbox and enter the code below.
              </Typography>
            </Box>

            <Box component="form" onSubmit={handleVerifyOtp}>
              <TextField
                label="Verification Code"
                fullWidth
                variant="outlined"
                margin="normal"
                value={otp}
                onChange={(e) => setOtp(e.target.value)}
                placeholder="Enter your verification code"
                type="text"
                autoFocus
                inputProps={{ maxLength: 6 }}
                sx={{ mb: 3 }}
              />
              
              <Button
                type="submit"
                variant="contained"
                fullWidth
                disabled={isSubmitting}
                sx={{ 
                  py: 1.5, 
                  backgroundColor: '#1F50C2', 
                  '&:hover': { backgroundColor: '#0F2B77' },
                  borderRadius: '8px',
                  fontWeight: 600,
                  mb: 2
                }}
              >
                {isSubmitting ? (
                  <CircularProgress size={24} color="inherit" />
                ) : (
                  'Verify Code'
                )}
              </Button>
              
              <Box sx={{ textAlign: 'center', mt: 2 }}>
                <Typography variant="body2" sx={{ mb: 1 }}>
                  Didn't receive the code?
                </Typography>
                
                <Button
                  variant="text"
                  disabled={!canResend || isSubmitting}
                  onClick={handleResendOtp}
                  sx={{ color: '#1F50C2' }}
                >
                  {isSubmitting ? (
                    <CircularProgress size={16} color="inherit" sx={{ mr: 1 }} />
                  ) : null}
                  {canResend ? 'Resend Code' : `Resend in ${countdown}s`}
                </Button>
              </Box>
            </Box>
          </Paper>
        </Container>
      </RootStyle>
    </Page>
  );
} 