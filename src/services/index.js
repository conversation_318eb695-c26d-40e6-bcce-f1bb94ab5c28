import { useEffect } from 'react';
import axios from '../utils/axios';

const setSession = (tokenKey, serviceToken, dataType, requestType) => {

  if (serviceToken && serviceToken !== "") {

    if (dataType) {
      serviceToken = JSON.stringify(serviceToken);
    }
    localStorage.setItem(tokenKey, serviceToken);
    if (requestType === 'login') {
      axios.defaults.headers.common.Authorization = `Bearer ${serviceToken}`;
    }
  } else {

    localStorage.removeItem(tokenKey);
    if (requestType === 'login') {
      delete axios.defaults.headers.common.Authorization;
    }

  }
};


// return the parsed/string obj 
// app tokens include activeCompany, serviceToken, serviceUser
const parseData = async (sessionData, sessionYype = 'string') => {

  const theData = window.localStorage.getItem(sessionData);
  if (sessionYype === "json") {
    return JSON.parse(theData);
  }
  return theData;
}

export async function loginAccount(data) {

     try {

        const dataObj = data;
        const sendData  = {
                             "email": dataObj.email,
                             "password": dataObj.password
                          };                 
        const response = await axios.post('/accounts/login', sendData );
        const responseData = response;

        if (responseData?.status === 200 || responseData?.status === 200) {
          return responseData?.data
        } 

        return responseData


    } catch (e) {
        return {'status': 410, 'message': 'error submitting request'}
    }

}

export async function registerAccount(data) {
    try {

        const dataObj = data;
        const sendData  = { 
                            "full_name": dataObj.fullName,
                            "email":dataObj.email,
                            "password": dataObj.password,
                            "phone_number": dataObj.phone_number,
                            "business_name": dataObj.business_name,
                            "user_type": dataObj.user_type,
                            "services": JSON.stringify(dataObj.payout_services),
                            "assets": JSON.stringify(dataObj.payin_assets)
                           };

        const response     = await axios.post('/accounts/register', sendData );
        const responseData = response;

        if (responseData?.status === 200 || responseData?.status === 200) {
          return responseData?.data
        } 

        return responseData

    } catch (e) {
        return {'status': 410, 'message': 'error submitting request'}
    }

}





export async function addPaymentMethod(data) {
  try {

      const dataObj = data;
      const sendData  = {
                          type: data.type,
                          currency: data.currency,
                          phone_number: data.phone_number,
                          country_code: data.country_code,
                          network: data.network,
                          account_name: data.account_name,
                          bank_name: data.bank_name,
                          bank_code: data.bank_code,
                          bank_address: data.bank_address,
                          account_number: data.account_number,
                          bank_country: data.bank_country,
                          bank_phone_number: data.bank_phone_number,
                          sort_code: data.sort_code, 
                          swift_code : data.swift_code
                        };
      const response     = await axios.post('/accounts/addPaymentMethod', sendData );
      const responseData = response;
      if (responseData?.status === 200 || responseData?.status === 200) {
        return responseData?.data
      } 

      return responseData

  } catch (e) {
      return {'status': 410, 'message': 'error submitting request'}
  }
}

export async function getPaymentMethods(data = "") {
  try {

      const dataObj = data;
      const responseData = await axios.get('/accounts/getPaymentMethods');
      if (responseData?.status === 200 || responseData?.status === 200) {
        return responseData?.data
      } 
      return responseData

  } catch (e) {
      return {'status': 410, 'message': 'error submitting request'}
  }
}

export async function getPaymentMethodForCurrency(data = "") {
  try {
      const dataObj = data;
      const responseData = await axios.get(`/accounts/getPaymentMethodForCurrency/${data?.currency}`);
      if (responseData?.status === 200 || responseData?.status === 200) {
        return responseData?.data
      } 
      return responseData

  } catch (e) {
      return {'status': 410, 'message': 'error submitting request'}
  }
}

export async function addAddress(data) {
    try {
        const dataObj = data;
        const sendData  = { 
                            chain: data?.chain,
                            address: data?.address,
                            wallet_name: data?.wallet_name
                          };                 
        const response     = await axios.post('/accounts/addAddress', sendData );
        const responseData = response;

        if (responseData?.status === 200 || responseData?.status === 200) {
          return responseData?.data
        } 

        return responseData

    } catch (e) {
        return {'status': 410, 'message': 'error submitting request'}
    }
}

export async function getAddress(data = "") {
    try {

        const dataObj = data;
        const responseData = await axios.get('/accounts/getAddresses');
        if (responseData?.status === 200) {
          return responseData?.data
        } 

        return responseData

    } catch (e) {
        return {'status': 410, 'message': 'error submitting request'}
    }

}


export async function getTransactions(data = "") {
    try {

        const dataObj = data;
        const responseData = await axios.get('/accounts/getTransactions');
        if (responseData?.status === 200 ) {
          return responseData?.data?.data
        } 
        return responseData?.data?.data

    } catch (e) {
        return {'status': 410, 'message': 'error submitting request'}
    }

}



export async function passwordRestOtpRequest(data = "") {
  try {

      const dataObj = data;
      const sendData  = { 
                          "email": data?.email
                        };                 
      const response     = await axios.post('/users/resetPasswordRequest', sendData );
      const responseData = response;

      if (responseData?.status === 200 || responseData?.status === 200) {
        return responseData?.data
      } 
      return responseData

  } catch (e) {
      return {'status': 410, 'message': 'error submitting request'}
  }
}

export async function resendToken(data = "") {
  try {

      const dataObj = data;
      const sendData  = { 
                          "email": data?.email
                        };                 
      const response     = await axios.post('/users/resendOTP', sendData );
      const responseData = response;

      if (responseData?.status === 200 || responseData?.status === 200) {
        return responseData?.data
      } 
      return responseData

  } catch (e) {
      return {'status': 410, 'message': 'error submitting request'}
  }
}

export async function resetPasswordComplete(data = "") {
  try {

      const dataObj = data;
      const sendData  = { 
                          "email"       : data?.email,
                          "otp"         : data?.otp,
                          "newPassword" : data?.password
                        };                 
      const response     = await axios.post('/users/resetPassword', sendData );
      const responseData = response;

      if (responseData?.status === 200) {
        return responseData?.data
      } 
      return responseData

  } catch (e) {
      return {'status': 410, 'message': 'error submitting request'}
  }
}


export async function getPendingQuotes(data = "") {
  try {

      const dataObj = data;            
      const response     = await axios.get('/accounts/getPendingQuotes');
      const responseData = response;
      if (responseData?.status === 200) {
        return responseData?.data
      } 
      return responseData?.data

  } catch (e) {
      return {'status': 410, 'message': 'error submitting request'}
  }
}

export async function cancelQuote(data = "") {
  try {

      const dataObj      = data;      
      const response     = await axios.get(`/accounts/cancelQuote/${data?.id}` );
      const responseData = response;
      if (responseData?.status === 200 || responseData?.status === 200) {
        return responseData?.data
      } 
      return responseData

  } catch (e) {
      return {'status': 410, 'message': 'error submitting request'}
  }
}

export async function getChains() {
  try {     
      const response     = await axios.get(`/accounts/chains`);
      const responseData = response;
      if (responseData?.status === 200 || responseData?.status === 200) {
        return responseData?.data
      } 
      return responseData

  } catch (e) {
      return {'status': 410, 'message': 'error submitting request'}
  }
}


export async function getAssets() {
  try {     
      const response     = await axios.get(`/accounts/getAssets`);
      const responseData = response;
      if (responseData?.status === 200 || responseData?.status === 200) {
        return responseData?.data
      } 
      return responseData

  } catch (e) {
      return {'status': 410, 'message': 'error submitting request'}
  }
}


export async function getServices() {
  try {     
      const response     = await axios.get(`/accounts/services`);
      const responseData = response;
      if (responseData?.status === 200 || responseData?.status === 200) {
        return responseData?.data
      } 
      return responseData

  } catch (e) {
      return {'status': 410, 'message': 'error submitting request'}
  }
}


export async function addAcceptedAsset(data = {}) {
  try {     
      
      const dataObj = data;
      const sendData  = {
                            "asset": data?.asset,
                            "address": data?.address,
                            "memo": data?.memo,
                            "chain": data?.asset
                        };                 
      const response     = await axios.post('/accounts/addAcceptedAsset', sendData );
      const responseData = response;
      if (responseData?.status === 200 || responseData?.status === 200) {
        return responseData?.data
      } 
      return responseData

  } catch (e) {
      return {'status': 410, 'message': 'error submitting request'}
  }
}

export async function updateproviderAddress(data = {}) {
  try {     
      
      const dataObj = data;
      const sendData  = {
                            "asset": data?.asset,
                            "address": data?.address,
                            "memo": data?.memo,
                            "chain": data?.asset
                        };                 
      const response     = await axios.post('/accounts/updateproviderAddress', sendData );
      const responseData = response;
      if (responseData?.status === 200 || responseData?.status === 200) {
        return responseData?.data
      } 
      return responseData

  } catch (e) {
      return {'status': 410, 'message': 'error submitting request'}
  }
}

export async function deactivateAddress(data = {}) {
  try {     
      
      const dataObj = data;
      const sendData  = {
                          "asset": data?.asset,
                          "address": data?.address,
                          "chain": data?.chain
                        };                 
      const response     = await axios.post('/accounts/deactivateAddress', sendData );
      const responseData = response;
      if (responseData?.status === 200 || responseData?.status === 200) {
        return responseData?.data
      } 
      return responseData

  } catch (e) {
      return {'status': 410, 'message': 'error submitting request'}
  }
}



export async function getProviderAddresses(data = {}) {
  try {     
      
      const dataObj = data;              
      const response     = await axios.get('/accounts/getProviderAddresses');
      const responseData = response;
      if (responseData?.status === 200 || responseData?.status === 200) {
        return responseData?.data
      } 
      return responseData

  } catch (e) {
      return {'status': 410, 'message': 'error submitting request'}
  }
}


export async function acceptService(data = {}) {
  try {     
      
      const dataObj = data;   
      const sendData  = {
                          "service_id": data?.service_id
                        };            
      const response     = await axios.post('/accounts/acceptService', sendData);
      const responseData = response;
      if (responseData?.status === 200 || responseData?.status === 200) {
        return responseData?.data
      } 
      return responseData

  } catch (e) {
      return {'status': 410, 'message': 'error submitting request'}
  }
}


export async function disableService(data = {}) {
  try {     
      
      const dataObj = data;   
      const sendData  = {
                          "service_id": data?.service_id
                        };            
      const response     = await axios.post('/accounts/disableService', sendData);
      const responseData = response;
      if (responseData?.status === 200 || responseData?.status === 200) {
        return responseData?.data
      } 
      return responseData

  } catch (e) {
      return {'status': 410, 'message': 'error submitting request'}
  }
}


export async function getProviderServices(data = {}) {
  try {     
      
      const dataObj = data;             
      const response     = await axios.get('/accounts/getproviderServices');
      const responseData = response;
      if (responseData?.status === 200 || responseData?.status === 200) {
        return responseData?.data
      } 
      return responseData

  } catch (e) {
      return {'status': 410, 'message': 'error submitting request'}
  }
}


export async function updatedRatesUrl(data = {}) {
  try {     
      
      const dataObj   = data;   
      const sendData  = {
                          "url": data?.url
                        };             
      const response  = await axios.post('/accounts/updatedRatesUrl', sendData);
      const responseData = response;
      if (responseData?.status === 200 || responseData?.status === 200) {
        return responseData?.data
      } 
      return responseData

  } catch (e) {
      return {'status': 410, 'message': 'error submitting request'}
  }
}



export async function getProviderInfo(data = {}) {
  try {     
      const dataObj   = data;              
      const response  = await axios.post('/accounts/getProviderInfo');
      const responseData = response;
      if (responseData?.status === 200 || responseData?.status === 200) {
        return responseData?.data
      } 
      return responseData
  } catch (e) {
      return {'status': 410, 'message': 'error submitting request'}
  }
}

export async function verifyPaymentMethod(data = {}) {
  try {
    const sendData  = {
      "provider_type":data?.provider_type,
      "bank_code":data?.bank_code,
      "account_number":data?.account_number,
      "currency":data?.currency
    }; 
    const { status, data: responseData } = await axios.post('/account/verifyAccount', sendData);

    return (status === 200 || status === 200) ? responseData : { status, data: responseData };
  } catch (error) {
    return { status: 410, message: 'Error submitting request' };
  }
}



export async function confirmPayment(data = {}) {
  try {     
      const dataObj   = data;   
      const sendData  = {
                          "hash": data?.hash,
                          "trans_id":data?.trans_id
                        };               
      const response  = await axios.post('/accounts/confirmPayment', sendData);
      const responseData = response;
      if (responseData?.status === 200 || responseData?.status === 200) {
        return responseData?.data
      } 
      return responseData
  } catch (e) {
      return {'status': 410, 'message': 'error submitting request'}
  }
}









