import {useEffect} from 'react';
import { Navigate, useRoutes, useNavigate } from 'react-router-dom';
// layouts
import DashboardLayout from './layouts/dashboard';
import LogoOnlyLayout from './layouts/LogoOnlyLayout';
// pages
import Blog from './pages/Blog';
import User from './pages/User';
import Profile from  './pages/Profile';
import Login from './pages/Login';
import ProviderRegister from './pages/providerRegistratiion';
import NotFound from './pages/Page404';
import Register from './pages/Register';
import Products from './pages/Products';
import DashboardApp from './pages/DashboardApp';
import Transactions from './pages/Transactions';
import Payments from './pages/Payments';
import Settings from './pages/Settings';
import Support from './pages/Support';
import Confirmation from './pages/Confirmation';
import PasswordResetOtp from './pages/PasswordResetOtp'
import PasswordReset    from './pages/PasswordReset'
import Index    from './pages/Index'
import EmailConfirmation from './pages/EmailConfirmation'
import WelcomeProvider from './pages/WelcomeProvider'
import CompleteZohoRegistration from './pages/CompleteZohoRegistration';
import Faq from './pages/Faq'
import Terms from './pages/Terms'

import Privacy    from './pages/Privacy'

const accessToken = localStorage.getItem('authJWT');

const AuthGuard = ({ children }) => {
  // (accessToken !== null) ? <Navigate to="/dashboard" replace /> : 
  return children;
};
const AuthorizedGuard = ({ children }) => {
  // return (accessToken !== null) ?  children: children;
  return children
};

export default function Router() {
  return useRoutes([
    {
      path: '/dashboard',
      element: <DashboardLayout />,
      children: [
        { path: '', element: <AuthorizedGuard><Payments /></AuthorizedGuard> },
        { path: 'main', element: <AuthorizedGuard><DashboardApp /></AuthorizedGuard> },
        { path: 'app', element: <AuthorizedGuard><Payments /></AuthorizedGuard> },
        { path: 'user', element: <AuthorizedGuard><User /></AuthorizedGuard> },
        { path: 'products', element: <AuthorizedGuard><Products /> </AuthorizedGuard>},
        { path: 'blog', element: <Blog /> },
        { path: 'payments', element: <AuthorizedGuard><Payments /> </AuthorizedGuard>},
        { path: 'transactions', element: <AuthorizedGuard><Transactions /></AuthorizedGuard> },
        { path: 'settings', element: <AuthorizedGuard><Settings /> </AuthorizedGuard>},
        { path: 'kyc', element: <AuthorizedGuard><Profile /></AuthorizedGuard>},
        { path: 'profile', element: <AuthorizedGuard><Profile /></AuthorizedGuard> },
        { path: 'api/documentation', element: <APIDocumentationRedirect /> },
        { path: 'support', element: <Support /> },
        { path: 'confirmation', element: <AuthorizedGuard><Confirmation /></AuthorizedGuard> },
      ],
    },
    {      
      path: '/faq',
      element: <Faq />,
    },
    {      
      path: '/index',
      element: <Index />,
    },
    {
      path: '/login',
      element: <AuthGuard><Login /></AuthGuard>,
    },
    {
      path: '/register',
      element: <AuthGuard><Register /></AuthGuard>,
    },
    {
      path: '/provider-register',
      element: <AuthGuard><ProviderRegister /></AuthGuard>,
    },
    {
      path: '/email-confirmation',
      element: <AuthGuard><EmailConfirmation /></AuthGuard>,
    },
    {
      path: '/welcome-provider',
      element: <AuthGuard><WelcomeProvider /></AuthGuard>,
    },
    {
      path: '/password-reset',
      element: <AuthGuard><PasswordReset /></AuthGuard>,
    },
    {
      path: '/password-reset-confirm',
      element: <AuthGuard><PasswordResetOtp /></AuthGuard>,
    },
    {
      path: '/forgot-password-otp',
      element: <AuthGuard><PasswordResetOtp /></AuthGuard>,
    },
    { 
      path: 'api/documentation', 
      element: <APIDocumentationRedirect /> 
    },
    { 
      path: '/privacy', 
      element: <Privacy /> 
    },
    {
      path: '/terms-of-use',
      element: <Terms />
    },
    {
      path: '/complete/zoho/registration',
      element: <CompleteZohoRegistration />
    },
    {
      path: '/complete/provider/registration',
      element: <CompleteZohoRegistration />
    },
    {
      path: '/',
      element: <Index />,
     
    },
    {
      path: '/404',
      element: <NotFound />,
     
    },
    {
      path: '*',
      element: <Navigate to="/404" replace />,
    },
  ]);
}


export const APIDocumentationRedirect = () => {
  const navigate = useNavigate();
  useEffect(() => {  
    window.open('https://payments-doc.muda.tech/liquidity/intro', '_blank'); 
    navigate(-1);
  }, [navigate]);
  return null;
};
