import React , { useState, useEffect, useRef } from 'react';
import * as Yup from 'yup';
// form
import { useForm, Controller  } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { LoadingButton } from '@mui/lab';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { Stack, Grid, Button, TextField} from '@mui/material';
import Autocomplete from '@mui/material/Autocomplete';
import { RHFTextField,  FormProvider } from '../../../components/hook-form';
import { addAddress }   from '../../../services';



const Wallets = ({onComplete, closeModal}) => { 
  const [formData, setFormData] = useState({
                                              "address": "",
                                              "chain": ""
                                          });
                                          
   const [assetCodes, setAssetCodes] = useState([
                                                  {label: "Ethereum", value: "ETHEREUM"},
                                                  {label: "Celo", value: "CELO" },
                                                  {label: "Stellar", value: "STELLAR" }
                                                ]);
  const documentUpload  = useRef();
  useEffect(() => {
    const intiListing = async () => {
    }
    intiListing();
  })
  
  const WalletSchema = Yup.object().shape({
     address: Yup.string().required('Wallet address is required')
  });

  const methods = useForm({
                            resolver: yupResolver(WalletSchema),
                            formData
                          });

  const {
    handleSubmit,
    control,
    setValue,
    formState: { errors, isSubmitting },
  } = methods;

  const onSubmit = async () => {
      if(formData?.chain === undefined || formData?.chain === ""){
        toast.error(`Select chain to save`, {
          position: "top-right"
        });
        return; 
      }
      const postObj = await addAddress(formData);
      if(postObj.status === 200 || postObj.status === 200){
        toast.success(`${postObj.message}`, {
          position: "top-right"
        });
        await new Promise((r) => setTimeout(r, 850));
        closeModal();

      } else {

        toast.error(`${postObj.message}`, {
          position: "top-right"
        });
      }
      return'ok';
  };
  


  const handleChangeValue = async (thename, content) => {
    setFormData(prevFormData => ({ ...prevFormData, [thename]: content?.target?.value }));
  }

  const handleChangeValueIn = async (thename, content) => {
    setFormData(prevFormData => ({ ...prevFormData, [thename]: content}));
  }

  return (  
    <div> 
     
      <FormProvider methods={methods} onSubmit={handleSubmit(onSubmit)}>

       <p className="the-model-content-title text-center"> <b> New Wallet Address</b></p>
       <Grid container className="model-container-blocking">
      

        <Grid item xs={12} md={12} sx={{ mb: { xs: 1, sm: 1,  pt: 3} }} className="item-form-top-10">
          <Stack spacing={1} className="item-form-top-100">
             <p><b>Asset code</b></p>
             <Autocomplete 
                        options={assetCodes}
                        getOptionLabel={(option) => option.label}
                        onChange={(event, newValue) => {
                          handleChangeValueIn("chain", newValue?.value)
                        }}
                        renderInput={(params) => <TextField {...params}  placeholder="Chain Type" />}
                  />
                  
            </Stack>
       </Grid>

       <Stack spacing={3} className="full-width item-form-top-10">
        <p><b>Wallet Address</b></p>
        <Controller
            name="address"
            control={control}
            defaultValue=""
            render={({ field }) => (
              <TextField
                  {...field}
                  label={`Wallet Address`}
                  type="text"
                  error={!!errors.address}
                  helperText={errors.address?.message}
                  fullWidth
                  onChange={(e) => {
                    field.onChange(e);
                    handleChangeValue('address', e);
                  }}
                />
              )} />
      </Stack>
      <LoadingButton fullWidth className="mt-3" size="large" type="submit" variant="contained" loading={isSubmitting}>
        Save
      </LoadingButton>

      </Grid>
     </FormProvider>  
    </div>
  );
}
export default Wallets;