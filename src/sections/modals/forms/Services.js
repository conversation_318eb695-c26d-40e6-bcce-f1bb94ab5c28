import React , { useState, useEffect, useRef } from 'react';
import * as Yup from 'yup';
// form
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { LoadingButton } from '@mui/lab';


import Button from '@mui/material/Button';
import { Stack, Grid, OutlinedInput, TextField} from '@mui/material';
import Autocomplete from '@mui/material/Autocomplete';
import { FormProvider } from '../../../components/hook-form';
import { addAddress }   from '../../../services';

const Services = ({onComplete}) => { 
  const [formData, setFormData] = useState({
                                          "service_id": "",
                                          "asset_code": "",
                                          "asset_name": "",


                                          "service_name": "",
                                          "provider_name": "",
                                          "country": "",
                                          "chain": "",
                                          "company_id": "",
                                          "currency": "" 

                                       });
  const [documetnTypes, setDocumentTypes] = useState([
                                   {label: "Mpesa ", value: "mpesa" },
                                   {label: "MTN Uganda ", value: "mtn" },
                                ]);
  const documentUpload  = useRef();
  useEffect(() => {
    const intiListing = async () => {
        setDocumentTypes([
                          {label: "Mpesa ", value: "mpesa" },
                          {label: "MTN Uganda ", value: "mtn" },
                        ]);
    }
    intiListing();
  }, [setDocumentTypes])

  const WalletSchema = Yup.object().shape({
    service_id: Yup.number().required('Service is required'),
    asset_code: Yup.number().required('Asset code is required'),
    asset_name: Yup.string().required('Asset name is required'),
  });

  const methods = useForm({
                            resolver: yupResolver(WalletSchema),
                            formData
                          });
  const {
    handleSubmit,
    formState: { isSubmitting },
    control,
  } = methods;


  const onSubmit = async () => {
    const postObj = await addAddress(formData);
    if(postObj.status === 200 || postObj.status === 200){
      onComplete(true, postObj.message);
    } else {
      onComplete(false, postObj.message);
    }
    return'ok';
  };
  

  return (  
    <div> 
     
      <FormProvider methods={methods} onSubmit={handleSubmit(onSubmit)}>
       <p className="the-model-content-title text-center"> <b> New Service </b></p>

       <Grid container  className="model-container-blocking">
        <Grid item xs={12} md={12} sx={{ mb: { xs: 1, sm: 1,  pt: 3} }} className="item-form-top-10">
          <Stack spacing={1} className="item-form-top-100">
             <p><b>Service name</b></p>
            <OutlinedInput
                     id="firstname-login"
                     type="text"
                     value={formData.service_name}
                     onChange={(e) => setFormData("service_name", e.target.value)}
                     placeholder="Enter service name"
                     fullWidth
                />
            </Stack>
        </Grid>
        <Grid item xs={12} md={12} sx={{ mb: { xs: 1, sm: 1,  pt: 3} }} className="item-form-top-10">
          <Stack spacing={1} className="item-form-top-100">
             <p><b>Provider name</b></p>
            <OutlinedInput
                     id="firstname-login"
                     type="text"
                     value={formData.provider_name}
                     onChange={(e) => setFormData("provider_name", e.target.value)}
                     placeholder="Enter provider name"
                     fullWidth
                />
            </Stack>
        </Grid>


        <Grid item xs={12} md={6} sx={{ mb: { xs: 1, sm: 1}, pt: 2, pr: 2 }} className="item-form-top-10">
          <Stack spacing={1} className="item-form-top-100">
          <p><b>Currency</b></p>
          <Autocomplete options={documetnTypes}
                        getOptionLabel={(option) => option.label}
                        onChange={(event, newValue) => {
                          setFormData("currency", 1)
                        }}
                        renderInput={(params) => <TextField {...params} />}
                      />
          </Stack>
        </Grid>


        <Grid item xs={12} md={6} sx={{ mb: { xs: 1, sm: 1}, pt: 2 }} className="item-form-top-10">
          <Stack spacing={1} className="item-form-top-100">
          <p><b>Chain</b></p>
          <Autocomplete options={documetnTypes}
                        getOptionLabel={(option) => option.label}
                        onChange={(event, newValue) => {
                          setFormData("currency", 1)
                        }}
                        renderInput={(params) => <TextField {...params} />}
                      />
          </Stack>
        </Grid>

        <Grid item xs={12} md={12} sx={{ mb: { xs: 1, sm: 1}, pt: 2 }} className="item-form-top-10">
          <Stack spacing={1} className="item-form-top-100">
          <p><b>Country</b></p>
          <Autocomplete options={documetnTypes}
                        getOptionLabel={(option) => option.label}
                        onChange={(event, newValue) => {
                          setFormData("currency", 1)
                        }}
                        renderInput={(params) => <TextField {...params} />}
                      />
          </Stack>
        </Grid>

        <Grid item xs={12} md={12} sx={{ mb: { xs: 1, sm: 1}, pt: 2 }} className="item-form-top-10">
          <Stack spacing={1} className="item-form-top-100">
          <p><b>Company</b></p>
          <Autocomplete options={documetnTypes}
                        getOptionLabel={(option) => option.label}
                        onChange={(event, newValue) => {
                          setFormData("company_ids", 1)
                        }}
                        renderInput={(params) => <TextField {...params} />}
                      />
          </Stack>
        </Grid>

      </Grid>
      <Stack direction="column" justifyContent="right" alignItems="right"  className="full-width">
        <LoadingButton size="medium" type="submit" variant="contained" loading={isSubmitting}>
          Save
        </LoadingButton>
      </Stack>
     </FormProvider>  
    </div>
  );
}
export default Services;
