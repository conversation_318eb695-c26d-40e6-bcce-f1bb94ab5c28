import React , { useState, useEffect, useRef } from 'react';
import Button from '@mui/material/Button';
import { Stack, Grid, OutlinedInput, TextField} from '@mui/material';
import Autocomplete from '@mui/material/Autocomplete';

const Payment = () => { 
  const [documetnTypes, setDocumentTypes] = useState([
                                   {label: "Mpesa ", value: "mpesa" },
                                   {label: "MTN Uganda ", value: "mtn" },
                                ]);
  const documentUpload  = useRef();
  useEffect(() => {
    const intiListing = async () => {
        setDocumentTypes([
                                   {label: "Mpesa ", value: "mpesa" },
                                   {label: "MTN Uganda ", value: "mtn" },
                                ]);
    }
    intiListing();
  }, [setDocumentTypes])

  const selectingDocument = () => {
     documentUpload.current.click(); 
  }
  // const closeModal = () => onComplete({"status":  true, "message":  "" });
  

  return (
    
    <div>

       <p className="the-model-content-title"> <b> New Payment </b></p>
       


       <Grid container sx={{ mb: { xs: 1, sm: 1 } }}>
        <Grid item xs={12} md={12} className="item-form-top-10">
          <Stack spacing={1} className="item-form-top-100">
          <p>Select Service </p>
          <Autocomplete options={documetnTypes}
                        getOptionLabel={(option) => option.label}
                        onChange={(event, newValue) => {}}
                        renderInput={(params) => <TextField {...params} />}
                      />
          </Stack>
        </Grid>

        <Grid item xs={12} md={12} className="item-form-top-10">
          <Stack spacing={1} className="item-form-top-100">
          <p>Select payment currency  </p>
          <Autocomplete options={documetnTypes}
                        getOptionLabel={(option) => option.label}
                        onChange={(event, newValue) => {}}
                        renderInput={(params) => <TextField {...params} />}
                      />
          </Stack>
        </Grid>


        <Grid item xs={12} md={12} className="item-form-top-10">
          <Stack spacing={1} className="item-form-top-100">
          <p>Avialbale Providers  </p>
          <Autocomplete options={documetnTypes}
                        getOptionLabel={(option) => option.label}
                        onChange={(event, newValue) => {}}
                        renderInput={(params) => <TextField {...params} />}
                      />
          </Stack>
        </Grid>
        <Grid item xs={12} md={12} className="item-form-top-10">
          <Stack spacing={1} className="item-form-top-100">
             <p>Enter Amount</p>
            <OutlinedInput
                                id="firstname-login"
                                type="text"
                                value={""}
                                placeholder="Enter address"
                                fullWidth
                />
            </Stack>
                          
       </Grid>


        <Grid item xs={12} md={12} className="item-form-top-10">
          <Stack spacing={1} className="item-form-top-100">
             <p>Enter Account Number</p>
            <OutlinedInput
                                id="firstname-login"
                                type="text"
                                value={""}
                                placeholder="Enter address"
                                fullWidth
                />
            </Stack>
        </Grid>

        <Grid item sm={3} xs={4} className="item-form-top-101">
          <Stack direction="column" justifyContent="right" alignItems="right" xs={{ mb: { xs: -0.5, sm: 0.5 } }}  className="full-width">
            <Button variant="outlined" className="upload-section-button" onClick={selectingDocument}>Validate </Button>
         </Stack>
        </Grid>  

      </Grid>
                      
    </div>
  );
}
export default Payment;
