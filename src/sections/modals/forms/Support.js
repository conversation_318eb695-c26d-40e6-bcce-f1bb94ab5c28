import React , { useState, useEffect, useRef } from 'react';
import * as Yup from 'yup';
// form
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { LoadingButton } from '@mui/lab';


import Button from '@mui/material/Button';
import { Stack, Grid, OutlinedInput, TextField} from '@mui/material';
import Autocomplete from '@mui/material/Autocomplete';
import { FormProvider } from '../../../components/hook-form';
import { addAddress }   from '../../../services';

const Support = ({onComplete}) => { 
  const [formData, setFormData] = useState({
                                          "transaction_reference": "",
                                          "title": "",
                                          "details": "",
                                          "payment_address": ""
                                       });

  const [documetnTypes, setDocumentTypes] = useState([
                                   {label: "USDC (Stellar)", value: "1" },
                                   {label: "cUSD (Cello Dollar)", value: "2" },
                                   {label: "USDT-ERC20", value: "3" }
                                ]);
  const documentUpload  = useRef();
  useEffect(() => {
    const intiListing = async () => {
        setDocumentTypes([
                            {label: "USDC (Stellar)", value: "1" },
                            {label: "cUSD (Cello Dollar)", value: "2" },
                            {label: "USDT-ERC20", value: "3" }
                        ]);
    }
    intiListing();
  }, [setDocumentTypes])

  const WalletSchema = Yup.object().shape({
    transaction_reference: Yup.number().required('Service is required'),
    title: Yup.number().required('Asset code is required'),
    payment_address: Yup.string().required('Asset name is required'),
  });

  const methods = useForm({
                            resolver: yupResolver(WalletSchema),
                            formData
                          });
  const {
    handleSubmit,
    formState: { isSubmitting },
    control,
  } = methods;


  const onSubmit = async () => {
    const postObj = await addAddress(formData);
    if(postObj.status === 200 || postObj.status === 200){
      onComplete(true, postObj.message);
    } else {
      onComplete(false, postObj.message);
    }
    return'ok';
  };
  

  return (  
    <div> 
     
      <FormProvider methods={methods} onSubmit={handleSubmit(onSubmit)}>
       <p className="the-model-content-title text-center"> <b> Support Ticket </b></p>

       <Grid container  className="model-container-blocking">
        <Grid item xs={12} md={12} sx={{ mb: { xs: 1, sm: 1,  pt: 3} }} className="item-form-top-10">
          <Stack spacing={1} className="item-form-top-100">
            <p><b>Title</b></p>
            <OutlinedInput
                     id="firstname-login"
                     type="text"
                     value={formData.title}
                     onChange={(e) => setFormData("title", e.target.value)}
                     placeholder="Enter support ticket"
                     fullWidth
                />
            </Stack>
        </Grid>

        <Grid item xs={12} md={12} sx={{ mb: { xs: 1, sm: 1}, pt: 2 }} className="item-form-top-10">
          <Stack spacing={1} className="item-form-top-100">
            <p><b>Token</b></p>
            <Autocomplete options={documetnTypes}
                            getOptionLabel={(option) => option.label}
                            onChange={(event, newValue) => {
                            setFormData("token", 1)
                            }}
                            renderInput={(params) => <TextField {...params} />}
                        />
          </Stack>
        </Grid>

        <Grid item xs={12} md={12} sx={{ mb: { xs: 1, sm: 1}, pt: 2 }} className="item-form-top-10">
          <Stack spacing={1} className="item-form-top-100">
           <p><b>Transaction Reference/MEMO</b></p>
           <OutlinedInput
                     id="firstname-login"
                     type="text"
                     value={formData.transaction_reference}
                     onChange={(e) => setFormData("transaction_reference", e.target.value)}
                     placeholder="Enter transaction reference"
                     fullWidth
                />
          </Stack>
        </Grid>

        <Grid item xs={12} md={12} sx={{ mb: { xs: 1, sm: 1}, pt: 2 }} className="item-form-top-10">
          <Stack spacing={1} className="item-form-top-100">
           <p><b>Payment Address</b></p>
           <OutlinedInput
                     id="firstname-login"
                     type="text"
                     value={formData.payment_address}
                     onChange={(e) => setFormData("payment_address", e.target.value)}
                     placeholder="Enter payment address"
                     fullWidth
                />
          </Stack>
        </Grid>

        

        <Grid item xs={12} md={12} sx={{ mb: { xs: 1, sm: 1}, pt: 2 }} className="item-form-top-10">
          <Stack spacing={1} className="item-form-top-100">
           <p><b>Details</b></p>
           <TextField
                     id="details-login"
                     type="text"
                     value={formData.details}
                     onChange={(e) => setFormData("details", e.target.value)}
                     placeholder="Enter support details"
                     fullWidth
                />
          </Stack>
        </Grid>

      </Grid>
      <Stack direction="column" justifyContent="right" alignItems="right"  className="full-width">
        <LoadingButton size="medium" type="submit" variant="contained" loading={isSubmitting}>
          Send
        </LoadingButton>
      </Stack>
     </FormProvider>  
    </div>
  );
}
export default Support;
