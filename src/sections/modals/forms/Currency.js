import PropTypes from 'prop-types';
import React , { useState, useEffect, Fragment,useRef } from 'react';
import Button from '@mui/material/Button';
// third party
import * as Yup from 'yup';
import { Formik } from 'formik';
import useWallet from 'hooks/useWallet';
import useAuth from 'hooks/useAuth';
import useScriptRef from 'hooks/useScriptRef';
import { openSnackbar } from 'api/snackbar';

// project-imports
import MainCard from 'components/MainCard';
import SimpleBar from 'components/third-party/SimpleBar';
import CircularWithPath from 'components/@extended/progress/CircularWithPath';
import { FormHelperText, Box, Modal, Stack, Grid, Alert, Typography, OutlinedInput, Avatar, CircularProgress, TextField} from '@mui/material';
import Autocomplete from '@mui/material/Autocomplete';

import { EditOutlined, CloseOutlined, PlusCircleOutlined, DeleteOutlined, WarningFilled } from '@ant-design/icons';
import uploadIcon   from 'assets/images/main/upload.png';



import DocumentDetails from 'components/DocumentDetails';
import StatusLoader from 'components/StatusLoader';
import DateFormat from 'components/DateFormat';
import DefaultDocuments from 'components/DefaultDocuments';


// project imports
// import SortOptions from 'SortOptions';
// icons
const icons = {
  EditOutlined,
  CloseOutlined,
  PlusCircleOutlined,
  DeleteOutlined, 
  WarningFilled
};

const style = {
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  width: 400,
  bgcolor: 'background.paper',
  border: '2px solid #000',
  boxShadow: 24,
  pt: 2,
  px: 4,
  pb: 3,
};


const Payment = ({ dataContent, onComplete }) => { 

  const [open, setOpen] = useState(false);
  const [formSubmit, setFormSubmit] = useState(false);
  const [customFeildLabel, setCustomFeildLabel] = useState("");
  const [customPostField, setCustomPostField] = useState("");
  const [customFeildData, setCustomFeildData] = useState([]);
  const [documetnTypes, setDocumetnTypes] = useState([]);
  const [documentDetails, setDocumentDetails] = useState(dataContent?.content?.document);


  let customeFields = documentDetails?.custome_fields;
  const dataArray = JSON.parse(customeFields);
  const dataArray2 = JSON.parse(dataArray);
  const arrayLength = dataArray2.length;
  let customeFieldsArray = [];
  if(arrayLength > 0){
      customeFieldsArray = dataArray2 
  }
  const [customFeidsDetails, setCustomFeidsDetails] = useState(customeFieldsArray);


  const scriptedRef  = useScriptRef();
  const allowedFileTypes = ["jpg", "jpeg", "png", "gif","pdf","doc","docx"];
  const maxFileSizeInBytes = ********; // 10MB

  const documentUpload  = useRef();
  const { createWallet, getAccountCompanyWallet, getCountryList, getDoucmentTypesList, getDocumentCustomeFields }  = useAuth();
  const handleOpen = () => {
    setOpen(true);
  };
  useEffect(() => {
    const intiListing = async () => {
            let listDocTypes = await getDoucmentTypesList();
            let collectionOptions = [];  
            listDocTypes.map((item) => {
               collectionOptions.push({label: item.doc_type, value:  item.doc_code })  
            })  
            setDocumetnTypes(collectionOptions);

          }
    intiListing();

  }, [getDoucmentTypesList])

  const selectingDocument = () => {
     documentUpload.current.click(); 
  }
  const closeModal = () => onComplete({"status":  true, "message":  "" });
  const setCustomeFields = async (documentId) => {
    try{

        let customF = await getDocumentCustomeFields(documentId);
        if(customF.status === 200){

          let contentRessponse = [{"postField":customF?.data?.postField, "label": customF?.data?.label}]
          setCustomFeildData(customF?.data);
          return contentRessponse
        }
        return [{}]

      }catch(e){
          return [{}]
      }  
  }

  return (
    
    <div>
       <Formik
                    initialValues={{
                      document: '',
                      custome_fields: '',
                      docType: '',
                      custome_fields_label : '',
                      custome_fields_posttype : '',
                      submit: null
                    }}
                    validationSchema={Yup.object().shape({
                      document: Yup.string().required('document is required'),
                      docType:  Yup.string().required('document type is required')
                    })}
                    onSubmit={async (values, { setErrors, setStatus, setSubmitting, setValues, setTouched }) => {
                    }}
                >
                  {({ errors, handleBlur, handleChange, handleSubmit, isSubmitting, touched, values, setFieldValue }) => (
                    <form noValidate>
                      
                      <p className="the-model-content-title"> <b> Document details</b></p>
                      
                      <Grid container sx={{ mb: { xs: 1, sm: 1 } }}>
                        <Grid item xs={12} md={12} className="item-form-top-10">
                          <Stack spacing={1} className="item-form-top-100">
                            <p className="details-title">Name</p>
                            <p>{documentDetails?.doc_type}</p>
                          </Stack>
                        </Grid>
                        <Grid item xs={12} md={12} className="item-form-top-10">
                          <Stack spacing={1} className="item-form-top-100">
                            <p className="details-title">Company</p>
                            <p>{documentDetails?.company_name} (<b>Director</b> {documentDetails?.director_name})</p>

                          </Stack>
                        </Grid>

                        <Grid item xs={12} md={12} className="item-form-top-10">
                          <Stack spacing={1} className="item-form-top-100">
                            <p className="details-title">Custome Feilds</p>
                            <DocumentDetails customOptions={customFeidsDetails}/>
                          </Stack>
                        </Grid>


                        <Grid item xs={12} md={12} className="item-form-top-10">
                          <Stack spacing={1} className="item-form-top-100">
                            <p className="details-title">Status</p>
                            <span><StatusLoader status={documentDetails?.doc_status} /></span>
                          </Stack>
                        </Grid>


                        <Grid item xs={12} md={12} className="item-form-top-10">
                          <Stack spacing={1} className="item-form-top-100">
                            <p className="details-title">Last Assessed</p>
                            <DateFormat dateValue={documentDetails?.date_submitted} />
                          </Stack>
                        </Grid>
                      </Grid>


                  
                     </form>
                   )}
      </Formik>                      
    </div>
  );
}



Payment.propTypes = {
  openModel: PropTypes.bool,
  modalToggler: PropTypes.func,
  dataContent: PropTypes.object,
  theModelType: PropTypes.string
};

export default Payment;
