import PropTypes from 'prop-types';
import React, { useState, useEffect, Fragment, useRef } from 'react';
import Button from '@mui/material/Button';
// project-imports

import { Box, Modal, Stack, Grid, Typography, OutlinedInput, Avatar } from '@mui/material';
import Payment from  './forms/Payment';
import Wallets from  './forms/Wallets';
import Services from './forms/Services';
import Support  from './forms/Support';
import PaymentMethod from  './forms/PaymentMethod';




const style = {
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  width: 400,
  bgcolor: 'background.paper',
  border: '2px solid #000',
  boxShadow: 24,
  pt: 2,
  px: 4,
  pb: 3,
};



const MainModalView = ({ openModel, modalToggler, dataContent, theModelType, onComplete, updateComponent, onDelete }) => {

  const [open, setOpen] = useState(false);
  const handleOpen = () => {
    setOpen(true);
  };

  useEffect(() => {
    handleOpening(openModel);
  }, [openModel, modalToggler, dataContent, theModelType, updateComponent])

  const handleOpening = (opt) => {
    setOpen(opt);
  };


  const onContinue = async (typeOption) => {
    await updateComponent({type: typeOption})
  }


  const onContinueFinal = async (typeOption) => {

    await updateComponent(typeOption)
  }

  const closeModal = () => modalToggler(false);
  const openModal = () => modalToggler(true);

  return (
    <>
      {open && (
        <Modal
          open={open}
          onClose={closeModal}
          aria-labelledby="modal-customer-add-label"
          aria-describedby="modal-customer-add-description"
          sx={{
            '& .MuiPaper-root:focus': {
              outline: 'none'
            }
          }}
        >
              <div className="main-model-block">
                
                <Grid container sm={12} xs={12} className="text-right model-heading" orientation="horizontal">
                  <Grid item xs={(theModelType === "goToPay")? 6: 12} sm={(theModelType === "goToPay")? 10: 12} className="item-form-top-10" textAlign="right">
                      <Button className="closing-icon" onClick={() => closeModal()}>Close</Button>
                  </Grid>  
                </Grid>

                {(theModelType === "currency")? <Payment />:''}
                {(theModelType === "wallets")? <Wallets onComplete={onComplete} closeModal={closeModal}/>:''}
                {(theModelType === "payment_methods")? <PaymentMethod onComplete={onComplete} closeModal={closeModal}/>:''}
                {(theModelType === "support")? <Support onComplete={onComplete} closeModal={closeModal}/>:''}
                {(theModelType === "service")? <Services onComplete={onComplete} closeModal={closeModal}/>:''}
              
              </div>

        </Modal>
      )}
    </>
  );
}

MainModalView.propTypes = {
  openModel: PropTypes.bool,
  modalToggler: PropTypes.func,
  dataContent: PropTypes.object,
  theModelType: PropTypes.string
};

export default MainModalView;
