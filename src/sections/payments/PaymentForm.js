import React from 'react';
import { useLocation, useHistory } from 'react-router-dom';
import { Card, CardContent, Typography, Button, Grid } from '@mui/material';

const PaymentForm = () => {
  const location = useLocation();
  const history = useHistory();
  const { amount, accountNumber, selectedService, selectedCountry, selectedPaymentAsset, selectedProvider } = location.state;

  // Dummy data for demonstration purposes
  const dummyData = {
    amount: 50,
    accountNumber: '*********',
    selectedService: 'Airtime',
    selectedCountry: 'United States (USD)',
    selectedPaymentAsset: 'BTC',
    selectedProvider: {
      label: 'MTN Uganda',
      rate: '1 USD = 3800',
      fee: '30 UGX'
    }
  };

  // Destructure dummy data if actual data is not available
  const { amount: dummyAmount, accountNumber: dummyAccountNumber, selectedService: dummyService, selectedCountry: dummyCountry, selectedPaymentAsset: dummyPaymentAsset, selectedProvider: dummyProvider } = dummyData;

  const handlePay = () => {
    // Perform payment logic here
    //console.log('Payment processed successfully');
    // Redirect to payment success page or any other page
   // history.push('/payment-success');
  };

  return (
    <Card sx={{ width: '100%' }}>
      <CardContent>
        <Typography variant="h5" gutterBottom>
          Payment Confirmation
        </Typography>
        <Typography variant="body1">
          Amount: {amount || dummyAmount}
        </Typography>
        <Typography variant="body1">
          Account Number: {accountNumber || dummyAccountNumber}
        </Typography>
        <Typography variant="body1">
          Selected Service: {selectedService || dummyService}
        </Typography>
        <Typography variant="body1">
          Selected Country: {selectedCountry || dummyCountry}
        </Typography>
        <Typography variant="body1">
          Selected Payment Asset: {selectedPaymentAsset || dummyPaymentAsset}
        </Typography>
        <Typography variant="body1">
          Selected Provider: {selectedProvider ? `${selectedProvider.label} (${selectedProvider.rate}, fee ${selectedProvider.fee})` : `${dummyProvider.label} (${dummyProvider.rate}, fee ${dummyProvider.fee})`}
        </Typography>
        <Grid container justifyContent="flex-end" marginTop={2}>
          <Button variant="contained" color="primary" onClick={handlePay}>
            Pay Now
          </Button>
        </Grid>
      </CardContent>
    </Card>
  );
};

export default PaymentForm;
