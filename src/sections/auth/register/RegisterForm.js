import * as Yup from 'yup';
import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
// form
import { useForm, FormProvider } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
// @mui
import { FormControl, Stack, IconButton, InputAdornment, Typography } from '@mui/material';
import { LoadingButton } from '@mui/lab';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import PhoneInput from 'react-phone-input-2'
// import 'react-phone-input-2/lib/style.css'
import 'react-phone-input-2/lib/material.css'
import CurrencyAssetsInput from '../../../pages/RegisterCurrencyInput';
// components
import Iconify from '../../../components/Iconify';
import RHFTextField from '../../../components/hook-form/RHFTextField';
import { registerAccount, getServices, getAssets } from '../../../services';
// ----------------------------------------------------------------------

export default function RegisterForm() {
  const navigate = useNavigate();
  const [schema, setSchema] = useState(""); 
  const [assetsList, setAssetsList] = useState([]);
  const [services, setServices]     = useState([]); 

  
  const [currenciesList, setCurrenciesList] = useState([]); 
  const [showPassword, setShowPassword] = useState(false);

  const RegisterClientSchema = Yup.object().shape({
    fullName: Yup.string().required('Full name required'),
    email: Yup.string().email('Email must be a valid email address').required('Email is required'),
    password: Yup.string().required('Password is required'),
    phone_number: Yup.string().required('Phone number is required'),
  });

  const RegisterProviderSchema = Yup.object().shape({
    fullName: Yup.string().required('Full name required'),
    email: Yup.string().email('Email must be a valid email address').required('Email is required'),
    password: Yup.string().required('Password is required'),
    business_name: Yup.string().required('Business name required'),
    phone_number: Yup.string().required('Phone number is required'),
    payout_services: Yup.array()
      .of(Yup.string().required('Each payout service must be a valid string'))
      .min(1, 'At least one payout service is required') 
      .required('Payout services list is required'),
    payin_assets: Yup.array()
      .of(Yup.string().required('Each payin asset must be a valid string')) 
      .min(1, 'At least one payin asset is required') 
      .required('Payin assets list is required'),
  });

  const defaultValues = {
    fullName: '',
    email: '',
    password: '',
    phone_number: '',
    business_name: '',
    payout_currencies: [],
    payout_services: [], 
    payin_assets: [], 
    user_type: 'client',
    receive_currency: '',
    receive_service: '',
    send_asset: ''
  };

  const methods = useForm({
    resolver: yupResolver(schema),
    defaultValues,
  });

  useEffect(() => {

    const newSchema = async () => {

      const servicesCollection = await getServices();
      setCurrenciesList(servicesCollection);
      setServices(servicesCollection);
      const assetsCollection = await getAssets();
      setAssetsList(assetsCollection?.data);
    
    }
    newSchema();
  }, []);

  const {
    handleSubmit,
    control,
    setValue,
    getValues,
    reset,
    watch,
    formState: { errors, isSubmitting }
  } = methods;

  const watchedSendAsset = watch("send_asset", "");
  const watchedReceiveCurrency = watch("receive_currency", "");
  const watchedCurrencies = watch("payout_currencies", []);
  const watchedReceiveService = watch("receive_service", "");
  const watchedServices = watch("payout_services", []);

  const watchedAssests = watch("payin_assets", []);

  const changeUserType = () => {
    const newSchema = getValues('user_type') === 'client' ? RegisterClientSchema : RegisterProviderSchema;
    setSchema(newSchema);
    // reset(); 
    // reset(defaultValues); 
  }

  useEffect(() => {
    changeUserType();  
  },[getValues('user_type')]);

  const onSubmit = async (data) => {
    const registerObj = await registerAccount(data);
    if (registerObj.status === 200 || registerObj.status === 200) {
      toast.success(registerObj.message, { position: 'top-right' });
      await new Promise((r) => setTimeout(r, 800));
      navigate('/login', { replace: true });
    } else {
      toast.error(registerObj.message, { position: 'top-right' });
    }
  };

  const accountType = (optinVal) => {

    setValue('user_type', optinVal)
    setValue('business_name', '')
    setValue('payout_currencies', [])
    setValue('payout_services', [])
    setValue('payin_assets', [])
  }

  const removeSelectedAsset = (listCollection, asset, listCollectionName) => {
  if (window.confirm(`Confirm to remove ${asset}`)) {
    const assetList = listCollection.filter((item) => item !== asset);
    setValue(listCollectionName, assetList);
  }
};

  return (
    <FormProvider {...methods}>
      <ToastContainer />
      <form onSubmit={handleSubmit(onSubmit)}>
        <Stack spacing={3}>

          <section className="account_option">
            <button type="none" className={(getValues('user_type') === 'client')? 'active': ''} onClick={() => accountType('client')}>Client</button>
            <button type="none" className={(getValues('user_type') === 'provider')? 'active': ''} onClick={() => accountType('provider')}>Provider</button>
          </section> 
          {(getValues('user_type') === 'provider')? <RHFTextField name="business_name" label="Business name" />: ''}
          {(getValues('user_type') === 'provider')?
            <div className="form_content_assets selected-currency-items_0"> 
              <FormControl fullWidth sx={{ mt: 0, mb: 0 }}>
                <CurrencyAssetsInput
                              control={control}
                              currencyName="send_asset"
                              assetLabel="Select Payin Asset"
                              seletcedPayinAssetsName="payin_assets"
                              currencies={assetsList?.map(asset => ({
                                  label: `${asset.asset_code} (${asset.chain})`,
                                  value: asset.asset_code,
                                  logo: asset.logo
                              }))}
                              removeSelected={removeSelectedAsset}
                              selectedCurrencyList={watchedAssests}
                              selectedCurrency={watchedSendAsset}
                              handleCurrencyChange={(field, value) =>  {
                                const assetFList   = getValues('payin_assets');
                                const assetList  = assetFList.filter((asset) => asset === value);
                                if(assetList.length === 0){
                                  assetFList.push(value)
                                  setValue('payin_assets', assetFList)
                                }
                              }}
                              errors={errors} /> 
              </FormControl> 
              {errors.payin_assets && <Typography color="error" className="error_texting selected-currency-items_1">{errors?.payin_assets?.message}</Typography>}

              <FormControl fullWidth sx={{ mt: 2, mb: 0 }}>
                <CurrencyAssetsInput
                      control={control}
                      currencyName="receive_service"
                      assetLabel="Select Payout Services"
                      seletcedPayinAssetsName="payout_services"
                      currencies={services?.map(asset => ({
                        label: `${asset.service_name}`,
                        value: asset.service_name,
                        logo: asset.logo
                      }))}
                      removeSelected={removeSelectedAsset}
                      selectedCurrencyList={watchedServices}
                      selectedCurrency={watchedReceiveService}
                      handleCurrencyChange={(field, value) => { 

                        const assetFList   = getValues('payout_services');
                        const assetList  = assetFList.filter((asset) => asset === value);
                        if(assetList.length === 0){
                           assetFList.push(value)
                           setValue('payout_services', assetFList)

                        }
                      }}
                      errors={errors}
                    />
              </FormControl>
              {errors.payout_services && <Typography color="error" className="error_texting selected-currency-items_1">{errors?.payout_services?.message}</Typography>}

          </div> : ''}

          <RHFTextField name="fullName" label="Full name" />
          <RHFTextField name="email" label="Email address" />
          <PhoneInput country={'ug'} value={getValues('phone_number')} onChange={(phone) => setValue("phone_number", phone)}  fullWidth className="phone-field"/>
          {errors.phone_number && <Typography color="error" className="error_texting">{errors.phone_number.message}</Typography>}

          <RHFTextField
            name="password"
            label="Password"
            type={showPassword ? 'text' : 'password'}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton edge="end" onClick={() => setShowPassword(!showPassword)}>
                    <Iconify icon={showPassword ? 'eva:eye-fill' : 'eva:eye-off-fill'} />
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />
        </Stack>
        <LoadingButton fullWidth size="large" type="submit" variant="contained" loading={isSubmitting} sx={{ mt: 3 }}>
          Register
        </LoadingButton>

      </form>
    </FormProvider>
  );
}
