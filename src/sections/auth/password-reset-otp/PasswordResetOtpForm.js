import * as Yup from 'yup';
import { useState } from 'react';
import { useNavigate, Link as RouterLink } from 'react-router-dom';

// form
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
// @mui
import { Link, Stack, IconButton, InputAdornment } from '@mui/material';
import { LoadingButton } from '@mui/lab';
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

// components
import Iconify from '../../../components/Iconify';
import { FormProvider, RHFTextField } from '../../../components/hook-form';
import { resetPasswordComplete, resendToken } from '../../../services';

// ----------------------------------------------------------------------
export default function PasswordResetOtpForm() {
  const navigate = useNavigate();

  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [resetEmail] = useState(localStorage.getItem('PasswordResetEmail'));

  const LoginSchema = Yup.object().shape({
    password: Yup.string().required('Password is required'),
    confirmPassword: Yup.string().oneOf([Yup.ref('password'), null], 'Passwords must match').required('Confirm password is required'),
    otp: Yup.string().required('OTP is required'),
  });

  const defaultValues = {
    email: resetEmail,
    password: '',
    confirmPassword: '',
    otp: ''
  };

  const methods = useForm({
    resolver: yupResolver(LoginSchema),
    defaultValues
  });

  const {
    handleSubmit,
    formState: { isSubmitting },
  } = methods;

  const onSubmit = async (data) => {
    const registerObj = await resetPasswordComplete(data);
    if (registerObj.status === 200 || registerObj.status === 200) {
      toast.success(registerObj.message, { position: "top-right" });
      navigate('/login', { replace: true });
    } else {
      toast.error(registerObj.message, { position: "top-right" });
    }
  };

  const getNewToken = async () => {
    const registerObj = await resendToken({ email: resetEmail });
    if (registerObj.status === 200 || registerObj.status === 200) {
      localStorage.setItem('PasswordResetEmail', resetEmail);
      toast.success(registerObj.message, { position: "top-right" });
    } else {
      toast.error(registerObj.message, { position: "top-right" });
    }
  };

  return (
    <FormProvider methods={methods} onSubmit={handleSubmit(onSubmit)}>
      <Stack spacing={3}>
        <RHFTextField name="otp" label="Enter OTP Code" />
        <RHFTextField
          name="password"
          label="Password"
          type={showPassword ? 'text' : 'password'}
          InputProps={{
            endAdornment: (
              <InputAdornment position="end">
                <IconButton onClick={() => setShowPassword(!showPassword)} edge="end">
                  <Iconify icon={showPassword ? 'eva:eye-fill' : 'eva:eye-off-fill'} />
                </IconButton>
              </InputAdornment>
            ),
          }}
        />
        <RHFTextField
          name="confirmPassword"
          label="Confirm Password"
          type={showConfirmPassword ? 'text' : 'password'}
          InputProps={{
            endAdornment: (
              <InputAdornment position="end">
                <IconButton onClick={() => setShowConfirmPassword(!showConfirmPassword)} edge="end">
                  <Iconify icon={showConfirmPassword ? 'eva:eye-fill' : 'eva:eye-off-fill'} />
                </IconButton>
              </InputAdornment>
            ),
          }}
        />
      </Stack>

      <Stack direction="row" alignItems="center" sx={{ my: 2 }}>
        <Link variant="subtitle2" underline="hover" onClick={getNewToken}>
          Resend for New OTP
        </Link>
      </Stack>

      <LoadingButton fullWidth size="large" type="submit" variant="contained" loading={isSubmitting}>
        Reset Password
      </LoadingButton>

      <Stack direction="row" alignItems="center" sx={{ my: 2 }}>
        <Link variant="subtitle2" underline="hover" component={RouterLink} to="/">
          Back to Login
        </Link>
      </Stack>
    </FormProvider>
  );
}
