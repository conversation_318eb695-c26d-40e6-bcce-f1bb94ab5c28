import * as Yup from 'yup';
import { useState } from 'react';
import { useNavigate, Link as RouterLink } from 'react-router-dom';

// form
import { useForm, FormProvider } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
// @mui
import { Link, Stack } from '@mui/material';
import { LoadingButton } from '@mui/lab';
import { ToastContainer, toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

// components
import RHFTextField from '../../../components/hook-form/RHFTextField';
import { passwordRestOtpRequest } from '../../../services';

// ----------------------------------------------------------------------

export default function PasswordResetForm() {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({ email: '' });

  const LoginSchema = Yup.object().shape({
    email: Yup.string().email('Email must be a valid email address').required('Email is required'),
  });

  const methods = useForm({
    resolver: yupResolver(LoginSchema),
    defaultValues: formData,
  });

  const {
    handleSubmit,
    formState: { isSubmitting },
  } = methods;

  const onSubmit = async (data) => {
    const registerObj = await passwordRestOtpRequest(data);
    if (registerObj.status === 200 || registerObj.status === 200) {
      localStorage.setItem('PasswordResetEmail', data.email);
      toast.success(registerObj.message, { position: 'top-right' });
      navigate('/forgot-password-otp', { replace: true });
    } else {
      toast.error(registerObj.message, { position: 'top-right' });
    }
  };

  return (
    <FormProvider {...methods}>
      <ToastContainer />
      <form onSubmit={handleSubmit(onSubmit)}>
        <Stack spacing={3}>
          <RHFTextField name="email" label="Email address" />
        </Stack>

        <LoadingButton fullWidth size="large" type="submit" variant="contained" loading={isSubmitting} className="mt-25">
          Send Request
        </LoadingButton>

        <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ my: 2 }}>
          <Link variant="subtitle2" underline="hover" component={RouterLink} to="/">
            Back to Login
          </Link>
        </Stack>
      </form>
    </FormProvider>
  );
}
