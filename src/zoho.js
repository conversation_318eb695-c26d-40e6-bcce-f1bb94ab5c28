import axios from 'axios';
import { BASE_URL } from './api';

// Base URL for your API
 // const BASE_URL = 'http://localhost:8031/';

// Function to get the auth token from localStorage
const getAuthToken = () => {
    const userObject = localStorage.getItem('serviceToken');
    if (userObject) {
        return userObject;
    }
    return "";
}

// Axios instance to attach base URL and headers for all requests
const axiosInstance = axios.create({
    baseURL: BASE_URL,
    headers: {
        'Content-Type': 'application/json',
    },
});

axiosInstance.interceptors.request.use(
    async (config) => {
        const token = getAuthToken();
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
    },
    (error) => {
        console.error('Interceptor error:', error);
        throw new Error(`Request interceptor error: ${error.message}`);
    }
);

axiosInstance.interceptors.response.use(
    response => response,
    error => {
        if (error.response && error.response.status === 401) {
          window.dispatchEvent(new CustomEvent("unauthorized"));
        }
        return []
    }
);

// Function to make GET request
export const get = async (path) => {
    try {
        const response = await axiosInstance.get(path);
        return response.data;
    } catch (error) {
        console.error('GET request error:', error);
        return []
    }
};

// Function to make POST request
export const post = async (path, data) => {
    try {
        const response = await axiosInstance.post(path, data);
        return response.data;
    } catch (error) {
        console.error('POST request error:', error);
        return []
    }
};




