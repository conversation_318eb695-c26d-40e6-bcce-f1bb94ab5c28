import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>er, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Dialog, DialogTitle, DialogContent, DialogActions, TextField, Select, MenuItem, IconButton } from '@mui/material';
import 'react-phone-input-2/lib/material.css'
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';   
import { addPaymentMethod } from '../../services';
import Iconify from '../Iconify';

const RecieveCurrency = ({ isOpen, onClose, onSuccess, data }) => {

      const [username, setUsername] = useState('');
      const [password, setPassword] = useState('');
      const [schema, setSchema] = useState(""); 
      const onSubmit = async (itemData) => {
        try {
          onSuccess(itemData);
        } catch (error) {
           toast.error("Error saving address");
        }
      };
  

  return (
        <Dialog open={isOpen} onClose={onClose} maxWidth="md" fullWidth className="currency-listing">
          <DialogTitle  className="pop-title">
            <b>Select Recieve Currency</b>
            <IconButton onClick={() => {onClose()  }}>
              <Iconify icon="eva:close-outline" color="red" />
            </IconButton>
          </DialogTitle>
          <DialogContent>

            {data.dataList.map((item, index) => (
              <div key={index} className="currency-item_flex" 
                   role="button" tabIndex={0} onClick={() => {onSubmit(item)}}
                   onKeyDown={(e) => { if (e.key === "Enter" || e.key === " ") onSubmit(item); }}>
                <div className="currency-item__icon">
                  <img  src={`/static/flags/${item?.currency?.toLowerCase()}.png`} alt={'C'} className="flags-extra" />
                </div>
                <div className="currency-item__details">
                  <Typography variant="p">{item?.currency}</Typography>
                </div>
              </div>
            ))}
          </DialogContent>
        </Dialog>
  );
};

export default RecieveCurrency;
