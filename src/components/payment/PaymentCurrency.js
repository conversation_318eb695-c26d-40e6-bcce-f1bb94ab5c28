import { useState, useEffect } from 'react';
import * as Yup from 'yup';
import { useF<PERSON>, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { But<PERSON>, Container, Stack, Typography, Dialog, DialogTitle, DialogContent, DialogActions, TextField, Select, MenuItem, IconButton } from '@mui/material';
import PhoneInput from 'react-phone-input-2'
// import 'react-phone-input-2/lib/style.css'
import 'react-phone-input-2/lib/material.css'
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';                                                                                         
import { post, get } from '../../api';
import { addAddress } from '../../services';
import { FormProvider } from '../hook-form';
import Iconify from '../Iconify';

const PaymentCurrency = ({ isOpen, onClose, onSuccess, data }) => {
const onSubmit = async (itemData) => {
    try {
      onSuccess(itemData);
    } catch (error) {
       toast.error("Error saving address");
    }
};

return (
    <Dialog open={isOpen} onClose={onClose} maxWidth="sm" fullWidth className="currency-listing">
      <DialogTitle  className="pop-title">
        <b>Select Payment Asset</b>
        <IconButton onClick={() => {onClose()  }}>
          <Iconify icon="eva:close-outline" color="red" />
        </IconButton>
      </DialogTitle>
      <DialogContent>
            {data.dataList.map((item, index) => (
              <div key={index} className="currency-item_flex" role="button" tabIndex={0} onClick={() => {onSubmit(item)}}  onKeyDown={(e) => { if (e.key === "Enter" || e.key === " ") onSubmit(item); }} >
                <div className="currency-item__icon">
                  <img  src={`/static/flags/${item?.asset_code?.toLowerCase()}.png`} alt={'C'} className="flags-extra" />
                </div>
                <div className="currency-item__details">
                  <Typography variant="p">{item?.asset_name}</Typography>
                </div>
                
              </div>
            ))}
      </DialogContent>
    </Dialog>
  );
};

export default PaymentCurrency;
