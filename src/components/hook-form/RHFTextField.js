import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
// form
import { useFormContext, Controller } from 'react-hook-form';
// @mui
import { TextField } from '@mui/material';

// ----------------------------------------------------------------------

RHFTextField.propTypes = {
  name: PropTypes.string.isRequired,
  handleChange: PropTypes.func,
  dataValue: PropTypes.string,
};

export default function RHFTextField({ name, handleChange, ...other }) {
  const { control } = useFormContext();

  return (
    <Controller
      name={name}
      control={control}
      render={({ field, fieldState: { error } }) => (
        <TextField
          {...field}
          fullWidth
          error={!!error}
          helperText={error ? error.message : ''}
          autoComplete="off"
          onChange={(e) => {
            field.onChange(e);
            if (handleChange) {
              handleChange(name, e.target.value);
            }
          }}
          {...other}
        />
      )}
    />
  );
}
