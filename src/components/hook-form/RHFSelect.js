nimport React, { useEffect } from 'react';
import PropTypes from 'prop-types';
// form
import { useFormContext, Controller, useForm } from 'react-hook-form';
// @mui
import { TextField } from '@mui/material';

// ----------------------------------------------------------------------

RHFSelect.propTypes = {
  name: PropTypes.string,
  handleChange: PropTypes.func,
  dataValue: PropTypes.string,
  options: PropTypes.obj
};

export default function RHFSelect({ name, dataValue, handleChange, options, ...other }) {
  const { control } = useFormContext(); 
  const { reset } = useForm();
   
  const takeValue = async(inputName, eBody, fieldValue) => {
    handleChange(inputName, eBody, fieldValue)
  }

  const handleReset = () => {
    reset();
  };

  useEffect(() => {
    handleReset();
  });
  
  return (
    <Controller
      name={name}
      value={dataValue}
      control={control}
      render={({ field, fieldState: { error, onChange } }) => (
        <> 
          <TextField
            {...field}
            fullWidth
            value={field?.value}
            error={!!error}
            helperText={error?.message}
            autoComplete='off'
            onMouseOut={(e) => { 
                                takeValue(name, e, field?.value) 
                             }}
            {...other}
          />
        </>  
      )}
    />
  );
}
