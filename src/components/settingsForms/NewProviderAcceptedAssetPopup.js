import { useState, useEffect } from 'react';
import * as Yup from 'yup';
import { useF<PERSON>, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { But<PERSON>, Container, Stack, Typography, Dialog, DialogTitle, DialogContent, DialogActions, TextField, Select, MenuItem, IconButton } from '@mui/material';
import PhoneInput from 'react-phone-input-2'
// import 'react-phone-input-2/lib/style.css'
import 'react-phone-input-2/lib/material.css'
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';                                                                                         
import { post, get } from '../../api';
import { addAddress, getChains, getAssets, updateproviderAddress } from '../../services';
import { FormProvider } from '../hook-form';
import Iconify from '../Iconify';


const chainOptions = ['STELLAR', 'CELO', 'TRON'];
const NewProviderAcceptedAssetPopup = ({ isOpen, onClose, onSuccess, data }) => {

    const [username, setUsername] = useState('');
    const [password, setPassword] = useState('');
    const [addresses, setAddresses] = useState([]);
    const [chains, setChains] = useState([]);
    const [assets, setAssets] = useState([]);
    const [open, setOpen] = useState(false);
    const [walletDelete, setWalletDelete] = useState(false);
    const [walletDeleteDetails, setWalletDeleteDetails] = useState({});

    useEffect(() => {
      fetchAddresses();
      fetchingChain();
    }, []);
  
    const fetchAddresses = async () => {
      try {
        const response = await get("accounts/getAddresses");
        setAddresses(response.data || []);
      } catch (error) {
        toast.error("Failed to fetch addresses");
      }
    };

    const fetchingChain = async () => {
      try {
        const response = await getChains();
        setChains(response.data || []);

        const theassets = await getAssets();
        setAssets(theassets.data || []);
      } catch (error) {
        toast.error("Failed to fetch addresses");
      }
    };

  
    const WalletSchema = Yup.object().shape({
                address: Yup.string().required("Wallet address is required"),
                memo:    Yup.string().required("Wallet memo is required"),
                chain:   Yup.string().required("Select a chain"),
                asset:   Yup.string().required("Select a asset")
            });
  
    const methods = useForm({
      resolver: yupResolver(WalletSchema),
      defaultValues: {
        address: "",
        memo: "",
        asset: "",
        chain: (data?.payCurrency !== "" && data?.payCurrency !== undefined)? data.payCurrency : "",
      },
    });
  
    const {
      handleSubmit,
      control,
      reset,
      formState: { errors, isSubmitting },
    } = methods;
  
    const onSubmit = async (data) => {
      try {
        
          const postObj = await updateproviderAddress(data);
          if (postObj.status === 200 || postObj.status === 200) {
            toast.success(`${postObj.message}`);
            fetchAddresses();
            setOpen(false);
            reset();
            onSuccess();
          } else {
            toast.error(`${postObj.message}`);
          }

      } catch (error) {
         toast.error("Error saving address");
      }
  };

  return (
    <Dialog open={isOpen} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle  className="pop-title">
        <b>New Accepted Asset</b>
        <IconButton onClick={() => {onClose()  }}>
          <Iconify icon="eva:close-outline" color="red" />
        </IconButton>
      </DialogTitle>

      <FormProvider {...methods} onSubmit={handleSubmit(onSubmit)}>
        <DialogContent>
            
            <div className="form-block">
              <Typography mt={3} mb={1} variant="p" className="form-block-title">Asset Label</Typography> 
              <Controller
                name="memo"
                style={{ marginTop: 0 }}
                control={control}
                render={({ field }) => (
                        <TextField
                            {...field}
                            label=""
                            type="text"
                            fullWidth
                        />
                )} />
              {errors.memo && <Typography color="error">{errors.memo.message}</Typography>}
            </div>

            </DialogContent>

            <DialogActions>
              <Button onClick={() => onClose()}>Cancel</Button>
              <Button variant="contained" type="submit" disabled={isSubmitting}>
                Save
              </Button>
            </DialogActions>
        </FormProvider>
    </Dialog>
  );
};

export default NewProviderAcceptedAssetPopup;
