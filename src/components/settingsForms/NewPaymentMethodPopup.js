import { useState, useEffect } from 'react';
import * as Yup from 'yup';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { Button, Container, Stack, Typography, Dialog, DialogTitle, DialogContent, DialogActions, TextField, Select, MenuItem, IconButton } from '@mui/material';
import PhoneInput from 'react-phone-input-2'
// import 'react-phone-input-2/lib/style.css'
import 'react-phone-input-2/lib/material.css'
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';                                                                                         
import { post, get } from '../../api';

import { addPaymentMethod, getAssets, verifyPaymentMethod} from '../../services';
import { FormProvider } from '../hook-form';
import Iconify from '../Iconify';

const NewPaymentMethodPopup = ({ isOpen, onClose, onSuccess, data }) => {

      const [username, setUsername] = useState('');
      const [password, setPassword] = useState('');
      const [schema, setSchema] = useState(""); 
      const [networkSwitch, setNetworkSwitch] = useState(0);  
      const [paymentMethods, setPaymentMethods] = useState([]);
      const [assetsCollection, setAssetsCollection] = useState([]);
      const [bankDetails, setBankDetails] = useState([]);
      const [paymentTypes, setPaymentTypes] = useState([]);
      const [paymentTypesNetorks, setPaymentTypesNetorks] = useState([
        { currency: 'UGX', country_code: 'UG', country: 'Uganda', phone_code: '+256', networks: ['MTN', 'Airtel'] },
        { currency: 'KES', country_code: 'KE', country: 'Kenya', phone_code: '+254', networks: ['MPESA'] },
        { currency: 'ZAR', country_code: 'ZA', country: 'South Africa', phone_code: '+27', networks: [] },
        { currency: 'TZS', country_code: 'TZ', country: 'Tanzania', phone_code: '+255', networks: [] }
      ]);
      const [paymentTypeOptions, setpaymentTypeOptions] = useState([
                                                                        { value: 'mobile_money', label: 'Mobile Money' },
                                                                        { value: 'bank', label: 'Bank' },
                                                                  ]); 
      const [loadingBankStatus, setLoadingBankStatus] = useState(false);
      
     
      const [formData, setFormData] = useState({
                                                  type: '',
                                                  currency: "",
                                                  country_code: '',
                                                  network: '',
                                                  phone_number: '',
                                                  account_name: '',
                                                  bank_name: '',
                                                  bank_code: '',
                                                  sort_code: '', 
                                                  swift_code : '', 
                                                  account_number: ''
                                              });
                                                 
      useEffect(() => {
        fetchPaymentMethods();
        fetchAssets();


      }, [isOpen, data?.currency]);

      useEffect(() => {

        const currencyData = (data?.currency !== undefined && data?.currency !== "")? data.currency : ""
        setValue('currency',  currencyData)
        const currency = 'currency';
        setFormData({ ...formData, currency: currencyData});

      }, [data?.currency]);


      useEffect(() => {
        fetchBankDetails()
      }, [formData.currency, formData.type]);

      const WalletSchema = Yup.object().shape({
                                  type: Yup.string().required("Type is required"),
                                  country_code: Yup.string().required("Country is required"),
                                  currency: Yup.string().required("Currency is required"),
                                  // network: Yup.string().required("Network is required"),
                                  phone_number: Yup.string().required("Phone number is required"),
                                  account_name: Yup.string().required("Account name is required")
                              });
      
      const BankSchema = Yup.object().shape({
                                  type: Yup.string().required("Type is required"),
                                  country_code: Yup.string().required("Country is required"),
                                  currency: Yup.string().required("Currency is required"),
                                  account_name: Yup.string().required("Account name is required"),
                                  bank_code: Yup.string().required("Bank is required"),
                                  // sort_code: Yup.string().required("Bank sort code is required"),
                                  // swift_code: Yup.string().required("Bank swift code is required"),
                                  account_number: Yup.string().required("Bank account number is required")
                              });                        
        
      const methods = useForm({
          resolver: yupResolver(schema),
          defaultValues: formData,
      });

      const {
                handleSubmit,
                control,
                reset,
                setValue,
                formState: { errors, isSubmitting },
            } = methods;


      useEffect(() => {
          const newSchema = formData?.type === 'bank' ? BankSchema : WalletSchema;
          setSchema(newSchema);
          methods.reset(formData);
      }, [formData?.type]);
  
      const fetchPaymentMethods = async () => {
          const response = await get(`accounts/getPaymentMethods`);
          setPaymentMethods(response || []);
      };


      const fetchBankDetails = async () => {

         setLoadingBankStatus(true)
         setBankDetails([]);
         
         const response = await get(`accounts/banks?currency=${formData.currency}`);
         if(response?.length >= 1){
          const data = response.filter((item) => item.currency === formData.currency.toUpperCase());
           setBankDetails(data);
         }

         setLoadingBankStatus(false)
         
      };

      

      useEffect(() => {
        const getAccountDetails = async () => {

            const accNumber  = (formData.type === 'mobile_money')? formData?.phone_number: formData?.account_number
            const accType = (formData.type === 'mobile_money')? 'mobile': formData.type
            const bankDetails = await verifyPaymentMethod({

                                              provider_type:accType,
                                              bank_code: formData?.bank_code,
                                              account_number: accNumber,
                                              currency:formData?.currency
                                          })
            // methods.setValue("account_name", "");
        }
        getAccountDetails()
    }, [formData.currency, formData.type, formData.account_number, formData.phone_number]);


      
      const fetchAssets = async () => {

        const response = await getAssets();
        if(response?.status === 200){

          const responseData     = response?.data?.filter((item) => item.type === 'fiat');

          setAssetsCollection(responseData || []);
          setPaymentTypes(responseData || []);

          // if currency is set 
          if(data?.currency){
            const countrySelection =  responseData.filter((item) => item.asset_code	=== data?.currency)[0];
            const countryCode      = 'country_code';
            setFormData((formData) => ({ ...formData, [countryCode]: countrySelection?.country_code?? "" }));
  
            const currencyCode = 'currency';
            setFormData((formData) => ({ ...formData, [currencyCode]: data?.currency ?? '' }));
          }


        } 

      };
  
      const handleChange = (e) => {
          setValue(e.target.name, e.target.value)
          setFormData({ ...formData, [e.target.name]: e.target.value });
      };
  
      const handlePhoneChange = (name, value) => {
          setValue(name, value)
          setFormData({ ...formData, [name]: value });
      };
  
      const handleTypeChange = (e) => {
        const { name, value } = e.target;
      
        setValue(name, value);
      
        if (name === "country_code") {
          const paymentTypeIndex = paymentTypes.findIndex(item =>
            Object.values(item).includes(value)
          );
          if (paymentTypeIndex >= 0) {
            setNetworkSwitch(paymentTypeIndex);
            setValue("network", "");
          }
        }
      
        // Always update form data
        setFormData(prev => ({ ...prev, [name]: value }));
      
        // If type is 'bank', reset account_name
        if (name === "type" && value === "bank") {
          // setFormData(prev => ({ ...prev, account_name: "" }));
        }
      };
      

      const handleCurrencyTypeChange = (e) => {
        setValue(e.target.name, e.target.value)
        setFormData({ ...formData, [e.target.name]: e.target.value });
    };
  
      
      const handleCurrencyChange = (e) => {
          
          setValue(e.target.name, e.target.value)
          const selectedCurrency = e.target.value;
          setFormData({ ...formData, [e.target.name]: e.target.value });
          
      };


      const handleBankChange = (e) => {
          setValue(e.target.name, e.target.value)
          setFormData((formData) => ({ ...formData, [e.target.name]: e.target.value }));

          const bankCodeValue = bankDetails.filter(pt => pt.bank_code === e.target.value)[0];
          const theBankName= 'bank_name'
          setValue(theBankName, bankCodeValue.name)
          setFormData((formData) => ({ ...formData, [theBankName]: bankCodeValue.name }));
      }
  

      const onSubmit = async (data) => {
          try {
  
              if(data.type === 'mobile_money'){
                
                data.phone_number = data?.phone_number.trim();
                data.phone_number = data?.phone_number.startsWith('+') ? data?.phone_number : `+${data?.phone_number}`;
              } 

              const postObj = await addPaymentMethod(data);
              if (postObj.status === 200 || postObj.status === 200) {
                
                toast.success(`${postObj.message}`);
                fetchPaymentMethods();
                reset();
                onClose();
                onSuccess();
              } else {
                toast.error(`${postObj.message}`);
              }
          } catch (error) {
  
             toast.error("Error saving address");
          }
  };
  
  return (
            <Dialog open={isOpen} onClose={onClose} maxWidth="sm" fullWidth>
              
                <DialogTitle  className="pop-title">
                  <b>New payment method</b>
                  <IconButton onClick={() => onClose}>
                    <Iconify icon="eva:close-outline" color="red" />
                  </IconButton>
                </DialogTitle>
            
                <FormProvider {...methods} onSubmit={handleSubmit(onSubmit)}>
                    <DialogContent>

                    <div className="form-block">
                      <Typography mt={3} mb={1} variant="p" className="form-block-title">
                        Type 
                      </Typography>   
                      <Select name="type" value={formData.type} 
                                onChange={handleTypeChange} fullWidth style={{ marginBottom: 1 }}>
                            {paymentTypeOptions.length > 1 && paymentTypeOptions.map(pt => (
                                <MenuItem key={pt.value} value={pt.value}>{pt.label}</MenuItem>
                            ))}
                      </Select>
                      {errors.type && <Typography color="error">{errors.type.message}</Typography>} 
                    </div>  

                    <div className="form-block">
                      <Typography mt={3} mb={1} variant="p" className="form-block-title">Country {formData.country_code}</Typography>   
                      <Select name="country_code" value={formData.country_code} onChange={handleTypeChange} fullWidth style={{ marginBottom: 1 }}>
                        {assetsCollection.length > 1 && assetsCollection.map(pt => (
                          <MenuItem key={pt?.country_code} value={pt?.country_code}>{pt.country}</MenuItem>
                        ))}
                      </Select>
                      {errors.country_code && <Typography color="error">{errors.country_code.message}</Typography>}
                    </div>


                    <div className="form-block">
                      <Typography mt={3} mb={1} variant="p" className="form-block-title" >Currency</Typography>
                      <Select name="currency" value={formData.currency} onChange={handleCurrencyTypeChange} fullWidth style={{ marginBottom: 1 }}>
                          {paymentTypes.length > 1 && paymentTypes.map(pt => (
                             <MenuItem key={pt.asset_code} value={pt.asset_code}>{pt.asset_code}</MenuItem>
                          ))}
                      </Select>
                      {errors.currency && <Typography color="error">{errors.currency.message}</Typography>}
                    </div>

                    {(formData?.type === 'bank') && (
                        <div className="form-block">
                          <Typography mt={3} mb={1} variant="p" className="form-block-title">
                            Bank Name  
                            <i className="red-error-text">{(formData.currency !== "" && !loadingBankStatus && bankDetails.length === 0) && ` (No banks for selected currency ${formData.currency})`}</i>
                          </Typography>   
                          <Select name="bank_code" value={formData.bank_code} onChange={handleBankChange} fullWidth style={{ marginBottom: 1 }}>
                            {bankDetails.length > 0 && bankDetails.map(pt => (
                              <MenuItem key={pt.bank_code} value={pt.bank_code}> {pt.name} </MenuItem>
                            ))}
                          </Select>
                          {errors.bank_code && <Typography color="error">{errors.bank_code.message}</Typography>}
                        </div>
                    )}

                    {(formData?.type === 'mobile_money')?
                     
                     <div>   

                          
                          {/* <div className="form-block  hidden ahow_hide">
                            <Typography mt={3} mb={1} variant="p" className="form-block-title">Network</Typography>   
                            <Select name="network" value={formData.network} onChange={handleCurrencyChange} fullWidth style={{ marginBottom: 1 }}>
                            {paymentTypesNetorks[networkSwitch].networks.map(pt => (
                                <MenuItem key={pt} value={pt}>{pt}</MenuItem>
                            ))}
                            </Select>
                            {errors.network && <Typography color="error">{errors.network.message}</Typography>}
                          </div>

                          InputProps={{
                          readOnly: formData?.type === 'bank',
                          }}

                          */}

                          <div className="form-block">
                            <Typography mt={3} mb={1} variant="p" className="form-block-title">Phone Number</Typography> 
                            <PhoneInput country={'ug'} value={formData.phone_number} onChange={(phone) => handlePhoneChange("phone_number", phone)} style={{ marginBottom: 1 }} fullWidth className="phone-field"/>
                            {errors.phone_number && <Typography color="error">{errors.phone_number.message}</Typography>}
                          </div>
                      
                      </div> :''}


                      <div className="form-block">
                        <Typography mt={3} mb={1} variant="p" className="form-block-title">Account Name</Typography> 
                        <TextField name="account_name"  label="" 
                                   value={formData.account_name} onChange={handleChange} fullWidth style={{ marginBottom: 1 }} />
                        {errors.account_name && <Typography color="error">{errors.account_name.message}</Typography>}
                       </div>  
                       
                        {(formData?.type === 'bank')?
                            <div>
                            
                            <div className="form-block">
                            <Typography mt={3} mb={1} variant="p" className="form-block-title">Account Number</Typography> 
                            <TextField name="account_number" label="" value={formData.account_number} onChange={handleChange} fullWidth style={{ marginBottom: 1 }} />
                            {errors.account_number && <Typography color="error">{errors.account_number.message}</Typography>}
                            </div>
                   
                            <div className="form-block">
                              <Typography mt={3} mb={1} variant="p" className="form-block-title">Sort Code</Typography> 
                              <TextField name="sort_code" label="" value={formData.sort_code} onChange={handleChange} fullWidth style={{ marginBottom: 1 }} />
                              {errors.sort_code && <Typography color="error">{errors.sort_code.message}</Typography>}
                            </div>

                            <div className="form-block">
                              <Typography mt={3} mb={1} variant="p" className="form-block-title">Swift Code</Typography> 
                              <TextField name="swift_code" label="" value={formData.swift_code} onChange={handleChange} fullWidth style={{ marginBottom: 1 }} />
                              {errors.swift_code && <Typography color="error">{errors.swift_code.message}</Typography>}
                            </div>


                        </div> :''}
                  </DialogContent>

                  <DialogActions>
                    <Button onClick={() => { onClose() }}>Cancel</Button>
                    <Button variant="contained" type="submit" disabled={isSubmitting}>
                        Save
                    </Button>
                  </DialogActions>
            </FormProvider>
        </Dialog>
  );
};

export default NewPaymentMethodPopup;
