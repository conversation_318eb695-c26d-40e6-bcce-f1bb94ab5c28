import { useState, useEffect } from "react";
import * as Yup from "yup";
import {
  <PERSON>ton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Typography,
  TextField,
  Select,
  MenuItem,
  IconButton,
} from "@mui/material";
import { useForm, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { toast } from "react-toastify";

import { getChains, addAddress } from "../../services";
import { get } from "../../api";
import Iconify from "../Iconify";
import { FormProvider } from "../hook-form";

const WalletSchema = Yup.object().shape({
  address: Yup.string().required("Wallet address is required"),
  wallet_name: Yup.string().required("Wallet name is required"),
  chain: Yup.string().required("Select a chain"),
});

const NewAddressPopup = ({ isOpen, onClose, onSuccess, data }) => {
  const [chains, setChains] = useState([]);

  const methods = useForm({
    resolver: yup<PERSON><PERSON>olver(WalletSchema),
    defaultValues: {
      address: "",
      wallet_name: "",
      chain: data?.payCurrency || "", 
    },
  });

  const {
    handleSubmit,
    control,
    reset,
    getValues,
    setValue,
    formState: { errors, isSubmitting },
  } = methods;

  useEffect(() => {
    fetchChains();
  }, []);


  useEffect(() => {
    setValue("chain", data?.payCurrency || "");
  }, [isOpen, data?.payCurrency]);

  

  const fetchChains = async () => {
    try {
      const response = await getChains();
      setChains(response.data || []);
    } catch {
      toast.error("Failed to fetch chains");
    }
  };

  const onSubmit = async (formData) => {
    try {
      const response = await addAddress(formData);
      if ([100, 200].includes(response.status)) {
        toast.success(response.message);
        reset();
        onSuccess();
        onClose();
      } else {
        toast.error(response.message);
      }
    } catch {
      toast.error("Error saving address");
    }
  };

  return (
    <Dialog open={isOpen} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle className="pop-title">
        <strong>New Wallet Address</strong>
        <IconButton onClick={onClose}>
          <Iconify icon="eva:close-outline" color="red" />
        </IconButton>
      </DialogTitle>

      <FormProvider {...methods} onSubmit={handleSubmit(onSubmit)}>
        <DialogContent>
          {/* Chain Selector */}
          <div className="form-block">
            <Typography mt={3} mb={1} className="form-block-title">
              Chain
            </Typography>
            <Controller
                name="chain"
                control={control}
                render={({ field }) => (
                  <Select {...field} fullWidth error={!!errors.chain}>
                    {chains.length > 0 ? (
                      chains.map((chain) => (
                        <MenuItem key={chain.chain_code} value={chain.chain_code}>
                          {chain.chain_code}
                        </MenuItem>
                      ))
                    ) : (
                      <MenuItem disabled>No chains available</MenuItem>
                    )}
                  </Select>
                )}
              />
            {errors.chain && <Typography color="error">{errors.chain.message}</Typography>}
          </div>

          {/* Wallet Name */}
          <div className="form-block">
            <Typography mt={3} mb={1} className="form-block-title">
              Wallet Name
            </Typography>
            <Controller
              name="wallet_name"
              control={control}
              render={({ field }) => (
                <TextField {...field} type="text" fullWidth />
              )}
            />
            {errors.wallet_name && (
              <Typography color="error">{errors.wallet_name.message}</Typography>
            )}
          </div>

          {/* Wallet Address */}
          <div className="form-block">
            <Typography mt={3} mb={1} className="form-block-title">
              Wallet Address
            </Typography>
            <Controller
              name="address"
              control={control}
              render={({ field }) => (
                <TextField {...field} type="text" fullWidth />
              )}
            />
            {errors.address && (
              <Typography color="error">{errors.address.message}</Typography>
            )}
          </div>
        </DialogContent>

        <DialogActions>
          <Button onClick={onClose}>Cancel</Button>
          <Button variant="contained" type="submit" disabled={isSubmitting}>
            Save
          </Button>
        </DialogActions>
      </FormProvider>
    </Dialog>
  );
};

export default NewAddressPopup;
