import { useState, useEffect } from 'react';
import * as Yup from 'yup';
import { useForm, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { But<PERSON>, Container, Stack, Typography, Dialog, DialogTitle, DialogContent, DialogActions, TextField, Select, MenuItem, IconButton } from '@mui/material';
import PhoneInput from 'react-phone-input-2'
// import 'react-phone-input-2/lib/style.css'
import 'react-phone-input-2/lib/material.css'
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';                                                                                         
import { post, get } from '../../api';
import { addAddress, getChains, getAssets, updatedRatesUrl } from '../../services';
import { FormProvider } from '../hook-form';
import Iconify from '../Iconify';


const chainOptions = ['STELLAR', 'CELO', 'TRON'];
const NewProviderExchangeEndPointPopup = ({ isOpen, onClose, onSuccess, data }) => {

    const [open, setOpen] = useState(false);
    const [walletDelete, setWalletDelete] = useState(false);
    const [walletDeleteDetails, setWalletDeleteDetails] = useState({});

  
    const WalletSchema = Yup.object().shape({
        url:   Yup.string().required("Provide exchange end point")
    });
  
    const methods = useForm({
      resolver: yupResolver(WalletSchema),
      defaultValues: {
        url: ""
      },
    });
  
    const {
      handleSubmit,
      control,
      reset,
      formState: { errors, isSubmitting },
    } = methods;
  
    const onSubmit = async (data) => {
      try {
        
          const postObj = await updatedRatesUrl(data);
          if (postObj.status === 200 || postObj.status === 200) {
            toast.success(`${postObj.message}`);
            setOpen(false);
            reset();
            onSuccess();
          } else {
            toast.error(`${postObj.message}`);
          }

      } catch (error) {
         toast.error("Error saving address");
      }
  };

  return (
    <Dialog open={isOpen} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle  className="pop-title">
        <b>Edit Exchange End Point</b>
        <IconButton onClick={() => {onClose()  }}>
          <Iconify icon="eva:close-outline" color="red" />
        </IconButton>
      </DialogTitle>

      <FormProvider {...methods} onSubmit={handleSubmit(onSubmit)}>
        <DialogContent>
           
            <div  className="form-block">
              <Typography mt={3} mb={1} variant="p" className="form-block-title">Exchange Url</Typography> 
              <Controller
                name="url"
                style={{ marginTop: 0 }}
                control={control}
                render={({ field }) => (
                        <TextField
                            {...field}
                            label=""
                            type="text"
                            fullWidth
                        />
                )} />
              {errors.url && <Typography color="error">{errors.url.message}</Typography>}
            </div>      
 
            </DialogContent>

            <DialogActions>
              <Button onClick={() => onClose()}>Cancel</Button>
              <Button variant="contained" type="submit" disabled={isSubmitting}>
                Save
              </Button>
            </DialogActions>
        </FormProvider>
    </Dialog>
  );
};

export default NewProviderExchangeEndPointPopup;
