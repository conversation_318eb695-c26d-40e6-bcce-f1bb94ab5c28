import { useState, useEffect } from 'react';
import * as Yup from 'yup';
import { useF<PERSON>, Controller } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { Button, Container, Stack, Typography, Dialog, DialogTitle, DialogContent, DialogActions, TextField, Select, MenuItem, IconButton } from '@mui/material';
import PhoneInput from 'react-phone-input-2'
// import 'react-phone-input-2/lib/style.css'
import 'react-phone-input-2/lib/material.css'
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';                                                                                         
import { post, get } from '../../api';
import { addAddress, getChains, getAssets, acceptService } from '../../services';
import { FormProvider } from '../hook-form';
import Iconify from '../Iconify';


const NewProviderAcceptedServicePopup = ({ isOpen, onClose, onSuccess, data }) => {
    
    const [services, setServices] = useState([]);
    const [open, setOpen] = useState(false);

    useEffect(() => {
      fetchServices();
    }, []);
    
    const fetchServices = async () => {
            try {
                const response = await get("accounts/services");
                setServices(response || []);
            } catch {
                toast.error("Failed to fetch services");
            }
    };
  

    const WalletSchema = Yup.object().shape({
                service_id: Yup.string().required("service is required"),
            });
  
    const methods = useForm({
      resolver: yupResolver(WalletSchema),
      defaultValues: {
        service_id: '',
      },
    });
  
    const {
      handleSubmit,
      control,
      reset,
      formState: { errors, isSubmitting },
    } = methods;
  
    const onSubmit = async (data) => {
      try {
        
          const postObj = await acceptService(data);
          if (postObj.status === 200 || postObj.status === 200) {
            toast.success(`${postObj.message}`);
            await fetchServices();
            setOpen(false);
            reset();
            onSuccess();
          } else {
            toast.error(`${postObj.message}`);
          }
      } catch (error) {
         toast.error("Error saving service");
      }
  };

  return (
    <Dialog open={isOpen} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle  className="pop-title">
        <b>New wallet address</b>
        <IconButton onClick={() => {onClose()  }}>
          <Iconify icon="eva:close-outline" color="red" />
        </IconButton>
      </DialogTitle>

      <FormProvider {...methods} onSubmit={handleSubmit(onSubmit)}>
        <DialogContent>
           
            <div className="form-block">
              <Typography mt={3} mb={1} variant="p" className="form-block-title"> Service </Typography>   
              <Controller
                name="service_id"
                mb={4} 
                control={control}
                defaultValue={(data?.service_id !== "" && data?.service_id !== undefined)? data?.service_id : ""}
                render={({ field }) => (
                  <Select {...field} fullWidth error={!!errors.service_id}>
                    {services.map((service) => (
                        <MenuItem key={service.id} value={service.service_id}>{service.service_name}</MenuItem>
                     ))}
                  </Select>
                )}
              />
              {errors.service_id && <Typography color="error">{errors.service_id.message}</Typography>}
            </div>  

            </DialogContent>

            <DialogActions>
              <Button onClick={() => onClose()}>Cancel</Button>
              <Button variant="contained" type="submit" disabled={isSubmitting}>
                Save
              </Button>
            </DialogActions>
        </FormProvider>
    </Dialog>
  );
};

export default NewProviderAcceptedServicePopup;
