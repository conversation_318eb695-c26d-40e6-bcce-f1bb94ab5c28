import React, { useState } from 'react';
import { useForm, FormProvider } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as Yup from 'yup';
import {
  Grid,
  Typography,
  Button,
  Paper,
  Box
} from '@mui/material';
import { InfoOutlined } from '@mui/icons-material';
import AmountCurrencyInput from '../pages/AmountCurrencyInput';

// On-Ramp Schema
const onRampSchema = Yup.object().shape({
  fiat_currency: Yup.string().required('Fiat currency is required'),
  crypto_asset: Yup.string().required('Crypto asset is required'),
  fiat_amount: Yup.number().positive().required('Fiat amount is required'),
  crypto_amount: Yup.number().positive().required('Crypto amount is required')
});

const OnRampForm = () => {
  const [isLoading, setIsLoading] = useState(false);

  // On-Ramp Form Methods
  const methods = useForm({
    resolver: yupResolver(onRampSchema),
    defaultValues: {
      fiat_currency: "NGN",
      crypto_asset: "USDT",
      fiat_amount: "35000",
      crypto_amount: "10"
    }
  });

  const {
    handleSubmit,
    watch,
    setValue,
    formState: { errors }
  } = methods;

  const watchedFiatAmount = watch("fiat_amount", "35000");
  const watchedFiatCurrency = watch("fiat_currency", "NGN");
  const watchedCryptoAsset = watch("crypto_asset", "USDT");

  // Mock exchange rate (in production, this would come from an API)
  const exchangeRate = 1650; // 1 USDT = 1650 NGN

  const handleFiatAmountChange = (value) => {
    setValue("fiat_amount", value);
    if (value && exchangeRate) {
      const cryptoValue = (parseFloat(value) / exchangeRate).toFixed(2);
      setValue("crypto_amount", cryptoValue);
    }
  };

  const handleCryptoAmountChange = (value) => {
    setValue("crypto_amount", value);
    if (value && exchangeRate) {
      const fiatValue = (parseFloat(value) * exchangeRate).toFixed(0);
      setValue("fiat_amount", fiatValue);
    }
  };

  const onSubmit = async (data) => {
    setIsLoading(true);
    // This would integrate with on-ramp providers in the future
    console.log('On-Ramp form data:', data);
    setIsLoading(false);
  };

  const fiatCurrencyOptions = [
    { value: 'NGN', label: 'NGN - Nigerian Naira', logo: '/static/flags/ngn.png' },
    { value: 'KES', label: 'KES - Kenyan Shilling', logo: '/static/flags/kes.png' }
  ];

  const cryptoAssetOptions = [
    { value: 'USDT', label: 'USDT - Tether', logo: '/static/flags/usdt.png' },
    { value: 'USDC', label: 'USDC - USD Coin', logo: '/static/flags/usdc.png' }
  ];

  return (
    <FormProvider {...methods}>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <Typography variant="h6" fontWeight={600} sx={{ mb: 1, color: '#1F50C2', textAlign: 'center' }}>
              Buy Crypto with Fiat
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3, textAlign: 'center' }}>
              Convert your local currency to cryptocurrency
            </Typography>
          </Grid>

          {/* Fiat to Crypto Inputs */}
          <Grid item xs={12}>
            <AmountCurrencyInput 
              label="You Pay (Fiat)"
              amountName="fiat_amount"
              currencyName="fiat_currency"
              amountError={errors.fiat_amount}
              currencyError={errors.fiat_currency}
              currencyOptions={fiatCurrencyOptions}
              onAmountChange={handleFiatAmountChange}
            />
          </Grid>

          <Grid item xs={12}>
            <AmountCurrencyInput 
              label="You Receive (Crypto)"
              amountName="crypto_amount"
              currencyName="crypto_asset"
              amountError={errors.crypto_amount}
              currencyError={errors.crypto_asset}
              currencyOptions={cryptoAssetOptions}
              onAmountChange={handleCryptoAmountChange}
            />
          </Grid>

          {/* Exchange Rate Info */}
          <Grid item xs={12}>
            <Paper 
              sx={{ 
                p: 2, 
                bgcolor: '#F0F7FF', 
                border: '1px solid #C8DAF4',
                borderRadius: '8px'
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                <InfoOutlined sx={{ color: '#1F50C2', fontSize: 20 }} />
                <Typography variant="body2" fontWeight={600} color="#1F50C2">
                  Exchange Rate
                </Typography>
              </Box>
              <Typography variant="body2" color="text.secondary">
                1 {watchedCryptoAsset} = {exchangeRate.toLocaleString()} {watchedFiatCurrency}
              </Typography>
            </Paper>
          </Grid>





          {/* Submit Button */}
          <Grid item xs={12}>
            <Button
              type="submit"
              fullWidth
              variant="contained"
              size="medium"
              sx={{
                py: 1.5,
                fontWeight: 600,
                backgroundColor: '#1F50C2',
                borderRadius: '6px',
                mt: 1,
                '&:hover': {
                  backgroundColor: '#1A4AA3',
                }
              }}
            >
              Buy Crypto
            </Button>
          </Grid>
        </Grid>
      </form>
    </FormProvider>
  );
};

export default OnRampForm;
