import React, { useState } from 'react';
import { useForm, FormProvider } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as Yup from 'yup';
import {
  Grid,
  Typography,
  Button,
  Paper,
  Box,
  Alert,
  AlertTitle
} from '@mui/material';
import { InfoOutlined, TrendingUp, Security, Speed } from '@mui/icons-material';
import AmountCurrencyInput from '../pages/AmountCurrencyInput';

// On-Ramp Schema
const onRampSchema = Yup.object().shape({
  fiat_currency: Yup.string().required('Fiat currency is required'),
  crypto_asset: Yup.string().required('Crypto asset is required'),
  fiat_amount: Yup.number().positive().required('Fiat amount is required'),
  crypto_amount: Yup.number().positive().required('Crypto amount is required')
});

const OnRampForm = () => {
  const [isLoading, setIsLoading] = useState(false);

  // On-Ramp Form Methods
  const methods = useForm({
    resolver: yupResolver(onRampSchema),
    defaultValues: {
      fiat_currency: "UGX",
      crypto_asset: "USDT",
      fiat_amount: "35000",
      crypto_amount: "10"
    }
  });

  const {
    handleSubmit,
    watch,
    setValue,
    formState: { errors }
  } = methods;

  const watchedFiatAmount = watch("fiat_amount", "35000");
  const watchedFiatCurrency = watch("fiat_currency", "UGX");
  const watchedCryptoAsset = watch("crypto_asset", "USDT");

  // Mock exchange rate (in production, this would come from an API)
  const exchangeRate = 3500; // 1 USDT = 3500 UGX

  const handleFiatAmountChange = (value) => {
    setValue("fiat_amount", value);
    if (value && exchangeRate) {
      const cryptoValue = (parseFloat(value) / exchangeRate).toFixed(2);
      setValue("crypto_amount", cryptoValue);
    }
  };

  const handleCryptoAmountChange = (value) => {
    setValue("crypto_amount", value);
    if (value && exchangeRate) {
      const fiatValue = (parseFloat(value) * exchangeRate).toFixed(0);
      setValue("fiat_amount", fiatValue);
    }
  };

  const onSubmit = async (data) => {
    setIsLoading(true);
    // This would integrate with on-ramp providers in the future
    console.log('On-Ramp form data:', data);
    setIsLoading(false);
  };

  const fiatCurrencyOptions = [
    { value: 'UGX', label: 'UGX - Ugandan Shilling', logo: '/static/flags/ugx.png' },
    { value: 'KES', label: 'KES - Kenyan Shilling', logo: '/static/flags/kes.png' },
    { value: 'NGN', label: 'NGN - Nigerian Naira', logo: '/static/flags/ngn.png' },
    { value: 'GHS', label: 'GHS - Ghanaian Cedi', logo: '/static/flags/ghs.png' },
    { value: 'ZAR', label: 'ZAR - South African Rand', logo: '/static/flags/zar.png' }
  ];

  const cryptoAssetOptions = [
    { value: 'USDT', label: 'USDT - Tether', logo: '/static/flags/usdt.png' },
    { value: 'USDC', label: 'USDC - USD Coin', logo: '/static/flags/usdc.png' },
    { value: 'BTC', label: 'BTC - Bitcoin', logo: '/static/flags/btc.png' },
    { value: 'ETH', label: 'ETH - Ethereum', logo: '/static/flags/eth.png' }
  ];

  return (
    <FormProvider {...methods}>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <Typography variant="h6" fontWeight={600} sx={{ mb: 1, color: '#1F50C2', textAlign: 'center' }}>
              Buy Crypto with Fiat
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3, textAlign: 'center' }}>
              Convert your local currency to cryptocurrency
            </Typography>
          </Grid>

          {/* Fiat to Crypto Inputs */}
          <Grid item xs={12}>
            <AmountCurrencyInput 
              label="You Pay (Fiat)"
              amountName="fiat_amount"
              currencyName="fiat_currency"
              amountError={errors.fiat_amount}
              currencyError={errors.fiat_currency}
              currencyOptions={fiatCurrencyOptions}
              onAmountChange={handleFiatAmountChange}
            />
          </Grid>

          <Grid item xs={12}>
            <AmountCurrencyInput 
              label="You Receive (Crypto)"
              amountName="crypto_amount"
              currencyName="crypto_asset"
              amountError={errors.crypto_amount}
              currencyError={errors.crypto_asset}
              currencyOptions={cryptoAssetOptions}
              onAmountChange={handleCryptoAmountChange}
            />
          </Grid>

          {/* Exchange Rate Info */}
          <Grid item xs={12}>
            <Paper 
              sx={{ 
                p: 2, 
                bgcolor: '#F0F7FF', 
                border: '1px solid #C8DAF4',
                borderRadius: '8px'
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                <InfoOutlined sx={{ color: '#1F50C2', fontSize: 20 }} />
                <Typography variant="body2" fontWeight={600} color="#1F50C2">
                  Exchange Rate
                </Typography>
              </Box>
              <Typography variant="body2" color="text.secondary">
                1 {watchedCryptoAsset} = {exchangeRate.toLocaleString()} {watchedFiatCurrency}
              </Typography>
            </Paper>
          </Grid>

          {/* Coming Soon Alert */}
          <Grid item xs={12}>
            <Alert 
              severity="info" 
              icon={<TrendingUp />}
              sx={{ 
                borderRadius: '8px',
                '& .MuiAlert-icon': {
                  color: '#1F50C2'
                }
              }}
            >
              <AlertTitle sx={{ fontWeight: 600 }}>Coming Soon</AlertTitle>
              On-Ramp functionality is currently under development. You'll be able to buy crypto with fiat currency soon!
            </Alert>
          </Grid>

          {/* Features Preview */}
          <Grid item xs={12}>
            <Typography variant="subtitle2" fontWeight={600} sx={{ mb: 2, color: '#1F50C2' }}>
              What to expect:
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={4}>
                <Box sx={{ textAlign: 'center', p: 2 }}>
                  <Security sx={{ color: '#1F50C2', fontSize: 32, mb: 1 }} />
                  <Typography variant="body2" fontWeight={600}>Secure</Typography>
                  <Typography variant="caption" color="text.secondary">
                    Bank-grade security
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={4}>
                <Box sx={{ textAlign: 'center', p: 2 }}>
                  <Speed sx={{ color: '#1F50C2', fontSize: 32, mb: 1 }} />
                  <Typography variant="body2" fontWeight={600}>Fast</Typography>
                  <Typography variant="caption" color="text.secondary">
                    Instant processing
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={4}>
                <Box sx={{ textAlign: 'center', p: 2 }}>
                  <TrendingUp sx={{ color: '#1F50C2', fontSize: 32, mb: 1 }} />
                  <Typography variant="body2" fontWeight={600}>Best Rates</Typography>
                  <Typography variant="caption" color="text.secondary">
                    Competitive pricing
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </Grid>

          {/* Disabled Submit Button */}
          <Grid item xs={12}>
            <Button
              type="submit"
              fullWidth
              variant="contained"
              size="medium"
              disabled={true}
              sx={{ 
                py: 1.5,
                fontWeight: 600,
                backgroundColor: '#DFE3E8',
                color: '#637381',
                borderRadius: '6px',
                mt: 1,
                '&:hover': {
                  backgroundColor: '#DFE3E8',
                }
              }}
            >
              Coming Soon
            </Button>
          </Grid>
        </Grid>
      </form>
    </FormProvider>
  );
};

export default OnRampForm;
