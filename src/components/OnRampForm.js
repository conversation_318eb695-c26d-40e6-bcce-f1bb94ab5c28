import React, { useState, useEffect } from 'react';
import { useForm, FormProvider } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as Yup from 'yup';
import {
  Grid,
  Typography,
  Button,
  Box,
  Card,
  Chip,
  CircularProgress
} from '@mui/material';
import AmountCurrencyInput from '../pages/AmountCurrencyInput';
import { post } from '../api';

// ProviderCard component for On-Ramp
const ProviderCard = ({ provider, isSelected, onSelect, sendAsset, receiveCurrency, fee }) => {
  // Format the service name for better display
  const formatServiceName = (service) => {
    if (!service) return 'Service';

    // For known service codes, provide user-friendly names
    if (service.toLowerCase().includes('mobile')) {
      return 'Mobile Money';
    }
    if (service.toLowerCase().includes('bank')) {
      return 'Bank Transfer';
    }

    // Format by removing underscores and capitalizing each word
    return service
      .replace(/_/g, ' ')
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  };

  // Get the provider name or a default if not available
  const providerName = provider.name || `Provider ${provider.provider_id || provider.id || ''}`;

  // Get the formatted service name
  const serviceName = formatServiceName(provider.service_name || provider.service_code);

  return (
    <Card
      onClick={() => onSelect(provider)}
      className={`provider-card ${isSelected ? 'active' : ''}`}
      sx={{
        border: isSelected ? `2px solid #1F50C2` : '1px solid #ddd',
        borderRadius: '8px',
        p: 2,
        cursor: 'pointer',
        height: '100%',
        transition: 'all 0.2s ease',
        '&:hover': {
          borderColor: '#1F50C2',
          transform: 'translateY(-2px)',
          boxShadow: '0 4px 16px rgba(31, 80, 194, 0.12)',
        },
        background: isSelected ? 'rgba(31, 80, 194, 0.05)' : 'white',
        display: 'flex',
        flexDirection: 'column',
        position: 'relative',
      }}
    >
      {/* Provider Name and Type Tag */}
      <Box sx={{ mb: 2, display: 'flex', flexDirection: 'column', alignItems: 'flex-start' }}>
        <Chip
          label={serviceName}
          size="small"
          color={isSelected ? "primary" : "default"}
          sx={{
            borderRadius: '4px',
            fontSize: '0.7rem',
            height: '22px',
            bgcolor: isSelected ? 'rgba(31, 80, 194, 0.15)' : 'rgba(0, 0, 0, 0.08)',
            color: isSelected ? '#1F50C2' : 'text.secondary'
          }}
        />
      </Box>

      {/* Min/Max Amount */}
      <Box sx={{ mb: 1 }}>
        <Typography variant="caption" color="text.secondary" display="block">
          Min: {provider.min_amount || 0} | Max: {provider.max_amount || 'Unlimited'}
        </Typography>
      </Box>

      {/* Selected Indicator */}
      {isSelected && (
        <Box
          sx={{
            position: 'absolute',
            top: -8,
            right: -8,
            bgcolor: '#1F50C2',
            borderRadius: '50%',
            width: 24,
            height: 24,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'white',
            fontSize: '0.8rem',
            fontWeight: 'bold'
          }}
        >
          ✓
        </Box>
      )}

      <Box sx={{ mt: 'auto' }}>
        <Typography variant="body2" fontWeight={500}>
          1 {receiveCurrency} ≈ {provider.rate} {sendAsset}
        </Typography>
        <Typography variant="caption" color="text.secondary" display="block">
          Fee: {fee || 0} {receiveCurrency}
        </Typography>
      </Box>
    </Card>
  );
};

// On-Ramp Schema
const onRampSchema = Yup.object().shape({
  fiat_currency: Yup.string().required('Fiat currency is required'),
  crypto_asset: Yup.string().required('Crypto asset is required'),
  fiat_amount: Yup.number().positive().required('Fiat amount is required'),
  crypto_amount: Yup.number().positive().required('Crypto amount is required')
});

const OnRampForm = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [providers, setProviders] = useState([]);
  const [selectedProvider, setSelectedProvider] = useState(null);
  const [isProvidersLoading, setIsProvidersLoading] = useState(false);
  const [fee, setFee] = useState(0);

  // On-Ramp Form Methods
  const methods = useForm({
    resolver: yupResolver(onRampSchema),
    defaultValues: {
      fiat_currency: "NGN",
      crypto_asset: "USDT",
      fiat_amount: "35000",
      crypto_amount: "10"
    }
  });

  const {
    handleSubmit,
    watch,
    setValue,
    formState: { errors }
  } = methods;

  const watchedFiatAmount = watch("fiat_amount", "35000");
  const watchedFiatCurrency = watch("fiat_currency", "NGN");
  const watchedCryptoAsset = watch("crypto_asset", "USDT");

  // Fetch providers for On-Ramp (reverse of Off-Ramp: fiat to crypto)
  const fetchProviders = async (fiatCurrency, cryptoAsset) => {
    if (!fiatCurrency || !cryptoAsset) return;

    try {
      setIsProvidersLoading(true);
      setSelectedProvider(null);

      // For On-Ramp, we're buying crypto with fiat, so we reverse the parameters
      const params = {
        asset: cryptoAsset,
        asset_code: cryptoAsset,
        currency: fiatCurrency,
        // Add on-ramp specific parameter if needed
        direction: 'buy' // or 'on-ramp'
      };

      console.log("Fetching On-Ramp providers with params:", params);
      const providersData = await post("accounts/provider", params);

      if (Array.isArray(providersData?.data) && providersData?.data.length > 0) {
        setProviders(providersData.data);

        // Auto-select the first provider
        const firstProvider = providersData.data[0];
        setSelectedProvider(firstProvider);

        console.log("On-Ramp providers loaded:", providersData.data.length);
      } else {
        setProviders([]);
        console.log("No On-Ramp providers available for", fiatCurrency, "to", cryptoAsset);
      }
    } catch (error) {
      console.error("Error fetching On-Ramp providers:", error);
      setProviders([]);
    } finally {
      setIsProvidersLoading(false);
    }
  };

  // Handle provider selection
  const handleProviderSelect = (provider) => {
    setSelectedProvider(provider);
    console.log("Selected On-Ramp provider:", provider);
  };

  // Fetch providers when currencies change
  useEffect(() => {
    fetchProviders(watchedFiatCurrency, watchedCryptoAsset);
  }, [watchedFiatCurrency, watchedCryptoAsset]);

  const handleFiatAmountChange = (value) => {
    setValue("fiat_amount", value);
    if (value && selectedProvider?.rate) {
      // For On-Ramp: fiat amount / rate = crypto amount
      const cryptoValue = (parseFloat(value) / selectedProvider.rate).toFixed(6);
      setValue("crypto_amount", cryptoValue);
    }
  };

  const handleCryptoAmountChange = (value) => {
    setValue("crypto_amount", value);
    if (value && selectedProvider?.rate) {
      // For On-Ramp: crypto amount * rate = fiat amount
      const fiatValue = (parseFloat(value) * selectedProvider.rate).toFixed(0);
      setValue("fiat_amount", fiatValue);
    }
  };

  const onSubmit = async (data) => {
    if (!selectedProvider) {
      console.error("No provider selected for On-Ramp");
      return;
    }

    setIsLoading(true);

    // Prepare On-Ramp data with provider information
    const onRampData = {
      ...data,
      provider_id: selectedProvider.provider_id,
      provider_service_id: selectedProvider.provider_service_id,
      provider_name: selectedProvider.name,
      rate: selectedProvider.rate,
      direction: 'buy', // On-Ramp direction
      service_code: selectedProvider.service_code
    };

    console.log('On-Ramp form data with provider:', onRampData);
    console.log('Selected provider:', selectedProvider);

    // This would integrate with on-ramp providers in the future
    // await submitOnRampOrder(onRampData);

    setIsLoading(false);
  };

  const fiatCurrencyOptions = [
    { value: 'NGN', label: 'NGN - Nigerian Naira', logo: '/static/flags/ngn.png' },
    { value: 'KES', label: 'KES - Kenyan Shilling', logo: '/static/flags/kes.png' }
  ];

  const cryptoAssetOptions = [
    { value: 'USDT', label: 'USDT - Tether', logo: '/static/flags/usdt.png' },
    { value: 'USDC', label: 'USDC - USD Coin', logo: '/static/flags/usdc.png' }
  ];

  return (
    <FormProvider {...methods}>
      <form onSubmit={handleSubmit(onSubmit)}>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <Typography variant="h6" fontWeight={600} sx={{ mb: 1, color: '#1F50C2', textAlign: 'center' }}>
              Buy Crypto with Fiat
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3, textAlign: 'center' }}>
              Convert your local currency to cryptocurrency
            </Typography>
          </Grid>

          {/* Fiat to Crypto Inputs */}
          <Grid item xs={12}>
            <AmountCurrencyInput 
              label="You Pay (Fiat)"
              amountName="fiat_amount"
              currencyName="fiat_currency"
              amountError={errors.fiat_amount}
              currencyError={errors.fiat_currency}
              currencyOptions={fiatCurrencyOptions}
              onAmountChange={handleFiatAmountChange}
            />
          </Grid>

          <Grid item xs={12}>
            <AmountCurrencyInput 
              label="You Receive (Crypto)"
              amountName="crypto_amount"
              currencyName="crypto_asset"
              amountError={errors.crypto_amount}
              currencyError={errors.crypto_asset}
              currencyOptions={cryptoAssetOptions}
              onAmountChange={handleCryptoAmountChange}
            />
          </Grid>

          {/* Provider Selection */}
          <Grid item xs={12}>
            <Typography variant="body2" fontWeight={600} color="text.secondary" sx={{ mb: 0.5 }}>
              Select Provider
            </Typography>
            {(() => {
              if (isProvidersLoading) {
                return (
                  <Box sx={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    height: '80px'
                  }}>
                    <CircularProgress size={24} />
                  </Box>
                );
              }

              if (providers.length === 0) {
                return (
                  <Typography variant="body2" color="text.secondary" sx={{ py: 1, textAlign: 'center' }}>
                   No providers available for {watchedFiatCurrency} to {watchedCryptoAsset}
                  </Typography>
                );
              }

              // Display all providers in a grid
              return (
                <Grid container spacing={1}>
                  {providers.map((provider, index) => (
                    <Grid item xs={12} sm={6} key={provider.provider_service_id || index}>
                      <ProviderCard
                        provider={provider}
                        isSelected={selectedProvider && selectedProvider.provider_service_id === provider.provider_service_id}
                        onSelect={handleProviderSelect}
                        sendAsset={watchedCryptoAsset}
                        receiveCurrency={watchedFiatCurrency}
                        fee={fee}
                      />
                    </Grid>
                  ))}
                </Grid>
              );
            })()}
          </Grid>





          {/* Submit Button */}
          <Grid item xs={12}>
            <Button
              type="submit"
              fullWidth
              variant="contained"
              size="medium"
              disabled={!selectedProvider || isLoading}
              sx={{
                py: 1.5,
                fontWeight: 600,
                backgroundColor: '#1F50C2',
                borderRadius: '6px',
                mt: 1,
                '&:hover': {
                  backgroundColor: '#1A4AA3',
                },
                '&:disabled': {
                  backgroundColor: '#DFE3E8',
                  color: '#637381',
                }
              }}
            >
              {!selectedProvider ? 'Select a Provider' : 'Buy Crypto'}
            </Button>
          </Grid>
        </Grid>
      </form>
    </FormProvider>
  );
};

export default OnRampForm;
