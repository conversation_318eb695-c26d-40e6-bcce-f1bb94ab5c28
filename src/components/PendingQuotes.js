import React, { useState, useEffect } from 'react';
import { Trash2 } from 'lucide-react';
import { Button } from './ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from './ui/card';

const QuoteCard = ({ quote, onCancel, cancelStatus }) => {
  const expiryTime = new Date(quote.expiresAt);
  const now = new Date();
  const timeLeft = Math.max(0, Math.floor((expiryTime.getTime() - now.getTime()) / 1000));

  const [remainingSeconds, setRemainingSeconds] = useState(timeLeft);

  useEffect(() => {
    if (remainingSeconds <= 0) return;

    const timer = setInterval(() => {
      setRemainingSeconds(prev => {
        if (prev <= 1) clearInterval(timer);
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [remainingSeconds]);

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs < 10 ? '0' : ''}${secs}`;
  };

  return (
    <Card className="mb-6 rounded-2xl border shadow-md bg-white pending-quote-block">


        <CardHeader className="pb-2 border-b content-pending-quote-title">
          <div className="flex justify-between items-center">
            <CardTitle className="text-lg font-bold text-gray-800">
              <strong>Quote</strong>  <span>#{quote.id}</span>
            </CardTitle>
          </div>
          <CardDescription className="text-sm text-gray-500">
            <strong>Account Number:</strong> <span className="font-medium text-gray-700">{quote.account_number}</span>
          </CardDescription>
        </CardHeader>

        <CardContent className="pt-4">
          <div className="grid grid-cols-2 gap-6 text-sm text-gray-700 content-pending-quote">
            
            <div className="block-pending-quote">
              <div>
                <p className="mb-1 text-gray-500"><strong>You Pay</strong></p>
                <p className="text-base font-semibold">{quote.send_amount} {quote.send_asset}</p>
              </div>

              <div>
                <p className="mb-1 text-gray-500"><strong>You Receive</strong></p>
                <p className="text-base font-semibold">
                  {parseFloat(quote.receive_amount).toFixed(2)} {quote.receive_currency}
                </p>
              </div>  
            </div>

            <div className="block-pending-quote">
              <div>
                <p className="mb-1 text-gray-500"><strong>Rate</strong></p>
                <p className="text-base">1 {quote.send_asset} = {quote.ex_rate} {quote.receive_currency}</p>
              </div>

              <div>
                <p className="mb-1 text-gray-500"><strong>Fee</strong></p>
                <p className="text-base">{quote.fee} {quote.receive_currency}</p>
              </div>
            </div>

          </div>
        </CardContent>

        <CardFooter className="flex justify-end gap-3 pt-4 border-t pending-quote-footer pending-quote-block-inner">
          <Button
            type="button"
            variant="destructive"
            className="flex items-center gap-2 px-4 py-2 text-sm font-medium"
            onClick={onCancel}
          >
             <Trash2 className="h-4 w-4" />
             Cancel Quote
          </Button>
        </CardFooter>

      </Card>

  );
};

const PendingQuotes = ({ quotes, onCancelQuote }) => {
  const [pendingQuotes, setPendingQuotes] = useState(quotes);
  const [cancelStatus, setCancelStatus] = useState("");

  const handleCancelQuote = async (quote) => {
    const result = await onCancelQuote(quote);
  };

  return (
    <div>
      {quotes.length === 0 ? (
        <div>
          <p>
            No pending quotes found. Create a new quote to get started.
          </p>
        </div>
      ) : (
        quotes.map(quote => (
          <QuoteCard
            key={quote.id}
            quote={quote}
            onCancel={() => handleCancelQuote(quote)}
            cancelStatus={cancelStatus}
          />
        ))
      )}
    </div>
  );
};

export default PendingQuotes;
