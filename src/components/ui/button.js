import clsx from "clsx";

export function Button({ children, variant = "primary", size = "md", asChild, className, ...props }) {
  const Component = asChild ? "div" : "button";

  const baseStyles = "inline-flex items-center justify-center rounded-md font-medium transition focus:outline-none";
  const variantStyles = {
    primary: "bg-blue-600 text-white hover:bg-blue-700 focus:ring-2 focus:ring-blue-500",
    secondary: "bg-gray-200 text-blue-700 hover:bg-gray-300 focus:ring-2 focus:ring-gray-400",
    outline: "border border-gray-300 text-gray-700 hover:bg-gray-100 focus:ring-2 focus:ring-gray-300",
    ghost: "text-gray-700 hover:bg-gray-100 focus:ring-2 focus:ring-gray-300",
  };
  const sizeStyles = {
    sm: "px-2 py-1 text-sm",
    md: "px-4 py-2",
    lg: "px-6 py-3 text-lg",
  };

  return (
    <Component
      className={clsx(baseStyles, variantStyles[variant], sizeStyles[size], className)}
      {...props}
    >
      {children}
    </Component>
  );
}
