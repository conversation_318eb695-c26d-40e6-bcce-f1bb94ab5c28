export function NavigationMenu({ children, className, ...props }) {
  return (
    <nav className={`relative ${className}`} {...props}>
      {children}
    </nav>
  );
}

export function NavigationMenuList({ children, className, ...props }) {
  return <ul className={`flex space-x-4 ${className}`} {...props}>{children}</ul>;
}

export function NavigationMenuItem({ children, className, ...props }) {
  return <li className={`relative ${className}`} {...props}>{children}</li>;
}

export function NavigationMenuTrigger({ children, className, ...props }) {
  return (
    <button
      className={`px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-200 rounded ${className}`}
      {...props}
    >
      {children}
    </button>
  );
}

export function NavigationMenuContent({ children, className, ...props }) {
  return <div className={`absolute bg-white shadow-md rounded ${className}`} {...props}>{children}</div>;
}

export function NavigationMenuLink({ asChild, children, className, ...props }) {
  const Component = asChild ? "div" : "a";
  return (
    <Component className={`block text-sm font-medium text-gray-700 ${className}`} {...props}>
      {children}
    </Component>
  );
}
