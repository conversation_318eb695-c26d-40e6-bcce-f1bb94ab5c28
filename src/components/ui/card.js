export function Card({ children, className, ...props }) {
  return (
    <div className={`rounded-lg shadow-md bg-white ${className}`} {...props}>
      {children}
    </div>
  );
}

export function CardContent({ children, className, ...props }) {
  return (
    <div className={`p-4 ${className}`} {...props}>
      {children}
    </div>
  );
}



export function CardTitle({ children, className, ...props }) {
  return (
    <div className={`text-2xl font-semibold leading-none tracking-tight ${className}`} {...props}>
      {children}
    </div>
  );
}

export function CardHeader({ children, className, ...props }) {
  return (
    <div className={`flex flex-col space-y-1.5 p-6  ${className}`} {...props}>
      {children}
    </div>
  );
}

export function CardFooter({ children, className, ...props }) {
  return (
    <div className={`flex items-center p-6 pt-0 ${className}`} {...props}>
      {children}
    </div>
  );
}

export function CardDescription({ children, className, ...props }) {
  return (
    <div className={`text-sm text-muted-foreground ${className}`} {...props}>
      {children}
    </div>
  );
}
