import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Container, Stack, Typography, Grid } from '@mui/material';
import { post, get } from '../api';

const Rates = ({ watchedProvider, watchedPayCurrency, watchedCurrency}) => {
  const [rate, setRate] = useState("");
  const [fee, setFee] = useState("");
  useEffect(() => {
      const handleProviderChange = async () => {
        const searchData = {
          amount: "1",
          provider_id:watchedProvider,
          symbol: watchedPayCurrency,
          currency: watchedCurrency
        };
        const rateData = await post("accounts/getRate", searchData);
        if (rateData.status === 200) {
          setRate(rateData.data.value);
          setFee(rateData.data.fee);
        }
      };
      handleProviderChange();
 }, [watchedPayCurrency, watchedCurrency, watchedProvider]);

return (
    <Grid item xs={12} sx={{ mb: 0, mt: 0, ml: 0 }}>
      <Typography variant="body2" className="clearfix space-btn-text"><b>Rate:</b> <span>1 {watchedPayCurrency} = {rate ?? ""} {watchedCurrency}</span></Typography>
      <Typography variant="body2" className="clearfix space-btn-text"><b>Fee:</b>  <span>{fee ?? ""} {watchedCurrency}</span></Typography>
   </Grid>
  );
};

export default Rates;
