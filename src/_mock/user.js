import { faker } from '@faker-js/faker';
import { sample } from 'lodash';

// ----------------------------------------------------------------------

const users = [...Array(0)].map((_, index) => ({
  id: faker.datatype.uuid(),
  avatarUrl: `/static/mock-images/avatars/avatar_${index + 1}.jpg`,
  name: faker.name.findName(),
  company: faker.company.companyName(),
  isVerified: sample([
    '50',
    '14',
    '90',
    '50',
    '30',
    '18',
    '20',
    '50',
    '100',
    '200',
  ]),
  status: sample(['active', 'banned']),
  role: sample([
    '50',
    '14',
    '90',
    '50',
    '30',
    '18',
    '20',
    '50',
    '100',
    '200',
  ]),
}));

export default users;
