{"name": "liquidityrail-ui", "author": "muda", "licence": "MIT", "version": "1.5.0", "private": false, "scripts": {"start": "set PORT=3300 && react-scripts start ", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint --ext .js,.jsx ./src", "lint:fix": "eslint --fix --ext .js,.jsx ./src", "clear-all": "rm -rf build node_modules", "re-start": "rm -rf build node_modules && yarn install && yarn start", "re-build": "rm -rf build node_modules && yarn install && yarn build"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "babel": {"presets": ["@babel/preset-react"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "dependencies": {"@emotion/react": "^11.9.3", "@emotion/styled": "^11.9.3", "@faker-js/faker": "^7.3.0", "@hookform/resolvers": "^2.9.3", "@iconify/react": "^3.2.2", "@mui/icons-material": "^5.15.15", "@mui/lab": "^5.0.0-alpha.88", "@mui/material": "^5.8.6", "@mui/x-data-grid": "^6.19.6", "@testing-library/jest-dom": "^5.16.4", "apexcharts": "^3.35.3", "axios": "^1.6.8", "change-case": "^4.1.2", "class-variance-authority": "^0.7.1", "date-fns": "^2.28.0", "history": "^5.3.0", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "lucide-react": "^0.483.0", "numeral": "^2.0.6", "prop-types": "^15.8.1", "qrcode.react": "^3.1.0", "react": "^18.2.0", "react-alert": "^7.0.3", "react-apexcharts": "^1.4.0", "react-dom": "^18.2.0", "react-helmet-async": "^1.3.0", "react-hook-form": "^7.33.1", "react-phone-input-2": "^2.15.1", "react-router-dom": "^6.3.0", "react-scripts": "^5.0.1", "react-toastify": "^10.0.5", "react-top-loading-bar": "^3.0.2", "simplebar": "^5.3.8", "simplebar-react": "^2.4.1", "sweetalert-react": "^0.4.11", "web-vitals": "^2.1.4", "yup": "^0.32.11"}, "devDependencies": {"@babel/core": "^7.18.6", "@babel/eslint-parser": "^7.18.2", "@svgr/webpack": "^6.2.1", "eslint": "^8.19.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^8.5.0", "eslint-config-react-app": "^7.0.1", "eslint-plugin-flowtype": "^8.0.3", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.6.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.30.1", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^2.7.1"}, "overrides": {"@svgr/webpack": "^6.2.1"}}