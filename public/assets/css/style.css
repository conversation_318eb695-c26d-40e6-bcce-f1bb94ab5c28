@import url("https://fonts.googleapis.com/css2?family=Archivo:wght@400;500&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap");

body {
       background-color: #FAFAFB !important;
       margin: 0;
       line-height: normal;
}

.fullWidth{
  width: 100% !important;
}

.main-model-block{
  margin: 0 auto !important;
  width:  38%;
  border-radius: 4px;
  background: #ffffff;
  margin-top: 20px !important;
  padding: 15px 20px;
  max-height: 95vh;
}


@media(max-width: 760px){
    .main-model-block{
      margin: 0 auto !important;
      width:  65%;
      border-radius: 4px;
      background: #ffffff;
      padding: 15px 20px;
    }
}

.item-form-top-101{
  margin-top: 15px !important;
}


.hidden{
  display: none;
  visibility: hidden;
}


.default-tab{
  color: #000000;
  padding: 5px 20px !important;
  margin-right: 5px !important;
  display: inline-block !important;
}

.active-tab{

  color: #2065D1 !important; 
  background: #c8daf4 !important;
  padding: 5px 20px !important;
  margin-right: 5px !important;
  display: inline-block !important;
}

.tabing-section{
  padding-bottom: 15px;
}


.model-container-blocking{

  max-height:    70vh;
  overflow:      hidden;
  overflow-y:    auto;
  padding:       0 15px;
}


.text-center{
  text-align: center !important;
  text-transform: uppercase;
}


.full-width{
  width: 100% !important;

}

.mt-3{
  margin-top: 8px !important;
}

.mt-25{
  margin-top: 25px !important; 
}